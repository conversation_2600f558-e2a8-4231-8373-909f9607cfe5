---
import Layout from "../layouts/Layout.astro";
import { SITE } from "../config";
---

<Layout 
  pageTitle="Search | PVB"
  isHomePage={false}
  accentColor="#2a2a2a"
  bgColor="rgba(245, 245, 245, 0.9)"
  backgroundImageUrl="/images/whitemarble.png"
  bodyDataPage="search"
>
  <div class="quote-container" style="max-width: 800px;">
    <h1 class="text-3xl font-bold mb-4 text-accent">Search</h1>
    <p class="mb-8 text-muted">Find posts by title, content, or tags</p>
    
    <div class="search-container">
      <div id="search-box" class="mb-8">
        <!-- This div will be replaced with the search UI -->
        <div class="search-placeholder">
          <p class="text-muted">Search functionality will be available after building the site.</p>
          <p class="search-note">For local development, you need to build the site first with <code>npm run build</code></p>
        </div>
      </div>
      
      <div id="search-results">
        <!-- Results will appear here -->
      </div>
    </div>
    
    <div class="mt-10">
      <a 
        href="/blog" 
        class="back-link"
      >
        <span class="back-arrow">←</span>
        <span>Back to blog</span>
      </a>
    </div>
  </div>
</Layout>

<style>
  .quote-container {
    margin: 0 auto;
  }
  h1 {
    font-family: 'Georgia Custom', Georgia, serif; 
  }
  .text-muted {
    color: var(--color-text-secondary);
  }
  .text-accent {
    color: var(--color-accent);
  }
  .search-container {
    margin: 2rem 0;
  }
  .search-placeholder {
    padding: 2rem;
    border: 1px solid rgba(var(--color-accent-rgb, 58, 44, 35), 0.2);
    border-radius: 0.5rem;
    text-align: center;
  }
  .search-note {
    margin-top: 1rem;
    font-size: 0.875rem;
  }
  .search-note code {
    background-color: rgba(0, 0, 0, 0.05);
    padding: 0.2rem 0.5rem;
    border-radius: 0.25rem;
    font-family: monospace;
  }
  .back-link {
    display: inline-flex;
    align-items: center;
    color: var(--color-accent);
    text-decoration: none;
    transition: opacity 0.2s ease;
    font-family: 'Georgia Custom', Georgia, serif;
  }
  .back-link:hover {
    opacity: 0.75;
  }
  .back-arrow {
    margin-right: 0.5rem;
  }
</style>

<script>
  // This script will be replaced with actual search functionality
  // We'll use pagefind for search once dependencies are installed
  document.addEventListener('DOMContentLoaded', function() {
    console.log('Search page loaded. Actual search functionality will be available after build.');
  });
</script>
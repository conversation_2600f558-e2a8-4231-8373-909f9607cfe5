import { a as createComponent, m as maybeRenderHead, u as unescapeHTML, r as renderTemplate } from './astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import 'clsx';

const html = "<h2 id=\"project-overview\">Project Overview</h2>\n<p>Resonance is a framework for creating sophisticated spatial audio experiences that respond dynamically to virtual environments. The system enables sound to behave with physical accuracy in three-dimensional space, creating immersive audio landscapes that complement visual elements in VR, AR, and traditional interfaces.</p>\n<h2 id=\"key-features\">Key Features</h2>\n<ul>\n<li><strong>Physically-Based Sound Propagation</strong>: Simulates how sound waves travel through and interact with virtual environments.</li>\n<li><strong>Spatial Positioning</strong>: Places audio sources in 3D space with accurate distance attenuation and directional properties.</li>\n<li><strong>Material-Based Acoustics</strong>: Models how different materials absorb, reflect, and diffract sound waves.</li>\n<li><strong>Room Acoustics</strong>: Simulates reverberations, echoes, and other acoustic properties based on environment geometry.</li>\n<li><strong>Dynamic Adaptation</strong>: Audio characteristics change in real-time as users or sound sources move through space.</li>\n<li><strong>Performance Optimization</strong>: Uses a multi-level approach to balance computational complexity with audio fidelity.</li>\n</ul>\n<h2 id=\"current-implementation\">Current Implementation</h2>\n<p>The framework is built on the Web Audio API and integrates with Three.js for spatial coordination. It employs a hybrid approach combining physical models for close-range interactions and statistically-based models for distant or complex interactions.</p>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto; white-space: pre-wrap; word-wrap: break-word;\" tabindex=\"0\" data-language=\"javascript\"><code><span class=\"line\"><span style=\"color:#6A737D\">// Example of material-based acoustic modeling</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">class</span><span style=\"color:#B392F0\"> AcousticMaterial</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">  constructor</span><span style=\"color:#E1E4E8\">({</span></span>\n<span class=\"line\"><span style=\"color:#FFAB70\">    absorption</span><span style=\"color:#F97583\"> =</span><span style=\"color:#E1E4E8\"> { low: </span><span style=\"color:#79B8FF\">0.1</span><span style=\"color:#E1E4E8\">, mid: </span><span style=\"color:#79B8FF\">0.2</span><span style=\"color:#E1E4E8\">, high: </span><span style=\"color:#79B8FF\">0.3</span><span style=\"color:#E1E4E8\"> },</span></span>\n<span class=\"line\"><span style=\"color:#FFAB70\">    scattering</span><span style=\"color:#F97583\"> =</span><span style=\"color:#79B8FF\"> 0.1</span><span style=\"color:#E1E4E8\">,</span></span>\n<span class=\"line\"><span style=\"color:#FFAB70\">    transmission</span><span style=\"color:#F97583\"> =</span><span style=\"color:#79B8FF\"> 0.05</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  } </span><span style=\"color:#F97583\">=</span><span style=\"color:#E1E4E8\"> {}) {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">    this</span><span style=\"color:#E1E4E8\">.absorption </span><span style=\"color:#F97583\">=</span><span style=\"color:#E1E4E8\"> absorption;</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">    this</span><span style=\"color:#E1E4E8\">.scattering </span><span style=\"color:#F97583\">=</span><span style=\"color:#E1E4E8\"> scattering;</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">    this</span><span style=\"color:#E1E4E8\">.transmission </span><span style=\"color:#F97583\">=</span><span style=\"color:#E1E4E8\"> transmission;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  }</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  </span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">  // Calculate frequency-dependent reflection coefficient</span></span>\n<span class=\"line\"><span style=\"color:#B392F0\">  getReflectionCoefficient</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#FFAB70\">frequency</span><span style=\"color:#E1E4E8\">) {</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    let</span><span style=\"color:#E1E4E8\"> band;</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    if</span><span style=\"color:#E1E4E8\"> (frequency </span><span style=\"color:#F97583\">&#x3C;</span><span style=\"color:#79B8FF\"> 250</span><span style=\"color:#E1E4E8\">) band </span><span style=\"color:#F97583\">=</span><span style=\"color:#9ECBFF\"> 'low'</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    else</span><span style=\"color:#F97583\"> if</span><span style=\"color:#E1E4E8\"> (frequency </span><span style=\"color:#F97583\">&#x3C;</span><span style=\"color:#79B8FF\"> 2000</span><span style=\"color:#E1E4E8\">) band </span><span style=\"color:#F97583\">=</span><span style=\"color:#9ECBFF\"> 'mid'</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    else</span><span style=\"color:#E1E4E8\"> band </span><span style=\"color:#F97583\">=</span><span style=\"color:#9ECBFF\"> 'high'</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    </span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    return</span><span style=\"color:#79B8FF\"> 1</span><span style=\"color:#F97583\"> -</span><span style=\"color:#79B8FF\"> this</span><span style=\"color:#E1E4E8\">.absorption[band];</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  }</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  </span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">  // Apply material properties to an incident sound ray</span></span>\n<span class=\"line\"><span style=\"color:#B392F0\">  processIncidentRay</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#FFAB70\">ray</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#FFAB70\">intersection</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#FFAB70\">frequency</span><span style=\"color:#E1E4E8\">) {</span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">    // Calculate reflected energy</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    const</span><span style=\"color:#79B8FF\"> reflectionCoeff</span><span style=\"color:#F97583\"> =</span><span style=\"color:#79B8FF\"> this</span><span style=\"color:#E1E4E8\">.</span><span style=\"color:#B392F0\">getReflectionCoefficient</span><span style=\"color:#E1E4E8\">(frequency);</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    const</span><span style=\"color:#79B8FF\"> reflectedEnergy</span><span style=\"color:#F97583\"> =</span><span style=\"color:#E1E4E8\"> ray.energy </span><span style=\"color:#F97583\">*</span><span style=\"color:#E1E4E8\"> reflectionCoeff </span><span style=\"color:#F97583\">*</span><span style=\"color:#E1E4E8\"> (</span><span style=\"color:#79B8FF\">1</span><span style=\"color:#F97583\"> -</span><span style=\"color:#79B8FF\"> this</span><span style=\"color:#E1E4E8\">.scattering);</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    </span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">    // Calculate scattered energy</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    const</span><span style=\"color:#79B8FF\"> scatteredEnergy</span><span style=\"color:#F97583\"> =</span><span style=\"color:#E1E4E8\"> ray.energy </span><span style=\"color:#F97583\">*</span><span style=\"color:#E1E4E8\"> reflectionCoeff </span><span style=\"color:#F97583\">*</span><span style=\"color:#79B8FF\"> this</span><span style=\"color:#E1E4E8\">.scattering;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    </span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">    // Calculate transmitted energy</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    const</span><span style=\"color:#79B8FF\"> transmittedEnergy</span><span style=\"color:#F97583\"> =</span><span style=\"color:#E1E4E8\"> ray.energy </span><span style=\"color:#F97583\">*</span><span style=\"color:#79B8FF\"> this</span><span style=\"color:#E1E4E8\">.transmission;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    </span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    return</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">      reflectedRay: {</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">        ...</span><span style=\"color:#E1E4E8\">ray,</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">        direction: </span><span style=\"color:#B392F0\">calculateReflectionVector</span><span style=\"color:#E1E4E8\">(ray.direction, intersection.normal),</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">        energy: reflectedEnergy</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">      },</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">      scatteredRays: </span><span style=\"color:#B392F0\">generateScatteredRays</span><span style=\"color:#E1E4E8\">(intersection, scatteredEnergy),</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">      transmittedRay: {</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">        ...</span><span style=\"color:#E1E4E8\">ray,</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">        direction: ray.direction, </span><span style=\"color:#6A737D\">// Simplified; should account for refraction</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">        energy: transmittedEnergy</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">      }</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    };</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  }</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span>\n<span class=\"line\"></span></code></pre>\n<h2 id=\"current-status\">Current Status</h2>\n<p>The core audio processing engine and spatial positioning system are complete. Current development is focused on improving the material acoustics system, optimizing performance for complex environments, and creating a more intuitive API for developers.</p>\n<h2 id=\"future-directions\">Future Directions</h2>\n<p>Planned developments include:</p>\n<ul>\n<li>Integration with popular game engines and AR frameworks</li>\n<li>Support for ambisonics and object-based audio formats</li>\n<li>Machine learning acceleration for acoustic modeling</li>\n<li>Collaborative audio environments for shared spatial experiences</li>\n<li>Tools for non-technical creators to design spatial soundscapes</li>\n</ul>";

				const frontmatter = {"title":"Resonance: Spatial Audio Framework","projectDate":"2024-03-20T00:00:00.000Z","status":"In Progress","featured":true,"tags":["Audio","WebAudio API","Spatial Computing","JavaScript","Three.js"],"ogImage":"/images/resonance-preview.png","description":"A framework for creating immersive spatial audio experiences that respond to virtual environments, user movement, and interactive elements.","repoUrl":"https://github.com/username/resonance-audio","liveUrl":"https://resonance-audio-demo.com"};
				const file = "C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-five.md";
				const url = undefined;
				function rawContent() {
					return "\n## Project Overview\n\nResonance is a framework for creating sophisticated spatial audio experiences that respond dynamically to virtual environments. The system enables sound to behave with physical accuracy in three-dimensional space, creating immersive audio landscapes that complement visual elements in VR, AR, and traditional interfaces.\n\n## Key Features\n\n- **Physically-Based Sound Propagation**: Simulates how sound waves travel through and interact with virtual environments.\n- **Spatial Positioning**: Places audio sources in 3D space with accurate distance attenuation and directional properties.\n- **Material-Based Acoustics**: Models how different materials absorb, reflect, and diffract sound waves.\n- **Room Acoustics**: Simulates reverberations, echoes, and other acoustic properties based on environment geometry.\n- **Dynamic Adaptation**: Audio characteristics change in real-time as users or sound sources move through space.\n- **Performance Optimization**: Uses a multi-level approach to balance computational complexity with audio fidelity.\n\n## Current Implementation\n\nThe framework is built on the Web Audio API and integrates with Three.js for spatial coordination. It employs a hybrid approach combining physical models for close-range interactions and statistically-based models for distant or complex interactions.\n\n```javascript\n// Example of material-based acoustic modeling\nclass AcousticMaterial {\n  constructor({\n    absorption = { low: 0.1, mid: 0.2, high: 0.3 },\n    scattering = 0.1,\n    transmission = 0.05\n  } = {}) {\n    this.absorption = absorption;\n    this.scattering = scattering;\n    this.transmission = transmission;\n  }\n  \n  // Calculate frequency-dependent reflection coefficient\n  getReflectionCoefficient(frequency) {\n    let band;\n    if (frequency < 250) band = 'low';\n    else if (frequency < 2000) band = 'mid';\n    else band = 'high';\n    \n    return 1 - this.absorption[band];\n  }\n  \n  // Apply material properties to an incident sound ray\n  processIncidentRay(ray, intersection, frequency) {\n    // Calculate reflected energy\n    const reflectionCoeff = this.getReflectionCoefficient(frequency);\n    const reflectedEnergy = ray.energy * reflectionCoeff * (1 - this.scattering);\n    \n    // Calculate scattered energy\n    const scatteredEnergy = ray.energy * reflectionCoeff * this.scattering;\n    \n    // Calculate transmitted energy\n    const transmittedEnergy = ray.energy * this.transmission;\n    \n    return {\n      reflectedRay: {\n        ...ray,\n        direction: calculateReflectionVector(ray.direction, intersection.normal),\n        energy: reflectedEnergy\n      },\n      scatteredRays: generateScatteredRays(intersection, scatteredEnergy),\n      transmittedRay: {\n        ...ray,\n        direction: ray.direction, // Simplified; should account for refraction\n        energy: transmittedEnergy\n      }\n    };\n  }\n}\n```\n\n## Current Status\n\nThe core audio processing engine and spatial positioning system are complete. Current development is focused on improving the material acoustics system, optimizing performance for complex environments, and creating a more intuitive API for developers.\n\n## Future Directions\n\nPlanned developments include:\n- Integration with popular game engines and AR frameworks\n- Support for ambisonics and object-based audio formats\n- Machine learning acceleration for acoustic modeling\n- Collaborative audio environments for shared spatial experiences\n- Tools for non-technical creators to design spatial soundscapes";
				}
				function compiledContent() {
					return html;
				}
				function getHeadings() {
					return [{"depth":2,"slug":"project-overview","text":"Project Overview"},{"depth":2,"slug":"key-features","text":"Key Features"},{"depth":2,"slug":"current-implementation","text":"Current Implementation"},{"depth":2,"slug":"current-status","text":"Current Status"},{"depth":2,"slug":"future-directions","text":"Future Directions"}];
				}

				const Content = createComponent((result, _props, slots) => {
					const { layout, ...content } = frontmatter;
					content.file = file;
					content.url = url;

					return renderTemplate`${maybeRenderHead()}${unescapeHTML(html)}`;
				});

export { Content, compiledContent, Content as default, file, frontmatter, getHeadings, rawContent, url };

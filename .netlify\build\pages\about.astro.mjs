import { a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../chunks/astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import { $ as $$Layout } from '../chunks/Layout_rXbp99fE.mjs';
/* empty css                                 */
export { renderers } from '../renderers.mjs';

var __freeze = Object.freeze;
var __defProp = Object.defineProperty;
var __template = (cooked, raw) => __freeze(__defProp(cooked, "raw", { value: __freeze(cooked.slice()) }));
var _a;
const $$About = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "pageTitle": "About | PVB", "isHomePage": false, "accentColor": "#3a2c23", "bgColor": "#3a2c23", "backgroundImageUrl": "/images/about.png", "bodyDataPage": "about", "data-astro-cid-kh7btl4r": true }, { "default": ($$result2) => renderTemplate(_a || (_a = __template([" ", `<div class="about-header" data-astro-cid-kh7btl4r> <div class="about-title" data-astro-cid-kh7btl4r>about</div> </div> <div class="content-container" data-astro-cid-kh7btl4r> <section class="about-section intro-section" data-astro-cid-kh7btl4r> <p data-astro-cid-kh7btl4r>This is the main introductory text about me. Keep it concise and engaging. Briefly touch upon what drives you or the purpose of this site.</p> <p data-astro-cid-kh7btl4r>This demonstrates how the components can be reused with different properties to change the appearance and behavior.</p> </section> <section class="about-section detail-section" data-astro-cid-kh7btl4r> <h2 data-astro-cid-kh7btl4r>My Journey</h2> <p data-astro-cid-kh7btl4r>Expand on your background, key experiences, and the path that led you here. Use storytelling elements if appropriate.</p> </section> <section class="about-section detail-section" data-astro-cid-kh7btl4r> <h2 data-astro-cid-kh7btl4r>Skills & Expertise</h2> <p data-astro-cid-kh7btl4r>List or describe your core skills, tools you master, and areas you specialize in.</p> <ul data-astro-cid-kh7btl4r> <li data-astro-cid-kh7btl4r>Skill/Area 1: Brief description.</li> <li data-astro-cid-kh7btl4r>Skill/Area 2: Brief description.</li> <li data-astro-cid-kh7btl4r>Skill/Area 3: Brief description.</li> </ul> </section> <section class="about-section detail-section" data-astro-cid-kh7btl4r> <h2 data-astro-cid-kh7btl4r>Philosophy</h2> <p data-astro-cid-kh7btl4r>Discuss your approach to work, design principles, or core values that guide you.</p> </section> </div> <script>
      // Set up back button event
      document.addEventListener('DOMContentLoaded', function() {
        const backButton = document.querySelector('.nav-circle.top-left');
        if (backButton) {
          backButton.addEventListener('click', () => {
            window.history.back();
          });
        }
      });
    <\/script> `])), maybeRenderHead()) })} `;
}, "C:/Users/<USER>/Desktop/pvb-astro/src/pages/about.astro", void 0);

const $$file = "C:/Users/<USER>/Desktop/pvb-astro/src/pages/about.astro";
const $$url = "/about";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
    __proto__: null,
    default: $$About,
    file: $$file,
    url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

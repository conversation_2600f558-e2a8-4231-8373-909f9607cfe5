export const links = [
  {
    category: "research",
    items: [
      {
        name: "ORCID",
        url: "https://orcid.org/0009-0001-2718-0254",
        description: "academic publication identifier",
        logo: "/images/logos/orcid.svg"
      },
      {
        name: "MURST Initiative",
        url: "https://murst.org/",
        description: "advancing recursive intelligence research",
        logo: "/images/logos/murst-logo.png"
      },
      {
        name: "Zenodo",
        url: "https://zenodo.org/communities/murst/records",
        description: "research publication archive",
        logo: "/images/logos/zenodo.svg"
      }
    ]
  },
  {
    category: "social",
    items: [
      {
        name: "LinkedIn",
        url: "https://www.linkedin.com/in/pruthvi-bhat-/",
        description: "professional profile",
        logo: "/images/logos/linkedin.png"
      },
      {
        name: "X",
        url: "https://x.com/pvibhat",
        description: "twitter/x profile",
        logo: "/images/logos/x.svg"
      },
      {
        name: "Bluesky",
        url: "https://bsky.app/profile/pvbhat.bsky.social",
        description: "bluesky profile",
        logo: "/images/logos/bluesky.svg"
      },
      {
        name: "LessWrong",
        url: "https://www.lesswrong.com/users/pruthvi-bhat",
        description: "rationality community",
        logo: "/images/logos/lesswrong.svg"
      }
    ]
  },
  {
    category: "projects",
    items: [
      {
        name: "Medium",
        url: "https://pruthvibhat.medium.com/",
        description: "essays and articles",
        logo: "/images/logos/medium.png"
      },
      {
        name: "GitHub",
        url: "https://github.com/PV-Bhat",
        description: "code repositories",
        logo: "/images/logos/github.png"
      },
      {
        name: "Hevy",
        url: "https://hevy.com/user/approxfit",
        description: "fitness tracking",
        logo: "/images/logos/hevy.png"
      }
    ]
  }
];
            >
              <span class="year-dot"></span>
              <span class="year-label">{image.year}</span>
            </button>
          ))}
        </div>
      </div>
    </section>
    
    <hr class="section-divider">
    
    <!-- Stats Component - Key Metrics -->
    <section class="stats-section" data-animate="fade-in">
      <div class="section-header">
        <div class="section-line"></div>
        <h2 class="section-title">PERFORMANCE</h2>
        <div class="section-line"></div>
      </div>
      
      <div class="stats-container">
        <div class="stats-column records-column">
          <h3 class="stats-heading">PERSONAL RECORDS</h3>
          <div class="records-list">
            {performanceData.personalRecords.map(record => (
              <div class="record-item">
                <div class="record-exercise">{record.exercise}</div>
                <div class="record-weight">{record.weight}</div>
                <div class="record-note">{record.note}</div>
              </div>
            ))}
          </div>
        </div>
        
        <div class="stats-column metrics-column">
          <h3 class="stats-heading">TRAINING VOLUME</h3>
          <div class="metrics-grid">
            {performanceData.statistics.map(stat => (
              <div class="metric-item">
                <div class="metric-value">{stat.value}</div>
                <div class="metric-label">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
    
    <hr class="section-divider">

    <!-- Philosophy Section -->
    <section class="philosophy-section" data-animate="fade-in">
      <div class="section-header">
        <div class="section-line"></div>
        <h2 class="section-title">PHILOSOPHY</h2>
        <div class="section-line"></div>
      </div>

      <p class="intro-text">
        Since 2017, I've approached physical training as an empirical practice—a deliberate application 
        of progressive overload principles documented through extensive data collection. The transformation 
        above represents not sporadic effort but systematic application of principles.
      </p>
      
      <div class="philosophy-content">
        {philosophySections.map(section => (
          <div class="philosophy-item">
            <h3 class="philosophy-title">{section.title}</h3>
            <p class="philosophy-text">{section.content}</p>
          </div>
        ))}
      </div>
    </section>
    
    <hr class="section-divider">

    <!-- Training System Section -->
    <section class="routines-section" data-animate="fade-in">
      <div class="section-header">
        <div class="section-line"></div>
        <h2 class="section-title">METHODOLOGY</h2>
        <div class="section-line"></div>
      </div>
      
      <div class="routines-content">
        <p class="routines-intro">
          My current training protocol follows a four-day rotation targeting specific movement patterns 
          and muscle groups. Each protocol is meticulously documented and progressively adapted based 
          on performance data.
        </p>
        
        <div class="routines-grid">
          {workoutRoutines.map(routine => (
            <a href={routine.href} class="routine-link" target="_blank" rel="noopener noreferrer">
              <div class="routine-name">{routine.name}</div>
              <div class="routine-arrow">→</div>
            </a>
          ))}
        </div>
        
        <div class="hevy-link-container">
          <a href="https://hevy.com/user/approxfit" class="hevy-link" target="_blank" rel="noopener noreferrer">
            <span class="hevy-link-text">VIEW COMPLETE TRAINING ARCHIVE</span>
            <span class="hevy-link-arrow">→</span>
          </a>
        </div>
      </div>
    </section>
  </div>
</Layout>

<script>
  // Parallax effect for marble background
  document.addEventListener('DOMContentLoaded', function() {
    window.addEventListener('scroll', function() {
      const scrollPosition = window.scrollY;
      document.body.style.backgroundPosition = `center ${scrollPosition * 0.02}px`;
    });
    
    // Intersection Observer for fade-in animations
    const animatedSections = document.querySelectorAll('[data-animate="fade-in"]');
    
    // Create observer
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('visible');
          observer.unobserve(entry.target); // Stop observing once it's visible
        }
      });
    }, {
      root: null,
      rootMargin: '0px',
      threshold: 0.15
    });
    
    // Observe all sections with fade-in animation
    animatedSections.forEach(section => {
      observer.observe(section);
    });
    
    // Gallery transformation functionality
    const transformationImages = document.querySelectorAll('.transformation-img');
    const yearMarkers = document.querySelectorAll('.year-marker');
    const prevYearBtn = document.getElementById('prev-year');
    const nextYearBtn = document.getElementById('next-year');
    const currentYearDisplay = document.getElementById('current-year');
    
    if (!transformationImages.length || !yearMarkers.length) return;
    
    let currentIndex = 0;
    const totalImages = transformationImages.length;
    
    // Function to update the active image
    function updateActiveImage(newIndex) {
      // Check bounds
      if (newIndex < 0) newIndex = 0;
      if (newIndex >= totalImages) newIndex = totalImages - 1;
      
      // Update active index
      currentIndex = newIndex;
      
      // Update image visibility
      transformationImages.forEach((img, index) => {
        if (index === currentIndex) {
          img.classList.add('active');
          img.style.opacity = 1;
        } else {
          img.classList.remove('active');
          img.style.opacity = 0;
        }
      });
      
      // Update year markers
      yearMarkers.forEach((marker, index) => {
        marker.classList.toggle('active', index === currentIndex);
      });
      
      // Update current year display
      if (currentYearDisplay) {
        currentYearDisplay.textContent = transformationImages[currentIndex].dataset.year;
      }
    }
    
    // Add click event to year markers
    yearMarkers.forEach((marker, index) => {
      marker.addEventListener('click', () => {
        updateActiveImage(index);
      });
    });
    
    // Add click events to prev/next buttons
    if (prevYearBtn) {
      prevYearBtn.addEventListener('click', () => {
        updateActiveImage(currentIndex - 1);
      });
    }
    
    if (nextYearBtn) {
      nextYearBtn.addEventListener('click', () => {
        updateActiveImage(currentIndex + 1);
      });
    }
    
    // Touch swipe functionality for mobile
    let touchStartX = 0;
    let touchEndX = 0;
    
    const currentImageContainer = document.querySelector('.current-image');
    if (currentImageContainer) {
      currentImageContainer.addEventListener('touchstart', (e) => {
        touchStartX = e.changedTouches[0].screenX;
      });
      
      currentImageContainer.addEventListener('touchend', (e) => {
        touchEndX = e.changedTouches[0].screenX;
        handleSwipe();
      });
    }
    
    function handleSwipe() {
      const swipeThreshold = 50; // Minimum distance to register as swipe
      
      if (touchEndX < touchStartX - swipeThreshold) {
        // Swipe left - next image
        updateActiveImage(currentIndex + 1);
      } else if (touchEndX > touchStartX + swipeThreshold) {
        // Swipe right - previous image
        updateActiveImage(currentIndex - 1);
      }
    }
    
    // Auto-rotate images every 5 seconds if no interaction
    let autoRotateTimer;
    
    function startAutoRotate() {
      autoRotateTimer = setInterval(() => {
        let nextIndex = (currentIndex + 1) % totalImages;
        updateActiveImage(nextIndex);
      }, 5000);
    }
    
    function stopAutoRotate() {
      clearInterval(autoRotateTimer);
    }
    
    // Start auto-rotation
    startAutoRotate();
    
    // Pause on hover/interaction
    const galleryContainer = document.querySelector('.transformation-gallery');
    if (galleryContainer) {
      galleryContainer.addEventListener('mouseenter', stopAutoRotate);
      galleryContainer.addEventListener('touchstart', stopAutoRotate);
      galleryContainer.addEventListener('mouseleave', startAutoRotate);
      galleryContainer.addEventListener('touchend', startAutoRotate);
    }
  });
</script>

<style>
  /* === Typography & Font Setup === */
  @import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@600;700&family=Crimson+Pro:wght@400;500&display=swap');
  
  /* Page Fundamentals */
  :global(body[data-page="fitness"]) {
    overflow-y: auto;
    overflow-x: hidden;
    height: auto;
    min-height: 100vh;
    background-attachment: fixed; /* For parallax effect */
    background-size: cover; /* Ensure background covers the whole viewport */
    color: #1a1a1a;
  }
  
  /* All headings engraved effect */
  h1, h2, h3, h4 {
    font-family: 'Cinzel', serif;
    font-weight: 700;
    letter-spacing: 0.05em;
    text-shadow: 0 1px 1px rgba(0,0,0,0.25);
    color: #1a1a1a;
    margin-bottom: 2.5rem;
  }
  
  /* Body text */
  p, .metric-label, .year-label, .record-note {
    font-family: 'Crimson Pro', serif;
    font-weight: 400;
    color: rgba(0,0,0,0.85);
    line-height: 1.4;
    letter-spacing: -0.01em;
  }
  
  /* Section dividers */
  .section-divider {
    border: none;
    height: 1px;
    background: rgba(0,0,0,0.15);
    margin: 3rem 0;
  }
  
  /* Fade-in animation for sections */
  .transformation-section,
  .stats-section,
  .philosophy-section,
  .routines-section {
    opacity: 0;
    transform: translateY(15px);
    transition: opacity 1s ease, transform 1s ease;
    animation: fadeIn 1s forwards;
  }
  
  @keyframes fadeIn {
    0% {
      opacity: 0;
      transform: translateY(15px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* When section becomes visible */
  .transformation-section.visible,
  .stats-section.visible,
  .philosophy-section.visible,
  .routines-section.visible {
    opacity: 1;
    transform: translateY(0);
  }
  
  /* === Header Section === */
  .fitness-header {
    width: 100%;
    text-align: center;
    margin: 55px 0 40px;
    position: relative;
  }

  .fitness-title {
    font-size: 2.2rem;
    display: inline-block;
    margin: 0;
    text-transform: uppercase;
  }
  
  /* Subtle underline with engraved appearance */
  .fitness-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 2px;
    background-color: rgba(0, 0, 0, 0.4);
  }

  /* === Content Container === */
  .content-container {
    max-width: 800px;
    margin: 0 auto 80px;
    padding: 0 25px;
  }
  
  /* === Section Headers === */
  .section-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin: 50px 0 35px;
  }

  .section-line {
    flex-grow: 1;
    height: 1px;
    background-color: rgba(0, 0, 0, 0.2);
    transition: background-color 0.4s cubic-bezier(0.25, 0.1, 0.25, 1);
  }
  
  .section-title {
    font-size: 1.1rem;
    padding: 0 0.8rem;
    margin: 0;
    text-transform: uppercase;
  }
  
  /* === Transformation Gallery === */
  .transformation-gallery {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 auto;
    max-width: 500px;
  }
  
  .current-image-container {
    position: relative;
    width: 100%;
    margin-bottom: 20px;
  }
  
  .image-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding: 0 5px;
  }
  
  .control-button {
    background: rgba(255, 255, 255, 0.5);
    border: 1px solid rgba(0, 0, 0, 0.15);
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0px 2px 4px rgba(0,0,0,0.15);
  }
  
  .control-button:hover {
    background-color: rgba(255, 255, 255, 0.7);
    border-color: rgba(0, 0, 0, 0.25);
  }
  
  .control-arrow {
    color: rgba(0, 0, 0, 0.7);
    font-size: 0.9rem;
  }
  
  .year-display {
    font-family: 'Cinzel', serif;
    font-size: 1.2rem;
    font-weight: 600;
    color: #1a1a1a;
    letter-spacing: 0.05em;
  }
  
  .current-image {
    position: relative;
    width: 100%;
    height: 480px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    overflow: hidden;
  }
  
  .transformation-img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    opacity: 0;
    transition: opacity 0.8s ease;
  }
  
  .transformation-img.active {
    opacity: 1;
  }
  
  .year-timeline {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-top: 20px;
    position: relative;
  }
  
  /* Timeline connecting line */
  .year-timeline::before {
    content: '';
    position: absolute;
    top: 10px;
    left: 0;
    right: 0;
    height: 1px;
    background-color: rgba(0, 0, 0, 0.15);
    z-index: 0;
  }
  
  .year-marker {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    position: relative;
    z-index: 1;
    transition: transform 0.3s ease;
  }
  
  .year-marker:hover {
    transform: translateY(-2px);
  }
  
  .year-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.3);
    margin-bottom: 7px;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.0);
  }
  
  .year-marker.active .year-dot {
    background-color: #000000;
    width: 12px;
    height: 12px;
    border: 1px solid rgba(0, 0, 0, 0.1);
  }
  
  .year-marker:hover .year-dot {
    background-color: rgba(0, 0, 0, 0.6);
  }
  
  .year-label {
    font-family: 'Cinzel', serif;
    font-size: 0.8rem;
    color: rgba(0, 0, 0, 0.6);
    transition: color 0.3s ease;
    letter-spacing: 0.03em;
  }
  
  .year-marker.active .year-label {
    font-weight: 600;
    color: #000000;
  }
  
  .year-marker:hover .year-label {
    color: rgba(0, 0, 0, 0.85);
  }
  
  /* === Stats Component === */
  .stats-container {
    display: flex;
    flex-direction: column;
    gap: 40px;
  }
  
  .stats-column {
    width: 100%;
  }
  
  .stats-heading {
    font-size: 1.2rem;
    margin: 0 0 25px;
    text-align: center;
    letter-spacing: 0.05em;
    text-transform: uppercase;
  }
  
  /* Personal Records */
  .records-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }
  
  .record-item {
    display: flex;
    align-items: baseline;
    justify-content: space-between;
    padding: 0.6rem 1rem;
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.5);
    box-shadow: 0px 2px 4px rgba(0,0,0,0.15);
  }
  
  .record-exercise {
    font-family: 'Cinzel', serif;
    font-size: 1rem;
    font-weight: 600;
    color: #1a1a1a;
    letter-spacing: 0.03em;
  }
  
  .record-weight {
    font-family: 'Cinzel', serif;
    font-size: 1.05rem;
    font-weight: 700;
    color: #1a1a1a;
    text-align: right;
    letter-spacing: 0.03em;
  }
  
  .record-note {
    font-size: 0.8rem;
    color: rgba(0, 0, 0, 0.6);
    margin-top: 3px;
  }
  
  /* Training Metrics */
  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
  
  .metric-item {
    text-align: center;
    padding: 0.6rem 1rem;
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.5);
    box-shadow: 0px 2px 4px rgba(0,0,0,0.15);
    transition: transform 0.3s ease;
  }
  
  .metric-item:hover {
    transform: translateY(-3px);
  }
  
  .metric-value {
    font-family: 'Cinzel', serif;
    font-size: 1.3rem;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 7px;
    letter-spacing: 0.03em;
  }
  
  .metric-label {
    font-size: 0.9rem;
    color: rgba(0, 0, 0, 0.7);
  }
  
  /* === Philosophy Section === */
  .intro-text {
    font-size: 1.15rem;
    line-height: 1.4;
    color: rgba(0, 0, 0, 0.85);
    margin-bottom: 40px;
  }
  
  .philosophy-content {
    display: flex;
    flex-direction: column;
    gap: 35px;
  }
  
  .philosophy-item {
    padding: 0.6rem 1rem;
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.5);
    box-shadow: 0px 2px 4px rgba(0,0,0,0.15);
  }
  
  .philosophy-title {
    font-size: 1.2rem;
    margin: 0 0 15px;
    color: #1a1a1a;
    letter-spacing: 0.05em;
  }
  
  .philosophy-text {
    font-size: 1rem;
    line-height: 1.4;
    color: rgba(0, 0, 0, 0.8);
    margin: 0;
  }
  
  /* === Routines Section === */
  .routines-intro {
    font-size: 1.05rem;
    line-height: 1.4;
    color: rgba(0, 0, 0, 0.8);
    margin-bottom: 35px;
  }
  
  .routines-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-bottom: 40px;
  }
  
  .routine-link {
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-decoration: none;
    padding: 0.6rem 1rem;
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.5);
    box-shadow: 0px 2px 4px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
  }
  
  .routine-link:hover {
    background-color: rgba(255, 255, 255, 0.7);
    transform: translateY(-3px);
    box-shadow: 0px 4px 8px rgba(0,0,0,0.2);
  }
  
  .routine-name {
    font-family: 'Cinzel', serif;
    font-size: 1rem;
    font-weight: 600;
    color: #1a1a1a;
    letter-spacing: 0.03em;
  }
  
  .routine-arrow {
    font-size: 1rem;
    color: rgba(0, 0, 0, 0.5);
    transition: transform 0.3s ease;
  }
  
  .routine-link:hover .routine-arrow {
    transform: translateX(3px);
    color: #1a1a1a;
  }
  
  /* === Hevy Link === */
  .hevy-link-container {
    text-align: center;
    margin-top: 30px;
  }
  
  .hevy-link {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
    padding: 0.6rem 1.5rem;
    background-color: rgba(0, 0, 0, 0.85);
    color: #ffffff;
    font-family: 'Cinzel', serif;
    font-weight: 600;
    letter-spacing: 0.05em;
    border-radius: 15px;
    transition: all 0.3s ease;
    box-shadow: 0px 2px 4px rgba(0,0,0,0.15);
  }
  
  .hevy-link:hover {
    background-color: #000000;
    transform: translateY(-3px);
    box-shadow: 0px 4px 12px rgba(0,0,0,0.25);
  }
  
  .hevy-link-text {
    font-size: 0.9rem;
  }
  
  .hevy-link-arrow {
    font-size: 0.9rem;
    transition: transform 0.3s ease;
  }
  
  .hevy-link:hover .hevy-link-arrow {
    transform: translateX(3px);
  }
  
  /* === Mobile-first responsive styles === */
  @media (max-width: 480px) {
    .fitness-header {
      margin: 45px 0 30px;
    }
    
    .fitness-title {
      font-size: 1.6rem;
    }
    
    .section-title {
      font-size: 0.9rem;
    }
    
    .current-image {
      height: 400px;
    }
    
    .year-timeline {
      padding: 0 10px;
    }
    
    .year-dot {
      width: 6px;
      height: 6px;
    }
    
    .year-marker.active .year-dot {
      width: 10px;
      height: 10px;
    }
    
    .year-label {
      font-size: 0.7rem;
    }
    
    .metrics-grid {
      grid-template-columns: 1fr;
      gap: 15px;
    }
    
    .routines-grid {
      grid-template-columns: 1fr;
    }
  }
  
  /* === Tablet responsive styles === */
  @media (min-width: 481px) and (max-width: 768px) {
    .content-container {
      max-width: 90%;
    }
  }
  
  /* === Larger screens === */
  @media (min-width: 769px) {
    .stats-container {
      flex-direction: row;
      gap: 40px;
    }
    
    .stats-column {
      width: calc(50% - 20px);
    }
  }
</style>
</file>

<file path="astro.config.mjs">
import { defineConfig } from 'astro/config';
import { SITE } from './src/config';

import react from '@astrojs/react';

// https://astro.build/config
export default defineConfig({
  // This should match your actual Netlify domain, not pvb.com
  site: 'https://pvb-astro.netlify.app',

  // This ensures all paths are relative to the root
  base: '/',

  output: 'static',

  markdown: {
    shikiConfig: {
      theme: 'github-dark',
      wrap: true
    },
  },

  experimental: {
    contentCollectionCache: true,
  },

  vite: {
    optimizeDeps: {
      exclude: ['@astrojs/mdx']
    }
  },

  integrations: [react()]
});
</file>

<file path="src/pages/blog/tag/[tag].astro">
---
import { getPostsByTag } from '../../../utils/unifiedContent';
import Layout from '../../../layouts/Layout.astro';
import BlogPostCard from '../../../components/BlogPostCard.astro';
import { getAllUniqueTags, slugifyStr } from '../../../utils/unifiedContent';

export async function getStaticPaths() {
  const allTags = await getAllUniqueTags();
  const paths = allTags.map(tag => ({
    params: { tag: slugifyStr(tag) },
    props: { tagName: tag },
  }));
  return paths;
}

const { tag } = Astro.params;
const { tagName } = Astro.props;

const posts = await getPostsByTag(tag);

const pageTitle = `Tag: ${tagName} | Blog | PVB`;
---

<Layout
  pageTitle={pageTitle}
  isHomePage={false}
  accentColor="#f0f0f0"
  bgColor="rgba(10, 10, 10, 0.94)"
  backgroundImageUrl="/images/blackgranite.png"
  bodyDataPage="blog"
>
  <div class="blog-header">
    <div class="blog-title">blog</div>
  </div>

  <div class="page-container">
    <div class="blog-sidebar">
      {/* Sidebar content like search, archives, subscribe, and tags filter */}
      {/* This could potentially be a reusable component */}
      <div class="search-container sidebar-section">
        <a href="/search" class="search-link sidebar-link">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="11" cy="11" r="8"></circle>
            <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
          </svg>
          <span>search</span>
        </a>
      </div>

      <div class="archive-container sidebar-section">
        <a href="/blog/archives" class="archive-link sidebar-link">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M3 3v18h18"></path>
            <path d="M7 17l4-4 4 4 4-4"></path>
            <path d="M7 11l4-4 4 4 4-4"></path>
          </svg>
          <span>archives</span>
        </a>
      </div>

      <div class="subscribe-container sidebar-section">
        <a href="#" class="subscribe-link sidebar-link">subscribe by email</a>
      </div>

      {/* Tags Filter (Collapsible) - Consider making this a component */}
      <div class="tags-container">
        <button class="tags-toggle sidebar-link" id="tags-toggle" aria-expanded="false" aria-controls="tags-list">
          <span class="tags-title">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M9 5H2v7l6.29 6.29c.94.94 2.48.94 3.42 0l7.58-7.58c.94-.94.94-2.48 0-3.42L13 2c-.94-.94-2.48-.94-3.42 0L9 5Z"></path>
              <path d="M6 9.01V9"></path>
            </svg>
            <span>tags</span>
          </span>
          <span class="toggle-icon">
            <span class="line hor"></span>
            <span class="line vert"></span>
          </span>
        </button>
        <div class="tags-list" id="tags-list">
          <a href="/tags" class="tag-link all-tags-link">all tags</a>
          {/* This part needs to fetch all tags */}
          {/* For now, placeholder or fetch all tags here */}
          {/* Example: */}
          {/* {allTags.map(tag => <a href={`/blog/tag/${slugifyStr(tag)}`} class="tag-link">{tag}</a>)} */}
        </div>
      </div>
    </div>

    <div class="blog-content">
      <h1 class="section-title">Posts tagged with "{tagName}"</h1>
      {posts && posts.length > 0 ? (
        <ul id="blog-post-list">
          {posts.map(post => (
            <li class="post-list-item">
              <BlogPostCard post={post} />
            </li>
          ))}
        </ul>
      ) : (
        <p>No posts found with this tag.</p>
      )}
    </div>
  </div>
</Layout>

<style>
  /* Add or import necessary styles */
  /* You might want to reuse styles from blog.astro */
</style>

<script is:inline>
  // Add necessary JS for sidebar toggle if not a component
</script>
</file>

<file path="src/pages/work.astro">
---
import { getPostsByType } from "../utils/unifiedContent";
import Layout from "../layouts/Layout.astro";
import ProjectCard from "../components/ProjectCard.astro";
import publications from "../data/publications.json"; // Import publications data

// Initialize with defaults in case of errors
let sortedProjects = [];
let featuredProjects = [];
let regularProjects = [];
let projectError = null; // Variable to hold specific error message

try {
  // Fetch all projects except archived ones
  // Get 'work' type content
  const workPosts = await getPostsByType('work');

  // Sort by date (newest first)
  sortedProjects = workPosts.sort(
    (a, b) => {
      const dateA = new Date(b.data?.pubDatetime || b.published_at);
      const dateB = new Date(a.data?.pubDatetime || a.published_at);
      return dateA.valueOf() - dateB.valueOf();
    }
  );

  // Extract featured projects
  featuredProjects = sortedProjects.filter(project => project.data?.featured || project.featured);
  regularProjects = sortedProjects.filter(project => !(project.data?.featured || project.featured));
} catch (error) {
  console.error("Error loading work projects:", error);
  projectError = "Failed to load projects. Please try again later."; // More specific user-facing message
  // In case of error, arrays will remain empty and the page will render with just static content
}

---

<Layout
  title="Work & Research | PVB"
  isHomePage={false}
  accentColor="#f0f0f0"
  bgColor="rgba(0, 0, 0, 0.88)"
  backgroundImageUrl="/images/obsidian.png"
  bodyDataPage="work"
>
  <!-- Work page header with elegant, minimal styling -->
  <div class="work-header">
    <h1 class="work-title">work</h1>
  </div>

  <div class="content-container">
    <!-- Featured Projects Section -->
    {featuredProjects.length > 0 && (
      <section class="featured-projects-section">
        {featuredProjects.map(project => (
          <ProjectCard project={project} variant="featured" />
        ))}
      </section>
    )}

    <!-- Projects Section with refined, elegant styling -->
    {regularProjects.length > 0 && (
      <section class="projects-section">
        <div class="section-header">
          <div class="section-line"></div>
          <h2 class="section-title">Projects</h2>
          <div class="section-line"></div>
        </div>

        <div class="projects-grid">
          {regularProjects.map(project => (
            <ProjectCard project={project} variant="standard" />
          ))}
        </div>
      </section>
    )}

    <!-- Show a message if no projects are found -->
    {sortedProjects.length === 0 && projectError && (
      <div class="projects-error">
        <p>{projectError}</p>
      </div>
    )}
    {sortedProjects.length === 0 && !projectError && (
      <div class="projects-error">
        <p>No projects found.</p>
      </div>
    )}

    <!-- Publications Section with refined styling -->
    <section class="publications-section">
      <div class="section-header">
        <div class="section-line"></div>
        <h2 class="section-title">Research & Publications</h2>
        <div class="section-line"></div>
      </div>

      <ul class="publications-list">
        {publications.map(pub => (
          <li class="publication-item">
            <h3 class="pub-title">{pub.title}</h3>
            <div class="pub-meta">
              <p class="pub-details">{pub.journal}, {pub.year}</p>
              <div class="pub-links">
                {pub.pdfUrl && (
                  <a href={pub.pdfUrl} target="_blank" rel="noopener noreferrer" class="pub-link" aria-label={`Read PDF: ${pub.title}`}>
                    PDF <span class="arrow">↗</span>
                  </a>
                )}
                {pub.url && pub.url !== "#" && (
                  <a href={pub.url} target="_blank" rel="noopener noreferrer" class="pub-link" aria-label={`View project: ${pub.title}`}>
                    Link <span class="arrow">↗</span>
                  </a>
                )}
              </div>
            </div>
          </li>
        ))}
      </ul>
    </section>
  </div>
</Layout>

<style>
  /* Header styling - refined minimal approach */
  .work-header {
    width: 100%;
    text-align: center;
    padding: 5rem 0 4rem;
    position: relative;
  }

  .work-title {
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 1.05rem; /* Adjusted for consistency with homepage quote text */
    font-weight: normal;
    color: rgba(240, 240, 240, 0.95);
    position: relative;
    letter-spacing: -0.01em;
    display: inline-block;
    margin: 0;
  }

  /* Add subtle underline to work title for visual consistency with other pages */
  .work-title::after {
    content: '';
    position: absolute;
    bottom: -0.6rem;
    left: 50%;
    transform: translateX(-50%);
    width: 2.5rem;
    height: 1px;
    background-color: rgba(240, 240, 240, 0.3);
    transition: width var(--transition-duration) var(--easing-standard);
  }

  .work-title:hover::after {
    width: 3.5rem;
    background-color: rgba(240, 240, 240, 0.4);
  }

  /* Main container - ensure proper alignment and reasonable width */
  .content-container {
    width: 100%;
    max-width: 54rem; /* 864px */
    margin: 0 auto;
    padding: 0 1.5rem 6rem;
    color: #f0f0f0;
  }

  /* Error message for when projects can't be loaded */
  .projects-error {
    text-align: center;
    padding: 2rem;
    margin: 2rem 0;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
    color: rgba(240, 240, 240, 0.8);
  }

  /* Ensure proper scrolling behavior */
  :global(body[data-page="work"]) {
    overflow-y: auto;
    overflow-x: hidden;
    height: auto;
    min-height: 100vh;
  }

  /* Featured Projects Section */
  .featured-projects-section {
    margin-bottom: 4rem;
  }

  /* Projects grid for regular projects */
  .projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(21rem, 1fr)); /* ~336px */
    gap: 3rem 2.5rem;
    margin-bottom: 4rem;
    margin-top: 2rem;
  }

  /* Section header - elegant and refined styling */
  .section-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin: 4rem 0 2rem;
  }

  .section-line {
    flex-grow: 1;
    height: 1px;
    background-color: rgba(255, 255, 255, 0.08);
    transition: background-color var(--transition-duration) var(--easing-standard);
  }

  .section-header:hover .section-line {
    background-color: rgba(255, 255, 255, 0.12);
  }

  .section-title {
    font-size: 1rem;
    font-weight: normal;
    color: rgba(255, 255, 255, 0.7);
    white-space: nowrap;
    letter-spacing: 0.01em;
    padding: 0 0.3rem;
    transition: color var(--transition-duration) var(--easing-standard);
    margin: 0;
  }

  .section-header:hover .section-title {
    color: rgba(255, 255, 255, 0.85);
  }

  /* Publications styling - refined with better spacing and animations */
  .publications-list {
    list-style: none;
    padding: 0;
    margin: 2rem 0 0;
  }

  .publication-item {
    margin-bottom: 2.5rem;
    padding-bottom: 2.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.06);
    transition: transform var(--transition-duration) var(--easing-standard);
  }

  .publication-item:hover {
    transform: translateY(-3px);
  }

  .publication-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
  }

  .pub-title {
    font-size: 1.05rem; /* Adjusted for consistency with homepage quote text */
    line-height: 1.4;
    margin: 0 0 0.8rem;
    font-weight: normal;
    color: rgba(255, 255, 255, 0.85);
    font-family: 'Georgia Custom', Georgia, serif;
  }

  .pub-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.8rem;
  }

  .pub-links {
    display: flex;
    gap: 0.8rem;
  }

  .pub-details {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.5);
    margin: 0;
  }

  .pub-link {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.6); /* Adjusted for better visibility */
    text-decoration: none;
    transition: all var(--transition-duration) var(--easing-standard);
    display: flex;
    align-items: center;
    gap: 0.2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    padding-bottom: 1px;
  }

  .pub-link .arrow {
    font-size: 0.7rem;
    transition: transform var(--transition-duration) var(--easing-standard);
  }

  .pub-link:hover {
    color: rgba(255, 255, 255, 1);
    border-bottom-color: rgba(255, 255, 255, 0.6);
  }

  .pub-link:hover .arrow {
    transform: translateX(0.125rem) translateY(-0.125rem);
  }

  /* Responsive adjustments with refined breakpoints */
  @media (max-width: 64rem) { /* 1024px */
    .content-container {
      max-width: 90%;
    }

    .projects-grid {
      grid-template-columns: repeat(auto-fill, minmax(18rem, 1fr)); /* ~288px */
      gap: 2.5rem 2rem;
    }
  }

  @media (max-width: 48rem) { /* 768px */
    .work-header {
      padding: 4.5rem 0 3rem;
    }

    .work-title {
      font-size: 1.05rem; /* Adjusted for consistency with homepage quote text */
    }

    .content-container {
      padding: 0 1.25rem 4.5rem;
    }

    .projects-grid {
      grid-template-columns: 1fr;
      gap: 2.5rem;
    }

    .section-header {
      margin: 3.5rem 0 1.5rem;
    }

    .publication-item {
      margin-bottom: 2rem;
      padding-bottom: 2rem;
    }
  }

  @media (max-width: 30rem) { /* 480px */
    .work-header {
      padding: 3.5rem 0 2.5rem;
    }

    .content-container {
      padding: 0 1rem 3.5rem;
    }

    .pub-meta {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.6rem;
    }

    .pub-details {
      font-size: 0.75rem;
    }
  }
</style>
</file>

<file path="src/layouts/BlogPost.astro">
---
import type { CollectionEntry } from "astro:content";
import { SITE } from "../config";
import Layout from "./Layout.astro";
import Tag from "../components/Tag.astro";
import { slugifyStr } from "../utils/slugify";
import { Image } from '@astrojs/image/components'; // Import Image component
import '../styles/prose.css'; // Import prose styles

export interface Props {
  post: any; // Accept any post object
}

const { post } = Astro.props;
// Normalize entry for both Astro content and raw JSON
const raw = post.data || post;
const { title, author = SITE.author, description, ogImage, tags = [], feature_image, feature_image_alt } = raw;
// Determine publication date
const pubDatetime = raw.pubDatetime ? new Date(raw.pubDatetime) : new Date(raw.published_at);
const modDatetime = raw.modDatetime ? new Date(raw.modDatetime) : null;
// Render HTML: use HTML from JSON only
const contentHtml = raw.html || '';

const datetime = pubDatetime.toISOString();
const postDate = pubDatetime.toLocaleDateString("en-US", {
  day: "numeric",
  month: "long",
  year: "numeric",
});

const ogUrl = ogImage ? ogImage : SITE.ogImage;

// Determine if this is a blog post or work project
const isWorkProject = (raw.tags && raw.tags.some(t => (typeof t === 'object' ? t.slug === 'work' || t.name === 'work' : t === 'work'))) || post.collection === 'work';
const pageType = isWorkProject ? 'work' : 'blog';
const backLabel = pageType; // always lower-case
---

<Layout
  title={title}
  description={description}
  ogImage={ogImage}
  canonicalURL={Astro.url}
  publishedDate={pubDatetime.toISOString()}
  isHomePage={false}
  accentColor="#f0f0f0"
  bgColor="rgba(0, 0, 0, 0.85)"
  backgroundImageUrl={isWorkProject ? "/images/obsidian.png" : "/images/blackgranite.png"}
  bodyDataPage={isWorkProject ? "work-post" : "blog-post"}
>
  <!-- Header title - Dynamic based on content type -->
  <div class="blog-header">
    <div class="blog-title">{pageType}</div>
  </div>

  <!-- DO NOT add a back button here - the Navigation component handles it -->

  <!-- Table of Contents Toggle Button -->
  <button id="toc-toggle" class="toc-toggle" aria-expanded="false" aria-controls="toc-panel">
    <span class="toc-icon-dots">
      <span class="dot"></span>
      <span class="dot"></span>
      <span class="dot"></span>
    </span>
  </button>
  <!-- Table of Contents Panel -->
  <div id="toc-panel" class="toc-panel" aria-hidden="true">
    <div class="toc-panel-inner">
      <h3 class="toc-panel-title">Table of Contents</h3>
      <button class="toc-close" id="toc-close" aria-label="Close Table of Contents">
        <span class="toc-close-arrow">›</span>
      </button>
      <div id="toc-content" class="toc-content">
        <!-- The TOC content will be populated via JavaScript -->
      </div>
    </div>
  </div>

  <!-- Content -->
  <article class="blog-post">
    <header class="post-header">
      <h1 class="post-title" transition:name={slugifyStr(title)}>
        {title}
      </h1>
      <div class="post-date">{postDate}</div>
      {feature_image && (
        <Image
          src={feature_image}
          alt={feature_image_alt || 'Feature Image'}
          width={1200}
          height={600}
          format="webp"
          quality={80}
          class="feature-image"
        />
      )}
    </header>

    <div class="post-content prose" set:html={raw.html}></div>

    {post.relatedPosts && post.relatedPosts.length > 0 && (
      <div class="related-posts">
        <h3 class="related-title">You might also enjoy</h3>
        <div class="related-grid">
          {post.relatedPosts.map(relatedPost => (
            <div class="related-post">
              <h4 class="related-post-title">
                <a href={`/blog/${relatedPost.slug}`}>{relatedPost.data.title}</a>
              </h4>
              <div class="related-post-date">
                {new Date(relatedPost.data.pubDatetime).toLocaleDateString('en-US', {day: 'numeric', month: 'short', year: 'numeric'})}
              </div>
            </div>
          ))}
        </div>
      </div>
    )}

    <div class="post-footer">
      <div class="post-tags">
        {tags.map(tag => <Tag tag={slugifyStr(tag)} tagName={tag} />)}
      </div>

      <div class="post-actions">
        <a href={`/${backLabel}`} class="return-link">← {backLabel}</a>
        <a href="#" class="subscribe-link">subscribe by email</a>
      </div>
    </div>
  </article>

  <script is:inline>
    document.addEventListener('DOMContentLoaded', function() {
      const tocToggle = document.getElementById('toc-toggle');
      const tocPanel = document.getElementById('toc-panel');
      const tocContent = document.getElementById('toc-content');
      const tocClose = document.getElementById('toc-close');
      const blogPost = document.querySelector('.blog-post');
      const isWorkPost = document.body.getAttribute('data-page') === 'work-post';

      // Set return link text based on page type
      const returnLink = document.querySelector('.return-link');
      if (returnLink && isWorkPost) {
        returnLink.textContent = '← Work';
        returnLink.setAttribute('href', '/work');
      }

      // Prevent automatic scrolling to bottom
      window.history.scrollRestoration = 'manual';

      // Generate TOC from headings in the post
      function generateToc() {
        const headings = document.querySelectorAll('.post-content h2, .post-content h3, .post-content h4');
        if (headings.length === 0) {
          tocContent.innerHTML = '<p class="toc-empty">No sections found</p>';
          // Close TOC if no sections
          tocToggle.classList.remove('active');
          tocToggle.setAttribute('aria-expanded', 'false');
          tocPanel.classList.remove('active');
          tocPanel.setAttribute('aria-hidden', 'true');
          return;
        }

        const tocHtml = document.createElement('div');
        tocHtml.classList.add('toc-list-container');

        headings.forEach((heading, index) => {
          // Add an ID to the heading if it doesn't have one
          if (!heading.id) {
            heading.id = `heading-${index}`;
          }

          const item = document.createElement('div');
          item.classList.add(`toc-item`);
          item.classList.add(`toc-${heading.tagName.toLowerCase()}`);

          const a = document.createElement('a');
          a.href = `#${heading.id}`;
          a.textContent = heading.textContent;
          a.style.color = '#ffffff'; // Force white color
          a.style.textDecoration = 'none'; // Force no underline

          a.addEventListener('click', function(e) {
            e.preventDefault();

            // Remove active class from all TOC links
            document.querySelectorAll('.toc-content a').forEach(link => {
              link.classList.remove('active');
            });

            // Add active class to clicked link
            a.classList.add('active');

            // Smooth scroll to the heading
            heading.scrollIntoView({ behavior: 'smooth' });

            // Add a highlight effect to the heading
            heading.classList.add('highlight');
            setTimeout(() => {
              heading.classList.remove('highlight');
            }, 1500);

            // On mobile, close the TOC after clicking
            if (window.innerWidth < 768) {
              toggleToc(false);
            }
          });

          item.appendChild(a);
          tocHtml.appendChild(item);
        });

        tocContent.innerHTML = '';
        tocContent.appendChild(tocHtml);

        // Initial styling of all links
        document.querySelectorAll('.toc-content a').forEach(link => {
          link.style.color = '#ffffff';
          link.style.textDecoration = 'none';
        });

        // Highlight the current section on scroll
        window.addEventListener('scroll', highlightCurrentSection);

        // Initial highlight
        setTimeout(highlightCurrentSection, 100);
      }

      // Highlight the current section in the TOC
      function highlightCurrentSection() {
        const headings = document.querySelectorAll('.post-content h2, .post-content h3, .post-content h4');
        if (!headings.length) return;

        // Find the heading that's currently in view
        let currentHeading = null;

        // Calculate viewport height and set threshold for heading detection
        const viewportHeight = window.innerHeight;
        const threshold = viewportHeight * 0.25; // 25% from the top (reduced from 30%)

        // Check which heading is most visible in the viewport
        let maxVisibleHeight = 0;
        let mostVisibleHeading = null;

        headings.forEach(heading => {
          const rect = heading.getBoundingClientRect();

          // If the heading is near the top of the viewport (within threshold)
          if (rect.top < threshold && rect.top > -50) { // Allow slight scrolling past
            currentHeading = heading;
            return; // Exit early, we found our heading
          }
          
          // If no heading is in the threshold zone, use the most visible one
          if (rect.top > 0 && rect.bottom < viewportHeight) {
            const visibleHeight = Math.min(viewportHeight, rect.bottom) - Math.max(0, rect.top);
            if (visibleHeight > maxVisibleHeight) {
              maxVisibleHeight = visibleHeight;
              mostVisibleHeading = heading;
            }
          }
        });

        // If we found a heading in the threshold, use it
        if (currentHeading) {
          // Keep current heading
        }
        // If no heading is in threshold but we found a visible heading, use that
        else if (mostVisibleHeading) {
          currentHeading = mostVisibleHeading;
        }
        // If we've scrolled down but found no headings, use the last one
        else if (window.scrollY > 0 && headings.length > 0) {
          let lastVisibleHeading = null;
          for (let i = headings.length - 1; i >= 0; i--) {
            if (headings[i].getBoundingClientRect().top < 0) {
              lastVisibleHeading = headings[i];
              break;
            }
          }
          currentHeading = lastVisibleHeading || headings[0];
        }
        // Otherwise use the first one
        else if (headings.length > 0) {
          currentHeading = headings[0];
        }

        // Remove active class from all TOC links
        document.querySelectorAll('.toc-content a').forEach(link => {
          link.classList.remove('active');
        });

        // If we found a current heading, highlight its TOC link
        if (currentHeading) {
          const id = currentHeading.id;
          const tocLink = document.querySelector(`.toc-content a[href="#${id}"]`);
          if (tocLink) {
            tocLink.classList.add('active');

            // Ensure the active link is visible in the TOC panel if it's open
            if (tocPanel.classList.contains('active')) {
              const tocPanelInner = document.querySelector('.toc-panel-inner');
              if (tocPanelInner) {
                const linkRect = tocLink.getBoundingClientRect();
                const panelRect = tocPanelInner.getBoundingClientRect();

                // If link is outside the visible area of the panel
                if (linkRect.top < panelRect.top || linkRect.bottom > panelRect.bottom) {
                  tocLink.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
              }
            }
          }
        }
      }

      // Toggle TOC panel with smooth transitions
      function toggleToc(open) {
        const isOpen = open !== undefined ? open : !tocToggle.classList.contains('active');
        if (isOpen) {
          generateToc();
        }
        tocToggle.classList.toggle('active', isOpen);
        tocToggle.setAttribute('aria-expanded', isOpen.toString());
        tocPanel.classList.toggle('active', isOpen);
        tocPanel.setAttribute('aria-hidden', (!isOpen).toString());
      }

      if (tocToggle && tocPanel) {
        tocToggle.addEventListener('click', () => toggleToc());
        tocClose?.addEventListener('click', () => toggleToc(false));
        // Auto-open TOC by default on post pages
        toggleToc(true);
      }

      // Close TOC when clicking outside
      document.addEventListener('click', (e) => {
        if (tocPanel.classList.contains('active') &&
            !tocPanel.contains(e.target) &&
            !tocToggle.contains(e.target) &&
            !tocClose.contains(e.target)) {
          toggleToc(false);
        }
      });

      // Close TOC with ESC key
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && tocPanel.classList.contains('active')) {
          toggleToc(false);
        }
      });

      // Set up back button event
      const backButton = document.querySelector('.nav-circle.top-left');
      if (backButton) {
        backButton.addEventListener('click', () => {
          window.history.back();
        });
      }

      // Make the bottom button slightly transparent on scroll
      const bottomButton = document.querySelector('.nav-circle.bottom-center');

      window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
          bottomButton.style.opacity = "0.7";
        } else {
          bottomButton.style.opacity = "1";
        }
      });

      // Generate TOC on page load only if it should be open
      if (window.innerWidth > 768) {
        generateToc();
      } else {
        // On mobile, start with TOC closed
        toggleToc(false);
      }
    });
  </script>
  
  <!-- Load TOC enhancement script -->
  <script src="/scripts/toc-enhancements.js" defer></script>
</Layout>

<style>
  /* Global Scrollbar Styling */
  :global(html) {
    scrollbar-width: thin;
    scrollbar-color: rgba(100, 100, 100, 0.4) transparent;
  }

  :global(::-webkit-scrollbar) {
    width: 8px;
    height: 8px;
  }

  :global(::-webkit-scrollbar-track) {
    background: transparent;
  }

  :global(::-webkit-scrollbar-thumb) {
    background-color: rgba(100, 100, 100, 0.4);
    border-radius: 4px;
  }

  :global(::-webkit-scrollbar-thumb:hover) {
    background-color: rgba(120, 120, 120, 0.6);
  }

  /* Make sure scrollbars are at the edge */
  :global(body),
  :global(.blog-post),
  :global(.toc-panel) {
    scrollbar-width: thin;
    overflow-y: auto;
  }

  /* Fix for work-post pages to match work pages */
  :global(body[data-page="work-post"]) {
    overflow-y: auto;
    overflow-x: hidden;
    height: auto;
    min-height: 100vh;
  }

  /* Blog Header - Static (not fixed) */
  .blog-header {
    width: 100%;
    display: flex;
    justify-content: center;
    margin: 55px 0 20px;
  }

  .blog-title {
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 1.3rem;
    color: rgba(240, 240, 240, 0.85);
    letter-spacing: -0.01em;
    position: relative;
  }

  .blog-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 1px;
    background-color: rgba(240, 240, 240, 0.4);
  }

  /* TOC Toggle Button - Solid white square that becomes hollow on hover */
  .toc-toggle {
    position: fixed;
    top: 30px;
    right: 30px;
    width: 20px; /* Smaller size */
    height: 20px; /* Smaller size */
    background-color: rgba(255, 255, 255, 0.9); /* Solid white */
    border: none;
    border-radius: 3px; /* Slightly rounded corners */
    cursor: pointer;
    z-index: 110;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: transform var(--transition-duration) var(--easing-standard),
                background-color var(--transition-duration) var(--easing-standard),
                border var(--transition-duration) var(--easing-standard);
    transform-origin: center;
  }

  /* Hover effect - grows and becomes hollow with dots */
  .toc-toggle:hover {
    transform: scale(1.2);
    background-color: transparent;
    border: 1.5px solid rgba(255, 255, 255, 0.9);
  }

  /* Active state (open state) - stays hollow */
  .toc-toggle.active {
    background-color: transparent;
    border: 1.5px solid rgba(255, 255, 255, 0.9);
  }

  /* Dots display */
  .toc-icon-dots {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 2px; /* Reduced from 3px for smaller size */
    opacity: 0;
    transition: opacity var(--transition-duration) var(--easing-standard);
  }

  /* Show dots on hover or when active */
  .toc-toggle:hover .toc-icon-dots,
  .toc-toggle.active .toc-icon-dots {
    opacity: 1;
  }

  .dot {
    width: 2.5px; /* Smaller dots */
    height: 2.5px; /* Smaller dots */
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    transition: width var(--transition-duration) var(--easing-standard),
                height var(--transition-duration) var(--easing-standard);
  }

  /* Lines hover effect (active state) */
  .toc-toggle.active:hover .dot {
    width: 10px; /* Expand to lines, slightly smaller */
    height: 1.5px; /* Thinner height for lines */
    border-radius: 1px; /* Less rounded for lines */
  }

  /* TOC Panel - Smoother slide animation and fully off-screen when closed */
  .toc-panel {
    position: fixed;
    top: 0;
    right: -350px; /* Start more off-screen to ensure it fully hides */
    width: 300px;
    height: 100vh;
    z-index: 100;
    overflow: hidden;
    transition: right 0.6s cubic-bezier(0.25, 0.1, 0.25, 1); /* Smoother animation */
  }

  /* Inner panel that gets the blur effect */
  .toc-panel-inner {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(20, 20, 20, 0.85);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    padding: 80px 20px 40px;
    overflow-y: auto;
  }

  .toc-panel.active {
    right: 0;
  }

  /* TOC Close Button with Arrow - Animation direction for closing */
  .toc-close {
    position: absolute;
    left: 10px;
    top: 85px;
    background: transparent;
    border: none;
    cursor: pointer;
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.8rem;
    transition: transform 0.4s cubic-bezier(0.25, 0.1, 0.25, 1),
                color 0.4s cubic-bezier(0.25, 0.1, 0.25, 1);
    opacity: 0.8;
    padding: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Rotate the arrow to point right (to close) */
  .toc-close .toc-close-arrow {
    display: inline-block;
    transform: rotate(180deg);
    transition: transform 0.4s cubic-bezier(0.25, 0.1, 0.25, 1);
  }

  .toc-close:hover {
    transform: translateX(-3px);
    opacity: 1;
  }

  .toc-close:hover .toc-close-arrow {
    transform: translateX(2px) rotate(180deg);
  }

  .toc-panel-title {
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.95);
    margin-bottom: 30px;
    font-weight: normal;
    text-align: center;
  }

  /* TOC content styling - Refined for minimal style */
  .toc-content {
    font-family: 'Georgia Custom', Georgia, serif;
    padding-left: 10px;
    text-align: center;
  }

  .toc-list-container {
    display: inline-block;
    text-align: left;
    width: 85%;
  }

  .toc-item {
    margin-bottom: 26px; /* Increased spacing between items */
  }

  .toc-item.toc-h3 {
    padding-left: 20px;
    font-size: 0.9rem;
    margin-bottom: 20px; /* Increased spacing */
    margin-top: 8px; /* Increased spacing from h2 */
  }

  .toc-item.toc-h4 {
    padding-left: 38px;
    font-size: 0.85rem;
    margin-bottom: 18px; /* Increased spacing */
    margin-top: 6px; /* Increased spacing from h3 */
  }

  .toc-content a {
    color: rgba(255, 255, 255, 0.78); /* Reduced opacity for non-active items */
    text-decoration: none;
    display: block;
    line-height: 1.4;
    padding: 5px 8px; /* Increased padding for better clickable area */
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
    border-radius: 4px;
  }

  .toc-content a:hover {
    color: rgba(255, 255, 255, 1);
    transform: translateX(5px);
    background-color: rgba(255, 255, 255, 0.08); /* Subtle background on hover */
  }

  .toc-content a.active {
    color: rgba(255, 255, 255, 1);
    transform: translateX(8px);
    position: relative;
    font-size: 1.05em;
    font-weight: 500;
    background-color: rgba(255, 255, 255, 0.1); /* Clearer active state */
  }

  .toc-content a.active::before {
    content: '';
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 80%;
    background-color: rgba(255, 255, 255, 0.9); /* Brighter indicator */
    border-radius: 1px;
  }

  .toc-empty {
    color: rgba(255, 255, 255, 0.7);
    font-style: italic;
    font-size: 0.9rem;
    text-align: center;
    padding: 20px 0;
  }

  /* Better content stability during TOC animation */
  :global(body[data-page="blog-post"]),
  :global(body[data-page="work-post"]) {
    overflow-x: hidden;
  }

  .blog-post {
    max-width: 750px;
    margin: 40px auto 100px; /* Reduced top margin since title is now static */
    padding: 0 30px;
    position: relative;
    width: 100%;
    transition: opacity 0.6s cubic-bezier(0.25, 0.1, 0.25, 1);
    line-height: 1.7;
  }

  /* More subtle content fade when TOC is open - no transform to maintain stability */
  :global(body.toc-active) .blog-post {
    opacity: 0.8;
  }

  .post-header {
    margin-bottom: 40px;
  }

  .post-title {
    font-size: 2rem;
    font-weight: normal;
    margin-bottom: 10px;
    font-family: 'Georgia Custom', Georgia, serif;
    color: rgba(240, 240, 240, 0.95);
    line-height: 1.2;
    letter-spacing: -0.02em;
  }

  .post-date {
    font-size: 0.95rem;
    color: rgba(200, 200, 200, 0.75);
    font-family: 'Georgia Custom', Georgia, serif;
  }

  .post-content {
    font-family: 'Georgia Custom', Georgia, serif;
    color: rgba(240, 240, 240, 0.95);
    line-height: 1.8;
    letter-spacing: 0.01em;
    font-size: 1.05rem;
  }

  .post-content p {
    margin-bottom: 1.6em;
  }

  .post-content :global(h2) {
    margin-top: 2em;
    margin-bottom: 0.8em;
    font-size: 1.6rem;
    color: rgba(240, 240, 240, 0.98);
  }

  .post-content :global(h3) {
    margin-top: 1.8em;
    margin-bottom: 0.7em;
    font-size: 1.3rem;
  }

  .post-content :global(ul), .post-content :global(ol) {
    margin-bottom: 1.6em;
    padding-left: 1.5em;
  }

  .post-content :global(li) {
    margin-bottom: 0.5em;
  }

  .post-content :global(a) {
    color: rgba(200, 200, 255, 0.9);
    text-decoration: none;
    border-bottom: 1px solid rgba(200, 200, 255, 0.3);
    transition: border-color 0.3s ease, color 0.3s ease;
  }

  .post-content :global(a:hover) {
    color: rgba(210, 210, 255, 1);
    border-bottom-color: rgba(210, 210, 255, 0.7);
  }

  .post-content :global(blockquote) {
    border-left: 3px solid rgba(200, 200, 200, 0.3);
    padding-left: 1.2em;
    margin-left: 0;
    margin-right: 0;
    margin-bottom: 1.6em;
    font-style: italic;
    color: rgba(220, 220, 220, 0.85);
  }

  .post-content :global(h2),
  .post-content :global(h3),
  .post-content :global(h4),
  .post-content :global(h5),
  .post-content :global(h6) {
    font-family: 'Georgia Custom', Georgia, serif;
    font-weight: normal;
    margin: 2em 0 0.7em;
    color: rgba(240, 240, 240, 0.95);
    line-height: 1.3;
  }

  .post-content :global(h2) {
    font-size: 1.85rem;
    letter-spacing: -0.01em;
  }

  .post-content :global(h3) {
    font-size: 1.6rem;
  }

  .post-content :global(p) {
    margin-bottom: 1.4em;
    font-size: 1.05rem;
  }

  .post-content :global(a) {
    color: rgba(220, 220, 220, 0.95);
    text-decoration: underline;
    text-decoration-color: rgba(200, 200, 200, 0.4);
    text-underline-offset: 2px;
    transition: text-decoration-color 0.2s ease;
  }

  .post-content :global(a:hover) {
    text-decoration-color: rgba(220, 220, 220, 0.95);
  }

  .post-content :global(ul),
  .post-content :global(ol) {
    margin-left: 2em;
    margin-bottom: 1.4em;
  }

  .post-content :global(li) {
    margin-bottom: 0.6em;
  }

  .post-content :global(pre) {
    background-color: rgba(10, 10, 10, 0.6);
    padding: 1.2em;
    border-radius: 3px;
    overflow-x: auto;
    margin: 1.8em 0;
    border: 1px solid rgba(255, 255, 255, 0.05);
  }

  .post-content :global(code) {
    font-family: monospace;
    font-size: 0.95em;
    color: rgba(220, 220, 220, 0.95);
  }

  .post-content :global(blockquote) {
    border-left: 3px solid rgba(200, 200, 200, 0.3);
    padding-left: 1.2em;
    margin-left: 0;
    font-style: italic;
    color: rgba(200, 200, 200, 0.8);
    margin: 1.8em 0;
  }

  /* Heading highlight effect when navigating from TOC */
  .post-content :global(.highlight) {
    animation: highlight-pulse 1.5s ease;
    position: relative;
  }

  .post-content :global(.highlight)::after {
    content: '';
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 5px;
    height: 80%;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    animation: highlight-bar 1.5s ease;
  }

  @keyframes highlight-pulse {
    0% { transform: scale(1); }
    20% { transform: scale(1.03); }
    100% { transform: scale(1); }
  }

  @keyframes highlight-bar {
    0% { opacity: 0; }
    20% { opacity: 1; }
    100% { opacity: 0; }
  }

  /* Related Posts Styles */
  .related-posts {
    margin-top: 60px;
    padding: 30px;
    background-color: rgba(30, 30, 30, 0.3);
    border-radius: 8px;
    border: 1px solid rgba(200, 200, 200, 0.1);
  }

  .related-title {
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 1.3rem;
    color: rgba(240, 240, 240, 0.95);
    margin-bottom: 20px;
    font-weight: normal;
  }

  .related-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
  }

  .related-post {
    padding: 15px;
    background-color: rgba(20, 20, 20, 0.4);
    border-radius: 6px;
    transition: transform 0.3s ease, background-color 0.3s ease;
  }

  .related-post:hover {
    transform: translateY(-3px);
    background-color: rgba(30, 30, 30, 0.6);
  }

  .related-post-title {
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 1.1rem;
    font-weight: normal;
    margin-bottom: 8px;
    line-height: 1.3;
  }

  .related-post-title a {
    color: rgba(230, 230, 230, 0.9);
    text-decoration: none;
    transition: color 0.3s ease;
  }

  .related-post-title a:hover {
    color: rgba(255, 255, 255, 1);
    text-decoration: underline;
    text-underline-offset: 3px;
  }

  .related-post-date {
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 0.85rem;
    color: rgba(180, 180, 180, 0.75);
  }

  @media (max-width: 768px) {
    .related-grid {
      grid-template-columns: 1fr;
    }
  }

  /* Footer Styles */
  .post-footer {
    margin-top: 70px;
    padding-top: 25px;
    border-top: 1px solid rgba(200, 200, 200, 0.15);
    display: flex;
    flex-direction: column;
    gap: 25px;
  }

  .post-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
  }

  .post-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 35px;
  }

  /* Use dark buttons */
  .return-link,
  .subscribe-link {
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 0.95rem;
    color: rgba(240, 240, 240, 0.9);
    text-decoration: none;
    background-color: rgba(34, 34, 34, 0.6);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    padding: 0.4rem 0.9rem;
    border-radius: 1rem;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .return-link:hover,
  .subscribe-link:hover {
    background-color: rgba(34, 34, 34, 0.95);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  /* Responsive styles */
  @media (max-width: 1024px) {
    .blog-post {
      max-width: 90%;
    }
  }

  @media (max-width: 768px) {
    .blog-title {
      font-size: 1.2rem;
    }

    .blog-header {
      margin: 45px 0 15px;
    }

    .toc-toggle {
      top: 25px;
      right: 25px;
      width: 18px;
      height: 18px;
    }

    .toc-panel {
      width: 85%; /* Cover most of the screen on mobile */
      right: -100%; /* Fully off-screen when closed on mobile */
    }

    .toc-close {
      top: 75px;
      left: 12px;
    }

    .blog-post {
      margin-top: 20px;
      padding: 0 15px;
    }

    .post-title {
      font-size: 1.8rem;
    }

    /* On mobile, overlay the TOC without pushing content */
    :global(body.toc-active) .blog-post {
      opacity: 0.4;
    }
  }
</style>
</file>

<file path="src/pages/blog.astro">
---
import { getPostsByType, paginatePosts, getAllUniqueTags, slugifyStr } from "../utils/unifiedContent.js";
import Layout from "../layouts/Layout.astro";
import BlogPostCard from "../components/BlogPostCard.astro";

// Initialize variables with defaults
let featuredPost = null;
let regularPosts = [];
let blogPosts = [];
let allTags: string[] = [];
let displayableTags: string[] = [];
let error = false;
let errorMessage = "Blog posts are currently being updated. Please check back soon.";
let pagination: any = {};

const POSTS_PER_PAGE = 5;

try {
  // Fetch all blog posts using the JSON utility
  blogPosts = await getPostsByType('blog');

  if (blogPosts && blogPosts.length > 0) {
    // Identify single featured post and regular list
    featuredPost = blogPosts.find(post => post.featured) || null;
    regularPosts = featuredPost
      ? blogPosts.filter(post => post.slug !== featuredPost.slug)
      : blogPosts;
    // Paginate regular posts
    const { posts: paginatedPosts, pagination: pag } = paginatePosts(regularPosts, 1, POSTS_PER_PAGE);
    regularPosts = paginatedPosts;
    pagination = pag;

    // Get all unique tags
    allTags = await getAllUniqueTags();
    // Filter out internal tags for display
    const internalTags = ['blog','work','archive'];
    displayableTags = allTags.filter(tag => !internalTags.includes(tag.toLowerCase()));
  } else {
    // No posts found
    error = true;
    errorMessage = "No blog posts found yet. Please check back soon.";
  }
} catch (e) {
  console.error("Error loading blog posts:", e);
  error = true;
  // Fallback tags in case of error
  allTags = ["philosophy", "wisdom", "mastery", "AI", "synthesis", "innovation"];
}
---

<Layout
  pageTitle="Blog | PVB"
  isHomePage={false}
  accentColor="#f0f0f0"
  bgColor="rgba(10, 10, 10, 0.94)"
  backgroundImageUrl="/images/blackgranite.png"
  bodyDataPage="blog"
>
  <!-- Blog title - Static (not fixed) -->
  <h1 class="blog-header"><span class="blog-title">blog</span></h1>

  <!-- Full Page Content Container -->
  <div class="page-container">
    <!-- Left Sidebar -->
    <div class="blog-sidebar">
      <!-- Search Bar -->
      <div class="search-container sidebar-section" style="display: flex; justify-content: center;">
        <a href="/search" class="search-link sidebar-link">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="11" cy="11" r="8"></circle>
            <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
          </svg>
          <span>search</span>
        </a>
      </div>

      <!-- Archive Button -->
      <div class="archive-container sidebar-section">
        <a href="/blog/archive" class="archive-link sidebar-link">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M6 3h12l3 6v12a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9l3-6z" />
            <path d="M10 12h4" />
          </svg>
          <span>archives</span>
        </a>
      </div>

      <!-- Subscribe -->
      <div class="subscribe-container sidebar-section">
        <a href="mailto:<EMAIL>" class="subscribe-link sidebar-link">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M3 8l7.333 4.667a2 2 0 0 0 2.334 0L20 8" />
            <rect x="3" y="6" width="18" height="12" rx="2" ry="2" />
          </svg>
          <span>subscribe by email</span>
        </a>
      </div>

      <!-- Tags Filter (Collapsible) -->
      <div class="tags-container">
        <button class="tags-toggle sidebar-link" id="tags-toggle" aria-expanded="false" aria-controls="tags-list">
          <span class="tags-title">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M9 5H2v7l6.29 6.29c.94.94 2.48.94 3.42 0l7.58-7.58c.94-.94.94-2.48 0-3.42L13 2c-.94-.94-2.48-.94-3.42 0L9 5Z"></path>
              <path d="M6 9.01V9"></path>
            </svg>
            <span>tags</span>
          </span>
          <span class="toggle-icon">
            <span class="line hor"></span>
            <span class="line vert"></span>
          </span>
        </button>
        <div class="tags-list" id="tags-list">
          <a href="/tags" class="tag-link all-tags-link">all tags</a>
          {displayableTags.map(tag => {
            // tag is a simple string
            const tagName = tag;
            // Use slugified tag name for the URL to ensure consistency
            const tagSlug = tagName.toLowerCase().replace(/\s+/g, '-').replace(/[^\w\-]+/g, '').replace(/\-\-+/g, '-');
            return (
              <a href={`/blog/tag/${tagSlug}`} class="tag-link">{tagName}</a>
            );
          })}
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="blog-content">

      {error ? (
        <div class="blog-error">
          <p>{errorMessage}</p>
        </div>
      ) : blogPosts.length > 0 ? (
        <>
          {/* Single Featured Post */}
          {featuredPost && (
            <section class="featured-post" data-animate="fade-up">
              <h2 class="section-title">Featured</h2>
              <div class="post-item-wrapper" data-tags={featuredPost.tags?.map(tag => slugifyStr(typeof tag === 'string' ? tag : (tag.slug || tag.name))).filter(Boolean).join(',') || ''}>
                <BlogPostCard post={featuredPost} />
              </div>
            </section>
          )}
          <!-- Posts List -->
          <ul id="blog-post-list">
            {regularPosts.map(post => (
              <li class="post-list-item" data-tags={post.tags?.map(tag => slugifyStr(typeof tag === 'string' ? tag : (tag.slug || tag.name))).filter(Boolean).join(',') || ''}>
                <BlogPostCard post={post} />
              </li>
            ))}
          </ul>

          <!-- Pagination Controls -->
          <div class="pagination">
            {pagination.prevPage != null && <a href={`/blog/page/${pagination.prevPage}`} class="pagination-link prev-page">← Newer Posts</a>}
            <div class="pagination-info">Page {pagination.currentPage} of {pagination.totalPages}</div>
            {pagination.nextPage != null && <a href={`/blog/page/${pagination.nextPage}`} class="pagination-link next-page">Older Posts →</a>}
          </div>

          <!-- All Blogs Link -->
          <div class="all-blogs-link-container" style="text-align:center; margin-top: 2rem;">
            <a href="/blog/all" class="all-blogs-link">View All Blogs</a>
          </div>
        </>
      ) : (
        <div class="blog-error">
          <p>No blog posts available. Please check back soon.</p>
        </div>
      )}
    </div>
  </div>

<script is:inline>
  // Tag drawer toggle functionality
  document.getElementById('tags-toggle')?.addEventListener('click', function() {
    const container = this.closest('.tags-container');
    const isExpanded = this.getAttribute('aria-expanded') === 'true';
    
    // Toggle container state
    container.classList.toggle('open');
    this.setAttribute('aria-expanded', !isExpanded);
  });
</script>

<script is:inline>
  // Fade-up animation observer
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('in-view');
        observer.unobserve(entry.target);
      }
    });
  }, { threshold: 0.1 });
  document.querySelectorAll('[data-animate="fade-up"]').forEach(el => observer.observe(el));
</script>
</Layout>

<style>
  /* Global Scrollbar Styling */
  :global(html) {
    scrollbar-width: thin;
    scrollbar-color: rgba(100, 100, 100, 0.4) transparent;
  }

  :global(::-webkit-scrollbar) {
    width: 8px;
    height: 8px;
  }

  :global(::-webkit-scrollbar-track) {
    background: transparent;
  }

  :global(::-webkit-scrollbar-thumb) {
    background-color: rgba(100, 100, 100, 0.4);
    border-radius: 4px;
  }

  :global(::-webkit-scrollbar-thumb:hover) {
    background-color: rgba(120, 120, 120, 0.6);
  }

  /* Enable scrolling on blog page */
  :global(body[data-page="blog"]) {
    overflow-y: auto;
  }

  /* Blog Header - Static (not fixed) positioned below the logo */
  .blog-header {
    width: 100%;
    display: flex;
    justify-content: center;
    margin: 55px auto 40px; /* Increased bottom margin and centered */
  }

  .blog-title {
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 1.4rem; /* Adjusted for hierarchy */
    letter-spacing: -0.01em;
    position: relative;
    margin-bottom: 10px; /* Add space below underline */
  }

  /* Add subtle underline to blog title */
  .blog-title::after {
    content: '';
    position: absolute;
    bottom: -10px; /* Move underline down slightly */
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 1px;
    background-color: rgba(var(--color-accent-rgb), 0.5);
  }

  /* Error message when blog posts can't be loaded */
  .blog-error {
    text-align: center;
    padding: 2rem;
    margin: 2rem 0;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
    color: rgba(240, 240, 240, 0.8);
  }

  /* Full Page Container */
  .page-container {
    display: flex;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 30px; /* Consistent horizontal padding */
    gap: 40px;
  }

  /* Section titles */
  .section-title {
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 0.9rem; /* Adjusted for hierarchy */
    font-weight: 500;
    color: rgba(var(--color-accent-rgb), 0.6);
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.08em; /* Increase letter spacing */
  }

  /* Left Sidebar - Better spacing and styling */
  .blog-sidebar {
    width: 220px;
    padding-top: 30px;
    padding-right: 20px;
    position: sticky;
    top: 0;
    height: 100vh;
    align-self: flex-start;
  }

  /* Add more space between sidebar sections */
  .sidebar-section {
    margin-bottom: 40px; /* Increased spacing between sections */
  }

  /* Shared styles for sidebar interactive elements */
  .sidebar-link {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
    color: rgba(240, 240, 240, 0.8);
    text-decoration: none;
    font-family: 'Georgia Custom', Georgia, serif;
    padding: 0.5rem 1rem;
    background-color: rgba(34, 34, 34, 0.5);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 0.75rem; /* Match card radius */
    transition: all var(--transition-duration) var(--easing-standard);
  }

  .sidebar-link:hover,
  .sidebar-link:focus-visible {
    color: rgba(240, 240, 240, 0.9);
    background-color: rgba(40, 40, 40, 0.6);
    border-color: rgba(255, 255, 255, 0.12);
    outline: none;
  }

  .sidebar-link svg {
    opacity: 0.8;
    transition: opacity var(--transition-duration) var(--easing-standard);
  }

  .sidebar-link:hover svg {
    opacity: 1;
  }

  /* Search Styles */
  .search-link {
    /* Inherits from .sidebar-link */
  }

  /* Archive Button */
  .archive-link {
    /* Inherits from .sidebar-link */
  }

  /* Style the tags button like other sidebar links */
  .tags-toggle {
    position: relative;
    /* Inherits from .sidebar-link */
    justify-content: space-between; /* Keep icon on the right */
    width: 100%; /* Make it full width */
    cursor: pointer;
    border-radius: 0.75rem; /* Match parent */
    margin-bottom: 0; /* Remove margin if container handles it */
    background-color: transparent; /* Let container handle bg */
    border: none; /* Let container handle border */
    box-shadow: none; /* Let container handle shadow */
    padding: 0.6rem 1rem; /* Slightly more padding for the button itself */
  }

  .tags-toggle:hover,
  .tags-toggle:focus-visible {
    background-color: rgba(255, 255, 255, 0.05); /* Subtle hover inside */
    transform: none; /* No lift needed, container handles it */
    box-shadow: none;
    border: none;
    outline: none;
  }

  /* Tag drawer container */
  .tags-container {
    background-color: rgba(34, 34, 34, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 0.75rem;
    transition: background-color var(--transition-duration) var(--easing-standard), border-color var(--transition-duration) var(--easing-standard);
  }

  /* Highlight drawer when open */
  .tags-container.open {
    border-color: rgba(255, 255, 255, 0.2);
    background-color: rgba(40, 40, 40, 0.7);
  }

  /* Tag list collapse/expand animation */
  .tags-list {
    overflow: hidden;
    max-height: 0;
    opacity: 0;
    transform: translateY(-10px);
    transition: max-height 0.4s ease-out, opacity var(--transition-duration) var(--easing-standard), transform var(--transition-duration) var(--easing-standard), padding var(--transition-duration) var(--easing-standard), margin-top var(--transition-duration) var(--easing-standard);
    padding: 0;
    margin-top: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 0.4rem;
  }

  .tags-container.open .tags-list {
    max-height: 500px;
    opacity: 1;
    transform: translateY(0);
    padding: 0.75rem;
    margin-top: 0.5rem;
  }

  /* Toggle icon shape */
  .toggle-icon {
    position: relative;
    width: 0.8rem;
    height: 0.8rem;
    display: inline-block;
    margin-left: auto;
  }
  .toggle-icon .line {
    position: absolute;
    background: rgba(240,240,240,0.6);
    transition: background var(--transition-duration) var(--easing-standard), transform var(--transition-duration) var(--easing-standard);
  }
  .toggle-icon .hor {
    top: 50%; left: 0;
    width: 100%; height: 1px;
    transform-origin: center;
    transform: translateY(-50%) scaleX(1);
  }
  .toggle-icon .vert {
    left: 50%; top: 0;
    width: 1px; height: 100%;
    transform-origin: center top;
    transform: translateX(-50%) scaleY(1);
  }
  .tags-toggle:hover .toggle-icon .line {
    background: rgba(240,240,240,0.8);
  }
  .tags-toggle[aria-expanded="true"] .toggle-icon .vert {
    transform: translateX(-50%) scaleY(0);
  }
  .tags-toggle[aria-expanded="true"] .toggle-icon .hor {
    transform: translateY(-50%) scaleX(1);
  }

  /* Style tags in drawer as pills */
  .tags-list .tag-link {
    display: inline-block;
    display: inline-block; /* Ensure padding works */
    background: rgba(34, 34, 34, 0.5); /* Match sidebar link background */
    color: rgba(240, 240, 240, 0.8); /* Match sidebar link color */
    padding: 0.3rem 0.8rem;
    border-radius: 0.5rem; /* Slightly less rounded than sidebar link */
    font-size: 0.85rem; /* Slightly smaller */
    text-decoration: none;
    border: 1px solid rgba(255, 255, 255, 0.08); /* Match sidebar link border */
    transition: all var(--transition-duration) var(--easing-standard);
  }
  .tags-list .tag-link:hover,
  .tags-list .tag-link:focus-visible {
    color: rgba(240, 240, 240, 0.9); /* Match sidebar link hover color */
    background-color: rgba(40, 40, 40, 0.6); /* Match sidebar link hover background */
    border-color: rgba(255, 255, 255, 0.12); /* Match sidebar link hover border */
    outline: none;
  }

  /* Main Content - Full width layout */
  .blog-content {
    flex: 1;
    padding: 10px 30px 60px 30px; /* Consistent padding */
    max-width: 900px;
  }

  /* All Blogs Link */
  .all-blogs-link-container {
    text-align: center;
    margin-top: 2rem;
  }

  .all-blogs-link {
    font-size: 0.9rem;
    color: var(--blog-text-secondary);
    text-decoration: none;
    font-family: 'Georgia Custom', Georgia, serif;
    transition: all var(--transition-duration) var(--easing-standard);
    display: inline-block;
    padding: 0.5rem 1rem; /* Match sidebar link padding */
    background-color: rgba(34, 34, 34, 0.5); /* Match sidebar link background */
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.08); /* Match sidebar link border */
    border-radius: 0.75rem; /* Match sidebar link radius */
  }

  .all-blogs-link:hover,
  .all-blogs-link:focus-visible {
    color: rgba(240, 240, 240, 0.9); /* Match sidebar link hover color */
    background-color: rgba(40, 40, 40, 0.6); /* Match sidebar link hover background */
    border-color: rgba(255, 255, 255, 0.12); /* Match sidebar link hover border */
    transform: none; /* Remove translateY */
    box-shadow: none; /* Remove box-shadow */
    outline: none;
  }

  /* Pagination */
  .pagination {
    margin-top: 3rem; /* Add more space above pagination */
    display: flex;
    gap: 1rem;
    align-items: center;
    justify-content: center;
  }

  .pagination-link {
    font-family: 'Georgia Custom', Georgia, serif;
    color: rgba(var(--color-accent-rgb), 0.7);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border: 1px solid rgba(var(--color-accent-rgb), 0.2);
    border-radius: 0.5rem;
    transition: all var(--transition-duration) var(--easing-standard);
  }

  .pagination-link:hover,
  .pagination-link:focus-visible {
    color: rgba(var(--color-accent-rgb), 1);
    background-color: rgba(var(--color-accent-rgb), 0.1);
    border-color: rgba(var(--color-accent-rgb), 0.4);
    outline: none;
  }

  .pagination-info {
    font-size: 0.9rem;
    color: rgba(240, 240, 240, 0.6);
    font-family: 'Georgia Custom', Georgia, serif;
  }

  /* Link Colors */
  .sidebar-link {
    color: var(--blog-text-secondary);
    text-decoration: none;
    transition: color 0.2s ease;
  }
  .sidebar-link:hover {
    color: var(--blog-text);
  }

  .blog-content a {
    color: var(--blog-text);
    text-decoration: none;
    transition: color 0.2s ease;
  }
  .blog-content a:hover {
    color: var(--blog-text-inverse);
  }

  /* Blog Header Title */
  .blog-header .blog-title {
    color: var(--blog-text);
  }

  /* Mobile responsive tweaks */
  @media (max-width: 768px) {
    .page-container {
      flex-direction: column;
      padding: 0 20px;
      gap: 20px;
    }
    .sidebar-section {
      display: none;
    }
    .blog-sidebar {
      width: 100%;
      padding: 0;
      position: relative;
      height: auto;
      margin-bottom: 20px;
    }
    .blog-content {
      max-width: 100%;
    }
  }

  /* Sidebar tweaks */
  .search-container { display: flex; justify-content: center; }
</style>
</file>

</files>

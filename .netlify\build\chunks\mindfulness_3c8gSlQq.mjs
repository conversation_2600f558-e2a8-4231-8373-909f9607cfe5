import { a as createComponent, m as maybeRenderHead, u as unescapeHTML, r as renderTemplate } from './astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import 'clsx';

const html = "";

				const frontmatter = {"text":"Silence is not empty, it's full of answers.","author":"Unknown","linkedPage":"/blog/power-of-silence","cardTitle":"Mindfulness","cardSubtitle":"Finding clarity in quiet","featured":false,"tags":["mindfulness","silence"]};
				const file = "C:/Users/<USER>/Desktop/pvb-astro/src/content/quotes/mindfulness.md";
				const url = undefined;
				function rawContent() {
					return "";
				}
				function compiledContent() {
					return html;
				}
				function getHeadings() {
					return [];
				}

				const Content = createComponent((result, _props, slots) => {
					const { layout, ...content } = frontmatter;
					content.file = file;
					content.url = url;

					return renderTemplate`${maybeRenderHead()}${unescapeHTML(html)}`;
				});

export { Content, compiledContent, Content as default, file, frontmatter, getHeadings, rawContent, url };

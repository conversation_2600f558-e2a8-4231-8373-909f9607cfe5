---
import Layout from '../../layouts/Layout.astro';
import { getPostsByType } from '../../utils/unifiedContent.js';
import ArchivePostCard from '../../components/ArchivePostCard.astro';

let posts = await getPostsByType('archive');
if (!posts || posts.length === 0) {
  // Fallback to blog posts if no specific archive posts are found
  posts = await getPostsByType('blog');
}

// Group posts by year and sort
const postsByYear = {};
posts.forEach(post => {
    const year = new Date(post.published_at).getFullYear();
    if (!postsByYear[year]) {
        postsByYear[year] = [];
    }
    postsByYear[year].push(post);
});

// Sort posts within each year by date (newest first)
Object.keys(postsByYear).forEach(year => {
    postsByYear[year].sort((a, b) => 
        new Date(b.published_at).getTime() - new Date(a.published_at).getTime()
    );
});

const years = Object.keys(postsByYear).sort((a, b) => parseInt(b) - parseInt(a));
const totalPosts = posts.length;
---

<Layout title="Archive | Your Name" description="A chronological list of all articles and posts.">
  <main class="archive-main">
    <header class="archive-header">
      <h1 class="archive-title">Archive</h1>
      <p class="archive-description">
        A chronological timeline of all {totalPosts} articles, thoughts, and projects.
      </p>
    </header>
    
    <div class="timeline-container">
      {years.map((year, yearIndex) => (
        <section class="timeline-year" style={`--delay: ${yearIndex * 0.1}s`}>
          <div class="year-header">
            <h2 class="year-heading">{year}</h2>
            <span class="year-count">{postsByYear[year].length} posts</span>
          </div>
          <div class="posts-timeline">
            {postsByYear[year].map((post, postIndex) => (
              <div style={`--delay: ${(yearIndex * 0.1) + (postIndex * 0.05)}s`}>
                <ArchivePostCard post={post} />
              </div>
            ))}
          </div>
        </section>
      ))}
    </div>
  </main>
</Layout>

<style>
  .archive-main {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 2rem 4rem;
    animation: fade-in 0.6s ease-out;
  }
  
  .archive-header {
    margin-bottom: 4rem;
    text-align: center;
  }
  
  .archive-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 1rem 0;
    color: var(--theme-text, #2c2c2c);
    letter-spacing: -0.025em;
  }
  
  .archive-description {
    font-size: 1.125rem;
    color: var(--theme-text-light, #666);
    margin: 0;
    line-height: 1.6;
  }
  
  .timeline-container {
    position: relative;
  }
  
  .timeline-container::before {
    content: '';
    position: absolute;
    left: 45px;
    top: 0;
    bottom: 0;
    width: 1px;
    background: linear-gradient(
      to bottom,
      transparent,
      rgba(20, 20, 20, 0.1) 20%,
      rgba(20, 20, 20, 0.1) 80%,
      transparent
    );
  }
  
  .timeline-year {
    margin-bottom: 3.5rem;
    opacity: 0;
    animation: slide-in-up 0.6s ease-out var(--delay, 0s) forwards;
  }
  
  .timeline-year:last-child {
    margin-bottom: 0;
  }
  
  .year-header {
    display: flex;
    align-items: baseline;
    gap: 1rem;
    margin-bottom: 1.5rem;
    position: relative;
  }
  
  .year-header::before {
    content: '';
    position: absolute;
    left: 40px;
    top: 50%;
    width: 10px;
    height: 10px;
    background: var(--theme-accent, #3a2c23);
    border-radius: 50%;
    transform: translateY(-50%);
    box-shadow: 0 0 0 3px var(--theme-bg, white);
  }
  
  .year-heading {
    font-size: 2rem;
    font-weight: 800;
    margin: 0;
    color: var(--theme-text, #2c2c2c);
    letter-spacing: -0.03em;
  }
  
  .year-count {
    font-size: 0.875rem;
    color: var(--theme-text-light, #666);
    font-weight: 500;
  }
  
  .posts-timeline {
    margin-left: 70px;
    border-left: 1px solid rgba(20, 20, 20, 0.05);
    padding-left: 1.5rem;
  }
  
  .posts-timeline > div {
    opacity: 0;
    animation: fade-in-up 0.4s ease-out var(--delay, 0s) forwards;
  }
  
  @keyframes fade-in {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes slide-in-up {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes fade-in-up {
    from {
      opacity: 0;
      transform: translateY(15px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @media (max-width: 768px) {
    .archive-main {
      padding: 1.5rem 1.5rem 3rem;
    }
    
    .archive-header {
      margin-bottom: 3rem;
    }
    
    .archive-title {
      font-size: 2rem;
    }
    
    .archive-description {
      font-size: 1rem;
    }
    
    .timeline-container::before {
      left: 20px;
    }
    
    .year-header::before {
      left: 15px;
      width: 8px;
      height: 8px;
    }
    
    .year-heading {
      font-size: 1.75rem;
    }
    
    .posts-timeline {
      margin-left: 40px;
      padding-left: 1rem;
    }
  }
</style>

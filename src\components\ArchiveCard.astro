---
export interface Props {
  post: {
    slug: string;
    title: string;
    published_at: string;
    tags?: Array<string | { name: string; slug?: string }>;
    excerpt?: string;
    custom_excerpt?: string;
    html?: string;
    status?: string;
    repo_url?: string;
    repoUrl?: string;
    live_url?: string;
    liveUrl?: string;
    [key: string]: any;
  };
}

const { post } = Astro.props;
const postDate = new Date(post.published_at).toLocaleDateString('en-US', {
    month: 'short',
    day: '2-digit'
});
---
<a href={`/blog/${post.slug}/`} class="archive-card">
  <span class="date">{postDate}</span>
  <span class="title">{post.title}</span>
</a>

<style>
  .archive-card {
    display: flex;
    align-items: baseline;
    gap: 1rem;
    padding: 0.75rem 1rem;
    border-radius: var(--theme-border-radius);
    transition: background-color 0.2s ease-in-out;
    text-decoration: none;
    color: var(--theme-text);
  }
  .archive-card:hover {
    background-color: var(--theme-bg-offset);
  }
  .archive-card .date {
    font-family: var(--font-mono);
    font-size: 0.9em;
    color: var(--theme-text-light);
    flex-shrink: 0;
    width: 50px; /* Adjust as needed */
  }
  .archive-card .title {
    font-weight: 600;
  }
</style>
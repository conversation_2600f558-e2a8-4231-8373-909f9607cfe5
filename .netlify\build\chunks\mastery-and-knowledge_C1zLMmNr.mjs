import { a as createComponent, m as maybeRenderHead, u as unescapeHTML, r as renderTemplate } from './astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import 'clsx';

const html = "<h2 id=\"mastery-and-knowledge\">Mastery and Knowledge</h2>\n<p>I realized something. You know, from a young age I thought that knowledge are associated with trees. And I understand now that they’re talking about mastery. Because mastery isn’t about knowledge. It’s about knowing what’s real and what isn’t.</p>\n<p>Because the tree knows that it’s a tree. It’s not up for debate. The tree knows it has leaves. It’s not up for debate. The tree knows that it is not a lake. There’s no negotiation there.</p>\n<p>So when somebody says to the tree that you’re not a tree, you’re just a lake, the tree doesn’t react. The tree says, no, I’m a tree. No matter if the person calls it arrogant, an old geezer, or whatever they call it, it will still reply, I am a tree. Nothing less, nothing more.</p>\n<p>That is knowledge. It’s not about knowing information. It’s about knowing what’s real.</p>\n<h2 id=\"the-wisdom-of-certainty\">The Wisdom of Certainty</h2>\n<p>This perspective shifts how we might think about expertise and learning. It’s not about how many facts you can recite or how many books you’ve read. It’s about having such a deep understanding that you recognize truth from falsehood without hesitation.</p>\n<h2 id=\"beyond-information\">Beyond Information</h2>\n<p>In our information-saturated age, we often confuse data collection with wisdom. But the tree doesn’t need to constantly update its database to know what it is. Its knowledge is embodied, certain, and unshakable.</p>\n<h2 id=\"finding-your-truth\">Finding Your Truth</h2>\n<p>Perhaps mastery in any field comes when you reach this level of certainty - not from arrogance, but from deep connection with reality. When you know something so thoroughly that external opinions cannot shake your understanding.</p>";

				const frontmatter = {"title":"Mastery and Knowledge - The Tree's Wisdom","author":"PVB","pubDatetime":"2023-12-09T12:00:00.000Z","featured":true,"draft":false,"tags":["philosophy","wisdom","mastery"],"description":"Reflections on how true knowledge is about recognizing what's real, not just accumulating information."};
				const file = "C:/Users/<USER>/Desktop/pvb-astro/src/content/blog/mastery-and-knowledge.md";
				const url = undefined;
				function rawContent() {
					return "\n## Mastery and Knowledge\n\nI realized something. You know, from a young age I thought that knowledge are associated with trees. And I understand now that they're talking about mastery. Because mastery isn't about knowledge. It's about knowing what's real and what isn't.\n\nBecause the tree knows that it's a tree. It's not up for debate. The tree knows it has leaves. It's not up for debate. The tree knows that it is not a lake. There's no negotiation there.\n\nSo when somebody says to the tree that you're not a tree, you're just a lake, the tree doesn't react. The tree says, no, I'm a tree. No matter if the person calls it arrogant, an old geezer, or whatever they call it, it will still reply, I am a tree. Nothing less, nothing more.\n\nThat is knowledge. It's not about knowing information. It's about knowing what's real.\n\n## The Wisdom of Certainty\n\nThis perspective shifts how we might think about expertise and learning. It's not about how many facts you can recite or how many books you've read. It's about having such a deep understanding that you recognize truth from falsehood without hesitation.\n\n## Beyond Information\n\nIn our information-saturated age, we often confuse data collection with wisdom. But the tree doesn't need to constantly update its database to know what it is. Its knowledge is embodied, certain, and unshakable.\n\n## Finding Your Truth\n\nPerhaps mastery in any field comes when you reach this level of certainty - not from arrogance, but from deep connection with reality. When you know something so thoroughly that external opinions cannot shake your understanding.\n";
				}
				function compiledContent() {
					return html;
				}
				function getHeadings() {
					return [{"depth":2,"slug":"mastery-and-knowledge","text":"Mastery and Knowledge"},{"depth":2,"slug":"the-wisdom-of-certainty","text":"The Wisdom of Certainty"},{"depth":2,"slug":"beyond-information","text":"Beyond Information"},{"depth":2,"slug":"finding-your-truth","text":"Finding Your Truth"}];
				}

				const Content = createComponent((result, _props, slots) => {
					const { layout, ...content } = frontmatter;
					content.file = file;
					content.url = url;

					return renderTemplate`${maybeRenderHead()}${unescapeHTML(html)}`;
				});

export { Content, compiledContent, Content as default, file, frontmatter, getHeadings, rawContent, url };

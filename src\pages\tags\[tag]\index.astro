---
import Layout from "../../../layouts/Layout.astro";
import BlogPostCard from "../../../components/BlogPostCard.astro";
import { getAllUniqueTags, getPostsByTag } from "../../../utils/unifiedContent";
import { SITE } from "../../../config";
import { slugifyStr } from "../../../utils/slugify";

export interface Props {
  posts: any[];
  tag: string;
  tagName: string;
}

export async function getStaticPaths() {
  // Get all unique tags from JSON content
  const allTags = await getAllUniqueTags();

  // Create paths for each tag
  return Promise.all(allTags.map(async (tagName) => {
    const tag = slugifyStr(tagName);
    // Use the slug for filtering posts, not the tag name
    console.log(`Getting posts for tag: ${tagName}, slug: ${tag}`);
    const posts = await getPostsByTag(tag);

    return {
      params: { tag },
      props: {
        posts,
        tag,
        tagName
      },
    };
  }));
}

const { posts, tag, tagName } = Astro.props;
---

<Layout
  pageTitle={`Tag: ${tagName} | PVB`}
  isHomePage={false}
  accentColor="#f0f0f0"
  bgColor="rgba(0, 0, 0, 0.9)"
  backgroundImageUrl="none"
  bodyDataPage="tag"
>
  <div class="quote-container" style="max-width: 800px;">
    <h1 class="text-3xl font-bold mb-4 text-accent">
      Tag: <span transition:name={tag}>#{tagName}</span>
    </h1>
    <p class="mb-8 text-muted">
      {posts.length} post{posts.length > 1 ? "s" : ""} with this tag
    </p>

    <div class="posts-container">
      <ul>
        {posts.map(post => <BlogPostCard post={post} />)}
      </ul>
    </div>

    <div class="mt-10">
      <a
        href="/tags"
        class="back-link"
      >
        <span class="back-arrow">←</span>
        <span>All tags</span>
      </a>
    </div>
  </div>
</Layout>

<style>
  .quote-container {
    margin: 0 auto;
  }
  h1 {
    font-family: 'Georgia Custom', Georgia, serif;
  }
  .text-muted {
    color: var(--color-text-secondary);
  }
  .text-accent {
    color: var(--color-accent);
  }
  .posts-container {
    margin: 2rem 0;
    text-align: left;
  }
  .posts-container ul {
    list-style: none;
    padding: 0;
  }
  .back-link {
    display: inline-flex;
    align-items: center;
    color: var(--color-accent);
    text-decoration: none;
    transition: opacity 0.2s ease;
    font-family: 'Georgia Custom', Georgia, serif;
  }
  .back-link:hover {
    opacity: 0.75;
  }
  .back-arrow {
    margin-right: 0.5rem;
  }
</style>
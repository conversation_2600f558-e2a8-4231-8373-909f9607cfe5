---
import Layout from '../layouts/Layout.astro';

import { links } from '../data/links';
// Define links with their metadata
---

<Layout
  pageTitle="links | pvb"
  isHomePage={false}
  accentColor="#f0f0f0"
  bgColor="rgba(10, 10, 10, 0.94)"
  backgroundImageUrl="/images/blackgranite.png"
  bodyDataPage="links"
>
  <div class="links-header">
    <h1 class="links-title">links</h1>
  </div>

  <main class="links-container">
    {links.map((category) => (
      <section class="link-category">
        <div class="category-header">
          <div class="category-line"></div>
          <h2 class="category-title">{category.category}</h2>
          <div class="category-line"></div>
        </div>
        
        <div class="links-grid">
          {category.items.map((link) => (
            <a 
              href={link.url} 
              class={`link-card ${link.url === '#' ? 'inactive' : ''}`}
              target={link.url !== '#' ? "_blank" : ""}
              rel="noopener noreferrer"
            >
              <div class="link-icon">
                <img src={link.logo} alt={`${link.name} logo - ${link.description}`} loading="lazy" />
              </div>
              <div class="link-content">
                <div class="link-title">{link.name}</div>
                <div class="link-description">{link.description}</div>
              </div>
              {link.url !== '#' && <div class="link-arrow" aria-hidden="true">→</div>}
            </a>
          ))}
        </div>
      </section>
    ))}
  </main>
</Layout>

<style>
  /* Fix for global overflow */
  :global(body[data-page="links"]) {
    overflow-x: hidden;
    overflow-y: auto;
    color: var(--blog-text);
    background-attachment: fixed;
    background-size: cover;
    height: auto;
    min-height: 100vh;
  }

  :global(.content-wrapper) {
    position: relative;
    height: auto;
    min-height: 100vh;
    width: 100%;
    padding-bottom: 60px;
  }

  /* Header styling */
  .links-header {
    width: 100%;
    text-align: center;
    margin: 55px 0 30px;
    position: relative;
  }

  .links-title {
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 1.5rem;
    color: var(--blog-text);
    letter-spacing: -0.01em;
    position: relative;
    text-transform: lowercase;
    font-weight: normal;
  }

  /* Subtle underline for links title */
  .links-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 1px;
    background-color: var(--blog-border);
  }

  /* Content container */
  .links-container {
    max-width: 700px;
    margin: 0 auto 60px;
    padding: 0 25px;
  }

  /* Category styling */
  .link-category {
    margin-bottom: 30px;
  }

  .category-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin: 30px 0 15px;
  }

  .category-line {
    flex-grow: 1;
    height: 1px;
    background-color: var(--blog-border);
  }

  .category-title {
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 0.9rem;
    font-weight: normal;
    color: var(--blog-text-secondary);
    white-space: nowrap;
    letter-spacing: 0.03em;
    padding: 0 0.5rem;
    margin: 0;
    text-transform: lowercase;
  }

  /* Grid layout for cards */
  .links-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  /* Link card styling - more minimalist */
  .link-card {
    display: flex;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.03);
    border: 1px solid var(--blog-border);
    border-radius: 4px;
    padding: 12px;
    text-decoration: none;
    transition: all 0.3s var(--easing-standard);
    color: inherit; /* Inherit text color */
  }

  .link-card:hover {
    background-color: rgba(255, 255, 255, 0.05);
    transform: translateY(-2px);
    border-color: rgba(255, 255, 255, 0.2);
  }

  /* Inactive link state - no link yet */
  .link-card.inactive {
    opacity: 0.5;
    cursor: default;
    pointer-events: none;
  }

  /* Link icon container */
  .link-icon {
    width: 32px;
    height: 32px;
    min-width: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    overflow: hidden;
    margin-right: 12px;
  }

  .link-icon img {
    width: 18px;
    height: 18px;
    object-fit: contain;
  }

  /* Link content */
  .link-content {
    flex: 1;
    overflow: hidden;
  }

  .link-title {
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 0.85rem;
    margin: 0 0 3px;
    color: var(--blog-text);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .link-description {
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 0.7rem;
    margin: 0;
    color: var(--blog-text-secondary);
  }

  /* Link arrow */
  .link-arrow {
    margin-left: 8px;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.4);
    transition: transform var(--transition-duration) var(--easing-standard);
  }

  .link-card:hover .link-arrow {
    transform: translateX(2px);
    color: rgba(255, 255, 255, 0.7);
  }

  /* Responsive design */
  @media (max-width: 768px) {
    .links-grid {
      grid-template-columns: 1fr;
    }
    
    .links-title {
      font-size: 1.3rem;
    }
    
    .category-title {
      font-size: 0.8rem;
    }
  }
  
  @media (max-width: 480px) {
    .links-header {
      margin: 45px 0 25px;
    }
    
    .link-card {
      padding: 10px;
    }
    
    .link-icon {
      width: 28px;
      height: 28px;
      min-width: 28px;
      margin-right: 10px;
    }
    
    .link-icon img {
      width: 16px;
      height: 16px;
    }
    
    .link-title {
      font-size: 0.8rem;
    }
    
    .link-description {
      font-size: 0.65rem;
    }
  }
</style>

// --- DOM Elements ---
const body = document.body;
const menuToggle = document.getElementById('menu-toggle');
const mainMenu = document.getElementById('main-menu');
const rootStyle = getComputedStyle(document.documentElement);
const transitionDurationCSS = parseFloat(rootStyle.getPropertyValue('--transition-duration') || '0.4') * 1000;
const menuItemExitDurationCSS = parseFloat(rootStyle.getPropertyValue('--menu-item-exit-duration') || '0.38') * 1000; // Used for menu items AND X close animation

let isMenuOpen = false;
let isQuoteCardOpen = false;
let currentQuote = null;
// Fallback quotes in case API fails
const fallbackQuotes = [
    { text: "Many mistake stability for safety, but only the dead remain still.", attribution: "Pruthvi Bhat", linkedPage: "/blog/stability-vs-safety", cardTitle: "Core Philosophy", cardSubtitle: "Exploring the need for change" },
    { text: "The purpose of knowledge is action, not more knowledge.", attribution: "Pruthvi Bhat", linkedPage: "/blog/knowledge-and-action", cardTitle: "Applied Learning", cardSubtitle: "Insights into meaningful action" },
    { text: "Silence is not empty, it's full of answers.", attribution: "Unknown", linkedPage: "/blog/power-of-silence", cardTitle: "Mindfulness", cardSubtitle: "Finding clarity in quiet" }
];
const pageTransition = document.getElementById('page-transition');
const quoteIndicatorWrapper = document.getElementById('quote-indicator-wrapper');
const quoteCard = document.getElementById('quote-card');
const quoteTextEl = document.querySelector('.quote-text');
const quoteAttributionEl = document.querySelector('.quote-attribution');
const quoteCardTitleEl = document.querySelector('.quote-card-title');
const quoteCardSubtitleEl = document.querySelector('.quote-card-subtitle');
const navLinks = document.querySelectorAll('a[data-nav]');
const logoLinks = document.querySelectorAll('.logo, .menu-logo');
const topLeftNav = document.querySelector('.nav-circle.top-left');
const contentWrapper = document.querySelector('.content-wrapper');

// --- Functions ---
function navigate(url) {
    if (!url || url === '#' || url.startsWith('javascript:')) return;
    pageTransition.classList.add('active');
    setTimeout(() => { window.location.href = url; }, transitionDurationCSS);
}

async function fetchRandomQuote() {
    try {
        // Try quotes.json first since it exists in the dist/api directory
        const response = await fetch('/api/quotes.json');
        if (!response.ok) throw new Error('Failed to fetch quotes');
        const quotes = await response.json();
        // Select a random quote from the array
        const randomIndex = Math.floor(Math.random() * quotes.length);
        return quotes[randomIndex];
    } catch (error) {
        try {
            // If quotes.json fails, try the random-quote.json endpoint
            console.warn('Error fetching from quotes.json, trying random-quote.json:', error);
            const response = await fetch('/api/random-quote.json');
            if (!response.ok) throw new Error('Failed to fetch random quote');
            return await response.json();
        } catch (secondError) {
            console.warn('Error fetching from all quote endpoints:', secondError);
            // Fall back to local quotes if all API attempts fail
            return selectRandomFallbackQuote();
        }
    }
}

function selectRandomFallbackQuote() {
    const i = Math.floor(Math.random() * fallbackQuotes.length);
    return fallbackQuotes[i];
}

function displayQuote(q) {
    if (!q) return;
    // Check if elements exist before updating them
    if (quoteCardTitleEl) quoteCardTitleEl.textContent = q.cardTitle || '';
    if (quoteCardSubtitleEl) quoteCardSubtitleEl.textContent = q.cardSubtitle || '';
    if (quoteTextEl) quoteTextEl.textContent = q.text ? `"${q.text}"` : '';
    if (quoteAttributionEl) quoteAttributionEl.textContent = q.author || q.attribution || '';
    currentQuote = q;
}

function toggleMenu(forceState) {
    // Check if menuToggle exists
    if (!menuToggle) return;
    
    const shouldBeOpen = forceState === undefined ? !isMenuOpen : forceState;
    if (shouldBeOpen === isMenuOpen) return;

    if (!shouldBeOpen) { // Closing menu
        if (isQuoteCardOpen) toggleQuoteCard(false);
        body.classList.add('closing');
        menuToggle.setAttribute('aria-label', 'Open Menu');
        // Timeout duration should match the longest closing animation (menu items OR X icon)
        setTimeout(() => {
            body.classList.remove('menu-active', 'closing');
            isMenuOpen = false;
        }, menuItemExitDurationCSS); // Use exit duration for cleanup
    } else { // Opening menu
        if (isQuoteCardOpen) toggleQuoteCard(false);
        body.classList.add('menu-active');
        menuToggle.setAttribute('aria-label', 'Close Menu');
        isMenuOpen = true;
    }
}

function handleQuoteCardClick() { 
    if (currentQuote?.linkedPage) navigate(currentQuote.linkedPage); 
}

function toggleQuoteCard(forceState) {
    // Check if required elements exist
    if (!quoteCard || !quoteIndicatorWrapper) return;
    
    const shouldBeOpen = forceState === undefined ? !isQuoteCardOpen : forceState;
    if (shouldBeOpen === isQuoteCardOpen) return;

    if (shouldBeOpen) { // Opening card
        if (isMenuOpen) {
            toggleMenu(false);
            setTimeout(openCard, menuItemExitDurationCSS + 50);
        } else {
            openCard();
        }
    } else { // Closing card
        closeCard();
    }

    function openCard() {
         if (isQuoteCardOpen) return;
         quoteCard.classList.add('active');
         quoteIndicatorWrapper.classList.add('active');
         body.classList.add('quote-card-active');
         quoteIndicatorWrapper.setAttribute('aria-label', 'Hide Quote Information');
         quoteCard.addEventListener('click', handleQuoteCardClick);
         isQuoteCardOpen = true;
         requestAnimationFrame(() => {
            const indicatorRect = quoteIndicatorWrapper.getBoundingClientRect();
            quoteCard.style.top = `${indicatorRect.bottom + 15}px`;
        });
    }

    function closeCard() {
         if (!isQuoteCardOpen) return;
         quoteCard.classList.remove('active');
         quoteIndicatorWrapper.classList.remove('active');
         body.classList.remove('quote-card-active');
         quoteIndicatorWrapper.setAttribute('aria-label', 'Show Quote Information');
         quoteCard.removeEventListener('click', handleQuoteCardClick);
         isQuoteCardOpen = false;
     }
}

function setTheme(t) { body.dataset.theme = t; body.classList.remove('theme-light', 'theme-dark'); body.classList.add(`theme-${t}`); }

function setPageContext() {
    // Check if topLeftNav exists
    if (!topLeftNav) return;
    
    const page = body.dataset.page || 'home';
    if (page !== 'home') {
        topLeftNav.setAttribute('title', 'Back');
        topLeftNav.setAttribute('aria-label', 'Go Back');
        topLeftNav.onclick = (e) => { e.preventDefault(); pageTransition.classList.add('active'); setTimeout(() => { history.back(); }, transitionDurationCSS); };
    } else {
        topLeftNav.setAttribute('title', 'About');
        topLeftNav.setAttribute('aria-label', 'About Page');
        topLeftNav.onclick = (e) => { e.preventDefault(); navigate('/about'); };
    }
 }

// --- Event Listeners ---
document.addEventListener('DOMContentLoaded', () => {
    // Initialize menu toggle button
    if (menuToggle) menuToggle.addEventListener('click', () => toggleMenu());

    // Initialize quote indicator
    if (quoteIndicatorWrapper) quoteIndicatorWrapper.addEventListener('click', (e) => {
        e.stopPropagation();
        toggleQuoteCard();
    });

    // Initialize navigation links
    if (navLinks && navLinks.length) {
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const url = link.getAttribute('href');
                if (isMenuOpen) {
                    toggleMenu(false);
                    setTimeout(() => navigate(url), 50);
                } else {
                    navigate(url);
                }
            });
        });
    }

    // Initialize logo links
    if (logoLinks && logoLinks.length) {
        logoLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const url = link.getAttribute('href');
                if (isMenuOpen) {
                    toggleMenu(false);
                    setTimeout(() => navigate(url), 50);
                } else {
                    navigate(url);
                }
            });
        });
    }

    // Initialize keyboard events
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            if (isMenuOpen) toggleMenu(false);
            else if (isQuoteCardOpen) toggleQuoteCard(false);
        }
    });

    // Initialize click outside events
    document.addEventListener('click', (e) => {
        if (isQuoteCardOpen && quoteCard && !quoteCard.contains(e.target) && quoteIndicatorWrapper && !quoteIndicatorWrapper.contains(e.target)) {
            toggleQuoteCard(false);
        }
        if (isMenuOpen && mainMenu && !mainMenu.contains(e.target) && menuToggle && !menuToggle.contains(e.target)) {
            toggleMenu(false);
        }
    });

    // Set page context based on data-page attribute
    setPageContext();

    // Initialize with a random quote
    fetchRandomQuote().then(quote => {
        displayQuote(quote);
    }).catch(error => {
        console.error('Failed to load quote:', error);
        const fallbackQuote = selectRandomFallbackQuote();
        displayQuote(fallbackQuote);
    });

    // Remove page transition effect
    setTimeout(() => {
        if (pageTransition) pageTransition.classList.remove('active');
        body.style.opacity = 1;
    }, 50);
});

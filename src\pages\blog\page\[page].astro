---
import { getPostsByType, paginatePosts } from "../../../utils/unifiedContent";
import Layout from "../../../layouts/Layout.astro";
import type { GetStaticPathsResult } from "astro";

// Number of posts per page
const POSTS_PER_PAGE = 5;

export async function getStaticPaths() {
  // Local posts per page constant
  const LOCAL_POSTS_PER_PAGE = 5;
  // Get only blog posts
  const blogPosts = await getPostsByType('blog');
  const totalPages = Math.ceil(blogPosts.length / LOCAL_POSTS_PER_PAGE);
  
  const paths: GetStaticPathsResult = [];
  
  // Generate pages for each pagination number
  for (let i = 1; i <= totalPages; i++) {
    const paginatedData = paginatePosts(blogPosts, i, LOCAL_POSTS_PER_PAGE);
    
    paths.push({
      params: { page: i.toString() },
      props: { 
        paginatedData,
        currentPage: i,
        totalPages,
      }
    });
  }
  
  return paths;
}

const { paginatedData, currentPage, totalPages } = Astro.props;
const { posts, pagination } = paginatedData;

// Filter internal tags from archive pages
const internalTags = ['blog','work','archive'];

---

<Layout
  pageTitle={`Blog - Page ${currentPage} | PVB`}
  isHomePage={false}
  accentColor="#f0f0f0"
  bgColor="rgba(10, 10, 10, 0.94)"
  backgroundImageUrl="/images/blackgranite.png"
  bodyDataPage="blog"
>
  <!-- Blog title - Static (not fixed) -->
  <div class="blog-header">
    <div class="blog-title">blog</div>
  </div>

  <!-- Full Page Content Container -->
  <div class="page-container">
    <!-- Left Sidebar -->
    <div class="blog-sidebar">
      <!-- Search Bar -->
      <div class="search-container sidebar-section">
        <a href="/search" class="search-link">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="11" cy="11" r="8"></circle>
            <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
          </svg>
          <span>search</span>
        </a>
      </div>

      <!-- Archive Button -->
      <div class="archive-container sidebar-section">
        <a href="/blog/timeline" class="archive-link">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M3 3v18h18"></path>
            <path d="M7 17l4-4 4 4 4-4"></path>
            <path d="M7 11l4-4 4 4 4-4"></path>
          </svg>
          <span>archive</span>
        </a>
      </div>

      <!-- Subscribe -->
      <div class="subscribe-container sidebar-section">
        <a href="#" class="subscribe-link">subscribe by email</a>
      </div>
    </div>

    <!-- Main Content -->
    <div class="blog-content">
      <!-- Posts List -->
      <div class="posts-list">
        {posts.map((post, index) => (
          <div class={`post-item ${index === 0 ? 'first-post' : ''}`}>
            <h2 class="post-title">
              <a href={`/blog/${post.slug}`}>{post.title}</a>
            </h2>
            <div class="post-meta">
              <span class="post-date">
                {new Date(post.published_at).toLocaleDateString('en-US', {day: 'numeric', month: 'long', year: 'numeric'})}
              </span>
              {post.reading_time && (
                <span class="reading-time">
                  • {post.reading_time} min read
                </span>
              )}
            </div>
            <p class="post-description">{post.description}</p>
            <div class="post-tags">
              {post.tags?.map(tag => {
                const name = typeof tag === 'string' ? tag : (tag.slug || tag.name || '');
                return name;
              })
              .filter(name => name && !internalTags.includes(name.toLowerCase()))
              .map(name => (
                <a href={`/blog/tag/${name}`} class="post-tag">#{name}</a>
              ))}
            </div>
            <a href={`/blog/${post.slug}`} class="read-more">
              Read Post <span class="read-more-arrow">→</span>
            </a>
          </div>
        ))}
      </div>

      <!-- Ghost-style Pagination -->
      <div class="pagination">
        {pagination.hasPrevPage ? (
          <a href={`/blog/page/${pagination.prevPage}`} class="pagination-link prev-page">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M19 12H5"></path>
              <path d="M12 19l-7-7 7-7"></path>
            </svg>
            <span>Newer Posts</span>
          </a>
        ) : (
          <span class="pagination-link disabled">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M19 12H5"></path>
              <path d="M12 19l-7-7 7-7"></path>
            </svg>
            <span>Newer Posts</span>
          </span>
        )}
        
        <div class="pagination-info">
          Page {pagination.currentPage} of {pagination.totalPages}
        </div>
        
        {pagination.hasNextPage ? (
          <a href={`/blog/page/${pagination.nextPage}`} class="pagination-link next-page">
            <span>Older Posts</span>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M5 12h14"></path>
              <path d="M12 5l7 7-7 7"></path>
            </svg>
          </a>
        ) : (
          <span class="pagination-link disabled">
            <span>Older Posts</span>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M5 12h14"></path>
              <path d="M12 5l7 7-7 7"></path>
            </svg>
          </span>
        )}
      </div>
    </div>
  </div>
</Layout>

<style>
  /* Global Scrollbar Styling */
  :global(html) {
    scrollbar-width: thin;
    scrollbar-color: rgba(100, 100, 100, 0.4) transparent;
  }

  :global(::-webkit-scrollbar) {
    width: 8px;
    height: 8px;
  }

  :global(::-webkit-scrollbar-track) {
    background: transparent;
  }

  :global(::-webkit-scrollbar-thumb) {
    background-color: rgba(100, 100, 100, 0.4);
    border-radius: 4px;
  }

  :global(::-webkit-scrollbar-thumb:hover) {
    background-color: rgba(120, 120, 120, 0.6);
  }

  /* Enable scrolling on blog page */
  :global(body[data-page="blog"]) {
    overflow-y: auto;
  }

  /* Blog Header - Static (not fixed) positioned below the logo */
  .blog-header {
    width: 100%;
    display: flex;
    justify-content: center;
    margin: 55px 0 30px;
  }

  .blog-title {
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 1.5rem;
    color: rgba(240, 240, 240, 0.9);
    letter-spacing: -0.01em;
    position: relative;
  }

  /* Add subtle underline to blog title */
  .blog-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 1px;
    background-color: rgba(240, 240, 240, 0.4);
  }

  /* Full Page Container */
  .page-container {
    display: flex;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 30px;
    gap: 40px;
  }

  /* Left Sidebar - Better spacing and styling */
  .blog-sidebar {
    width: 220px;
    padding-top: 30px;
    padding-right: 20px;
    position: sticky;
    top: 0;
    height: 100vh;
    align-self: flex-start;
  }

  /* Add more space between sidebar sections */
  .sidebar-section {
    margin-bottom: 40px;
  }

  /* Search Styles */
  .search-link {
    display: flex;
    align-items: center;
    color: rgba(240, 240, 240, 0.9);
    text-decoration: none;
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    padding: 0.4rem 0.9rem;
    background-color: rgba(34, 34, 34, 0.6);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
  }

  .search-link:hover {
    color: rgba(240, 240, 240, 1);
    background-color: rgba(34, 34, 34, 0.95);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .search-link svg {
    margin-right: 10px;
  }

  /* Archive Button */
  .archive-container {
    display: flex;
    align-items: center;
  }

  .archive-link {
    font-size: 0.9rem;
    color: rgba(240, 240, 240, 0.9);
    text-decoration: none;
    font-family: 'Georgia Custom', Georgia, serif;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 0.4rem 0.9rem;
    background-color: rgba(34, 34, 34, 0.6);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
  }

  .archive-link:hover {
    color: rgba(240, 240, 240, 1);
    background-color: rgba(34, 34, 34, 0.95);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  /* Subscribe Link */
  .subscribe-link {
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 0.9rem;
    color: rgba(240, 240, 240, 0.9);
    text-decoration: none;
    background-color: rgba(34, 34, 34, 0.6);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    padding: 0.4rem 0.9rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
    transition: all 0.3s ease;
    display: inline-block;
  }

  .subscribe-link:hover {
    color: rgba(240, 240, 240, 1);
    background-color: rgba(34, 34, 34, 0.95);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  /* Main Content */
  .blog-content {
    flex: 1;
    padding-top: 10px;
    padding-bottom: 60px;
    padding-right: 20px;
    padding-left: 20px;
    max-width: 900px;
  }

  /* Posts List */
  .posts-list {
    display: flex;
    flex-direction: column;
    gap: 50px;
  }

  .post-item {
    margin-bottom: 20px;
    padding-bottom: 30px;
    border-bottom: 1px solid rgba(200, 200, 200, 0.1);
  }

  .post-item:last-child {
    border-bottom: none;
    margin-bottom: 40px;
  }

  .first-post {
    margin-bottom: 30px;
  }

  .post-title {
    font-size: 1.8rem;
    font-weight: normal;
    margin-bottom: 10px;
    font-family: 'Georgia Custom', Georgia, serif;
    line-height: 1.2;
    letter-spacing: -0.02em;
  }

  .first-post .post-title {
    font-size: 2.2rem;
  }

  .post-title a {
    color: rgba(240, 240, 240, 0.95);
    text-decoration: none;
    transition: color 0.3s ease;
  }

  .post-title a:hover {
    color: rgba(255, 255, 255, 1);
    text-decoration: underline;
    text-underline-offset: 3px;
    text-decoration-color: rgba(200, 200, 200, 0.4);
  }

  .post-meta {
    display: flex;
    align-items: center;
    font-size: 0.95rem;
    color: rgba(200, 200, 200, 0.75);
    margin-bottom: 16px;
    font-family: 'Georgia Custom', Georgia, serif;
  }

  .reading-time {
    margin-left: 5px;
  }

  .post-description {
    font-family: 'Georgia Custom', Georgia, serif;
    color: rgba(230, 230, 230, 0.9);
    line-height: 1.6;
    font-size: 1.05rem;
    margin-bottom: 16px;
    font-style: italic;
  }

  .post-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;
  }

  .post-tag {
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 0.85rem;
    color: rgba(200, 200, 200, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
    border-bottom: 1px solid rgba(200, 200, 200, 0.3);
    padding-bottom: 2px;
  }

  .post-tag:hover {
    color: rgba(240, 240, 240, 1);
    border-bottom-color: rgba(240, 240, 240, 0.6);
  }

  /* Read more link */
  .read-more {
    display: inline-block;
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 0.95rem;
    color: rgba(240, 240, 240, 0.9);
    text-decoration: none;
    transition: all 0.3s ease;
    padding: 0.4rem 0.9rem;
    background-color: rgba(34, 34, 34, 0.6);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
  }

  .read-more:hover {
    color: rgba(240, 240, 240, 1);
    background-color: rgba(34, 34, 34, 0.95);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .read-more-arrow {
    display: inline-block;
    margin-left: 2px;
    transition: transform 0.3s ease;
  }

  .read-more:hover .read-more-arrow {
    transform: translateX(3px);
  }

  /* Ghost-style Pagination */
  .pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 60px;
    padding-top: 30px;
    border-top: 1px solid rgba(200, 200, 200, 0.15);
  }

  .pagination-link {
    display: flex;
    align-items: center;
    gap: 8px;
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 0.95rem;
    color: rgba(240, 240, 240, 0.9);
    text-decoration: none;
    transition: all 0.3s ease;
    padding: 0.4rem 0.9rem;
    background-color: rgba(34, 34, 34, 0.6);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
  }

  .pagination-link.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: rgba(34, 34, 34, 0.3);
  }

  .pagination-link:not(.disabled):hover {
    color: rgba(240, 240, 240, 1);
    background-color: rgba(34, 34, 34, 0.95);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .pagination-info {
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 0.9rem;
    color: rgba(200, 200, 200, 0.7);
  }

  /* Responsive styles */
  @media (max-width: 1024px) {
    .blog-content {
      max-width: 100%;
    }
  }

  @media (max-width: 768px) {
    .page-container {
      flex-direction: column;
    }

    .blog-sidebar {
      width: 100%;
      padding-top: 10px;
      position: relative;
      height: auto;
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      align-items: center;
      gap: 15px;
    }

    .sidebar-section {
      margin: 0 10px 15px 0;
    }

    .blog-content {
      padding-top: 20px;
      width: 100%;
      padding-left: 0;
    }

    .post-title {
      font-size: 1.6rem;
    }

    .first-post .post-title {
      font-size: 1.8rem;
    }

    .pagination {
      flex-direction: column;
      gap: 15px;
    }

    .pagination-info {
      order: -1;
      margin-bottom: 10px;
    }
  }
</style>

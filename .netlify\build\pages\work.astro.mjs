import { c as createAstro, a as createComponent, m as maybeRenderHead, b as addAttribute, r as renderTemplate, e as renderTransition, d as renderComponent } from '../chunks/astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import { g as getCollection } from '../chunks/_astro_content_CcaQj6Wl.mjs';
import { $ as $$Layout } from '../chunks/Layout_rXbp99fE.mjs';
import 'clsx';
import { s as slugifyStr } from '../chunks/slugify_CHvHojPC.mjs';
/* empty css                                */
/* empty css                                 */
export { renderers } from '../renderers.mjs';

const $$Astro = createAstro("https://pvb.com");
const $$ProjectCard = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$ProjectCard;
  const { project, variant = "standard" } = Astro2.props;
  const { title, description, projectDate, tags, status, featured, repoUrl, liveUrl } = project.data;
  function formatDate(date) {
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short"
    });
  }
  const isFeatured = variant === "featured";
  return renderTemplate`${isFeatured ? renderTemplate`${maybeRenderHead()}<article class="project-card featured" data-astro-cid-mspuyifq><a${addAttribute(`/work/${project.slug}`, "href")} class="project-link" data-astro-cid-mspuyifq><div class="project-meta" data-astro-cid-mspuyifq><time${addAttribute(projectDate.toISOString(), "datetime")} data-astro-cid-mspuyifq>${formatDate(projectDate)}</time><div class="project-badges" data-astro-cid-mspuyifq>${status && status !== "Completed" && renderTemplate`<span class="project-status" data-astro-cid-mspuyifq>${status}</span>`}<span class="featured-badge" data-astro-cid-mspuyifq>Featured</span></div></div><h2 class="project-title" data-astro-cid-mspuyifq${addAttribute(renderTransition($$result, "xatmo7nz", "", slugifyStr(title)), "data-astro-transition-scope")}>${title}</h2><p class="project-description" data-astro-cid-mspuyifq>${description}</p>${tags && renderTemplate`<div class="project-tags" data-astro-cid-mspuyifq>${tags.map((tag) => renderTemplate`<span class="tag" data-astro-cid-mspuyifq>${tag}</span>`)}</div>`}<div class="project-actions" data-astro-cid-mspuyifq><span class="view-details" data-astro-cid-mspuyifq>
Read more <span class="arrow" data-astro-cid-mspuyifq>→</span></span><div class="external-links" data-astro-cid-mspuyifq>${repoUrl && renderTemplate`<a${addAttribute(repoUrl, "href")} target="_blank" rel="noopener noreferrer" class="external-link" onclick="event.stopPropagation()" data-astro-cid-mspuyifq>
Source <span class="ext-arrow" data-astro-cid-mspuyifq>↗</span></a>`}${liveUrl && renderTemplate`<a${addAttribute(liveUrl, "href")} target="_blank" rel="noopener noreferrer" class="external-link" onclick="event.stopPropagation()" data-astro-cid-mspuyifq>
Demo <span class="ext-arrow" data-astro-cid-mspuyifq>↗</span></a>`}</div></div></a></article>` : renderTemplate`<article class="project-card standard" data-astro-cid-mspuyifq><a${addAttribute(`/work/${project.slug}`, "href")} class="project-link" data-astro-cid-mspuyifq><div class="project-content" data-astro-cid-mspuyifq><h2 class="project-title" data-astro-cid-mspuyifq${addAttribute(renderTransition($$result, "pixvamr3", "", slugifyStr(title)), "data-astro-transition-scope")}>${title}</h2><p class="project-description" data-astro-cid-mspuyifq>${description}</p><div class="project-footer" data-astro-cid-mspuyifq><div class="project-meta" data-astro-cid-mspuyifq><time${addAttribute(projectDate.toISOString(), "datetime")} data-astro-cid-mspuyifq>${formatDate(projectDate)}</time>${status && status !== "Completed" && renderTemplate`<span class="project-status small" data-astro-cid-mspuyifq>${status}</span>`}</div><div class="project-tags-container" data-astro-cid-mspuyifq>${tags && tags.length > 0 && renderTemplate`<div class="project-tags small" data-astro-cid-mspuyifq>${tags.slice(0, 3).map((tag) => renderTemplate`<span class="tag small" data-astro-cid-mspuyifq>${tag}</span>`)}${tags.length > 3 && renderTemplate`<span class="more-tags" data-astro-cid-mspuyifq>+${tags.length - 3}</span>`}</div>`}</div></div></div></a></article>`}`;
}, "C:/Users/<USER>/Desktop/pvb-astro/src/components/ProjectCard.astro", "self");

const $$Work = createComponent(async ($$result, $$props, $$slots) => {
  const allProjects = await getCollection(
    "work",
    ({ data }) => data.status !== "Archived"
  );
  const sortedProjects = allProjects.sort(
    (a, b) => b.data.projectDate.valueOf() - a.data.projectDate.valueOf()
  );
  const featuredProjects = sortedProjects.filter((project) => project.data.featured);
  const regularProjects = sortedProjects.filter((project) => !project.data.featured);
  const publications = [
    {
      title: "Recursive Self-Referential Compression (RSRC): AI's Survival Map in the Post-Scaling Era",
      journal: "MURST Research Initiative",
      year: 2025,
      url: "#",
      pdfUrl: "/pdfs/RSRC_Paper.pdf"
    },
    {
      title: "Cordyceps militaris: Culturing, Optimization and Bioactive Compound Analysis",
      journal: "Research Paper (IEEE format)",
      year: 2023,
      url: "#",
      pdfUrl: "/pdfs/Cordyceps_Paper.pdf"
    }
  ];
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "pageTitle": "Work & Research | PVB", "isHomePage": false, "accentColor": "#f0f0f0", "bgColor": "rgba(0, 0, 0, 0.88)", "backgroundImageUrl": "/images/blackgranite.png", "bodyDataPage": "work", "data-astro-cid-jljc7dey": true }, { "default": async ($$result2) => renderTemplate`  ${maybeRenderHead()}<div class="work-header" data-astro-cid-jljc7dey> <h1 class="work-title" data-astro-cid-jljc7dey>work</h1> </div> <div class="content-container" data-astro-cid-jljc7dey> <!-- Featured Projects Section --> ${featuredProjects.length > 0 && renderTemplate`<section class="featured-projects-section" data-astro-cid-jljc7dey> ${featuredProjects.map((project) => renderTemplate`${renderComponent($$result2, "ProjectCard", $$ProjectCard, { "project": project, "variant": "featured", "data-astro-cid-jljc7dey": true })}`)} </section>`} <!-- Projects Section with refined, elegant styling --> ${regularProjects.length > 0 && renderTemplate`<section class="projects-section" data-astro-cid-jljc7dey> <div class="section-header" data-astro-cid-jljc7dey> <div class="section-line" data-astro-cid-jljc7dey></div> <h2 class="section-title" data-astro-cid-jljc7dey>Projects</h2> <div class="section-line" data-astro-cid-jljc7dey></div> </div> <div class="projects-grid" data-astro-cid-jljc7dey> ${regularProjects.map((project) => renderTemplate`${renderComponent($$result2, "ProjectCard", $$ProjectCard, { "project": project, "variant": "standard", "data-astro-cid-jljc7dey": true })}`)} </div> </section>`} <!-- Publications Section with refined styling --> <section class="publications-section" data-astro-cid-jljc7dey> <div class="section-header" data-astro-cid-jljc7dey> <div class="section-line" data-astro-cid-jljc7dey></div> <h2 class="section-title" data-astro-cid-jljc7dey>Research & Publications</h2> <div class="section-line" data-astro-cid-jljc7dey></div> </div> <ul class="publications-list" data-astro-cid-jljc7dey> ${publications.map((pub) => renderTemplate`<li class="publication-item" data-astro-cid-jljc7dey> <h3 class="pub-title" data-astro-cid-jljc7dey>${pub.title}</h3> <div class="pub-meta" data-astro-cid-jljc7dey> <p class="pub-details" data-astro-cid-jljc7dey>${pub.journal}, ${pub.year}</p> <div class="pub-links" data-astro-cid-jljc7dey> ${pub.pdfUrl && renderTemplate`<a${addAttribute(pub.pdfUrl, "href")} target="_blank" rel="noopener noreferrer" class="pub-link" data-astro-cid-jljc7dey>
PDF <span class="arrow" data-astro-cid-jljc7dey>↗</span> </a>`} ${pub.url && pub.url !== "#" && renderTemplate`<a${addAttribute(pub.url, "href")} target="_blank" rel="noopener noreferrer" class="pub-link" data-astro-cid-jljc7dey>
Link <span class="arrow" data-astro-cid-jljc7dey>↗</span> </a>`} </div> </div> </li>`)} </ul> </section> </div> ` })} `;
}, "C:/Users/<USER>/Desktop/pvb-astro/src/pages/work.astro", void 0);

const $$file = "C:/Users/<USER>/Desktop/pvb-astro/src/pages/work.astro";
const $$url = "/work";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Work,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

---
import Layout from "../layouts/Layout.astro";

const pageTitle = "404: Page Not Found";
---

<Layout
  pageTitle={pageTitle}
  isHomePage={false}
  accentColor="#f0f0f0"
  bgColor="rgba(0, 0, 0, 0.88)"
  backgroundImageUrl="/images/blackgranite.png"
  bodyDataPage="error"
>
  <div class="error-container">
    <h1 class="error-title">404</h1>
    <p class="error-message">Page not found</p>
    <div class="error-button-container">
      <a href="/" class="error-button">Return Home</a>
    </div>
  </div>
</Layout>

<style>
  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
    color: #f0f0f0;
    text-align: center;
    padding: 2rem;
  }

  .error-title {
    font-size: 5rem;
    font-weight: normal;
    margin: 0;
    line-height: 1;
    color: rgba(240, 240, 240, 0.95);
  }

  .error-message {
    font-size: 1.25rem;
    margin: 1rem 0 2rem;
    opacity: 0.8;
  }

  .error-button-container {
    margin-top: 1rem;
  }

  .error-button {
    display: inline-block;
    padding: 0.5rem 1.25rem;
    border: 1px solid rgba(240, 240, 240, 0.3);
    border-radius: 2rem;
    text-decoration: none;
    color: #f0f0f0;
    font-size: 0.9rem;
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
  }

  .error-button:hover {
    background-color: rgba(240, 240, 240, 0.1);
    border-color: rgba(240, 240, 240, 0.5);
    transform: translateY(-2px);
  }

  @media (max-width: 768px) {
    .error-title {
      font-size: 4rem;
    }
    
    .error-message {
      font-size: 1.1rem;
    }
  }
</style>

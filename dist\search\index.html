<!DOCTYPE html><html lang="en" data-astro-cid-sckkx6r4> <head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>Search | PVB</title><style>.quote-container[data-astro-cid-ipsxrsrh]{margin:0 auto}h1[data-astro-cid-ipsxrsrh]{font-family:Georgia Custom,Georgia,serif}.text-muted[data-astro-cid-ipsxrsrh]{color:var(--color-text-secondary)}.text-accent[data-astro-cid-ipsxrsrh]{color:var(--color-accent)}.search-container[data-astro-cid-ipsxrsrh]{margin:2rem 0}.search-placeholder[data-astro-cid-ipsxrsrh]{padding:2rem;border:1px solid rgba(var(--color-accent-rgb, 58, 44, 35),.2);border-radius:.5rem;text-align:center}.search-note[data-astro-cid-ipsxrsrh]{margin-top:1rem;font-size:.875rem}.search-note[data-astro-cid-ipsxrsrh] code[data-astro-cid-ipsxrsrh]{background-color:#0000000d;padding:.2rem .5rem;border-radius:.25rem;font-family:monospace}.back-link[data-astro-cid-ipsxrsrh]{display:inline-flex;align-items:center;color:var(--color-accent);text-decoration:none;transition:opacity .2s ease;font-family:Georgia Custom,Georgia,serif}.back-link[data-astro-cid-ipsxrsrh]:hover{opacity:.75}.back-arrow[data-astro-cid-ipsxrsrh]{margin-right:.5rem}
</style>
<link rel="stylesheet" href="/_astro/_slug_.CSlrdnFy.css"><script type="module">document.addEventListener("DOMContentLoaded",function(){console.log("Search page loaded. Actual search functionality will be available after build.")});
</script></head> <body data-page="search" style="--color-accent: #2a2a2a; --color-bg: rgba(245, 245, 245, 0.9); background-image: url(/images/whitemarble.png); background-color: rgba(245, 245, 245, 0.9);" data-astro-cid-sckkx6r4> <div class="page-transition" id="page-transition" data-astro-cid-sckkx6r4></div> <!-- Common logo for all pages --> <a href="/" class="logo" data-astro-cid-sckkx6r4>pvb</a> <div class="content-wrapper" data-astro-cid-sckkx6r4> <!-- Slot for page-specific content -->  <div class="quote-container" style="max-width: 800px;" data-astro-cid-ipsxrsrh> <h1 class="text-3xl font-bold mb-4 text-accent" data-astro-cid-ipsxrsrh>Search</h1> <p class="mb-8 text-muted" data-astro-cid-ipsxrsrh>Find posts by title, content, or tags</p> <div class="search-container" data-astro-cid-ipsxrsrh> <div id="search-box" class="mb-8" data-astro-cid-ipsxrsrh> <!-- This div will be replaced with the search UI --> <div class="search-placeholder" data-astro-cid-ipsxrsrh> <p class="text-muted" data-astro-cid-ipsxrsrh>Search functionality will be available after building the site.</p> <p class="search-note" data-astro-cid-ipsxrsrh>For local development, you need to build the site first with <code data-astro-cid-ipsxrsrh>npm run build</code></p> </div> </div> <div id="search-results" data-astro-cid-ipsxrsrh> <!-- Results will appear here --> </div> </div> <div class="mt-10" data-astro-cid-ipsxrsrh> <a href="/blog" class="back-link" data-astro-cid-ipsxrsrh> <span class="back-arrow" data-astro-cid-ipsxrsrh>←</span> <span data-astro-cid-ipsxrsrh>Back to blog</span> </a> </div> </div>  </div> <div class="quote-card" id="quote-card" data-astro-cid-sckkx6r4> <h3 class="quote-card-title" data-astro-cid-sckkx6r4></h3> <p class="quote-card-subtitle" data-astro-cid-sckkx6r4></p> </div> <!-- Include navigation on all pages --> <!-- Top-Left Nav Circle --><div class="nav-circle top-left" title="Back" aria-label="Go Back" data-astro-cid-pux6a34n> <span class="nav-icon" data-astro-cid-pux6a34n></span> <!-- CSS handles the '?' or '←' --> </div>   <!-- Note: Bottom-center nav circle is now handled by Layout.astro --> <script>
  // Initialize navigation functionality
  document.addEventListener('DOMContentLoaded', function() {
    const topLeftNav = document.querySelector('.nav-circle.top-left');
    const isHomePage = document.body.getAttribute('data-page') === 'home';

    if (topLeftNav) {
      topLeftNav.addEventListener('click', function() {
        if (isHomePage) {
          // On home page, go to about page
          window.location.href = '/about';
        } else {
          // On other pages, go back
          window.history.back();
        }
      });
    }
  });
</script>  <!-- Always include the bottom navigation button --> <div class="nav-circle bottom-center" id="menu-toggle" title="Menu" aria-label="Toggle Menu" data-astro-cid-sckkx6r4> <span class="nav-icon" data-astro-cid-sckkx6r4></span> </div> <!-- Include main menu on all pages --> <div class="main-menu" id="main-menu"> <a href="/" class="logo menu-logo">pvb</a> <div class="menu-wrapper"> <a href="/blog" data-nav>blog</a> <a href="/work" data-nav>work</a> <a href="/about" data-nav>about</a> <a href="/fitness" data-nav>fitness</a> <a href="/cv" class="side-menu-item left" data-nav>cv</a> <a href="/links" class="side-menu-item right" data-nav>links</a> <a href="/more" class="see-more" data-nav>
more
<span class="arrow">↓</span> </a> </div> </div> <script>
        // Function to ensure proper scrolling behavior
        document.addEventListener('DOMContentLoaded', function() {
            // Remove the transition overlay once page loads
            const pageTransition = document.getElementById('page-transition');
            setTimeout(() => {
                if (pageTransition) {
                    pageTransition.classList.remove('active');
                }
            }, 400);

            // Make sure scrollbar positioning is always at the edge
            const bodyEl = document.body;
            const pageType = bodyEl.getAttribute('data-page');

            // Add specific adjustments based on page type
            if (pageType === 'blog' || pageType === 'blog-post' || pageType === 'about' || pageType === 'work' || pageType === 'work-post') {
                bodyEl.style.overflowY = 'auto';
                bodyEl.style.height = 'auto';
                document.documentElement.style.overflowY = 'auto';
                document.documentElement.style.height = 'auto';

                // Special handling for full-page content on blog and blog post pages
                if (pageType === 'blog' || pageType === 'blog-post' || pageType === 'work' || pageType === 'work-post') {
                    const contentWrapper = document.querySelector('.content-wrapper');
                    if (contentWrapper) {
                        contentWrapper.style.position = 'relative';
                        contentWrapper.style.height = 'auto';
                        contentWrapper.style.minHeight = '100vh';
                    }
                }

                // Restore scroll position if it was saved (for navigation back)
                if (sessionStorage.getItem(`scrollPos-${pageType}`)) {
                    const savedScrollPos = parseInt(sessionStorage.getItem(`scrollPos-${pageType}`));
                    setTimeout(() => {
                        window.scrollTo(0, savedScrollPos);
                    }, 100);
                }

                // Save scroll position when navigating away
                window.addEventListener('beforeunload', function() {
                    sessionStorage.setItem(`scrollPos-${pageType}`, window.scrollY.toString());
                });

                // Change bottom button opacity on scroll
                const bottomButton = document.querySelector('.nav-circle.bottom-center');
                if (bottomButton) {
                    window.addEventListener('scroll', function() {
                        if (window.scrollY > 100) {
                            bottomButton.style.opacity = "0.7";
                        } else {
                            bottomButton.style.opacity = "1";
                        }
                    });
                }
            }
        });

        // Simple page transition
        document.addEventListener('DOMContentLoaded', function() {
            const pageTransition = document.getElementById('page-transition');
            const links = document.querySelectorAll('a:not([target="_blank"]):not([href^="#"]):not([href^="javascript"])');

            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    if (this.hostname === window.location.hostname) {
                        e.preventDefault();
                        const href = this.getAttribute('href');

                        // Apply the transition
                        if (pageTransition) {
                            pageTransition.classList.add('active');
                        }

                        // Navigate after transition
                        setTimeout(() => {
                            window.location.href = href;
                        }, 400); // Match the CSS transition duration
                    }
                });
            });
        });
    </script> <!-- Fix: Use either is:inline or src, not both --> <script src="/scripts/main.js" defer></script> </body> </html>   
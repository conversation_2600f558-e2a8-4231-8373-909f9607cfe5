---
export interface Props {
  tag: string;
  tagName: string;
  size?: "sm" | "lg";
  class?: string;
}

const { tag, tagName, size = "sm", class: className = "" } = Astro.props;
// Ensure tag and tagName are strings; if objects, extract slug/name
const safeTag = typeof tag === 'object' && tag !== null && tag.slug ? tag.slug : tag;
const safeTagName = typeof tagName === 'object' && tagName !== null && tagName.name ? tagName.name : tagName;
---

<a
  href={`/tags/${safeTag}`}
  class:list={[
    "tag-link",
    size === "sm" ? "tag-sm" : "tag-lg",
    className
  ]}
>
  #{safeTagName}
</a>

<style>
  .tag-link {
    display: inline-block;
    padding: 0.2rem 0.6rem;
    border-radius: 1rem;
    text-decoration: none;
    border: 1px solid rgba(var(--color-accent-rgb), 0.3);
    background-color: rgba(var(--color-accent-rgb), 0.05);
    color: rgba(var(--color-accent-rgb), 0.8);
    transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
    font-family: 'Georgia Custom', Georgia, serif;
  }

  .tag-link:hover {
    background-color: rgba(var(--color-accent-rgb), 0.15);
    border-color: rgba(var(--color-accent-rgb), 0.5);
    color: rgba(var(--color-accent-rgb), 1);
  }

  .tag-sm {
    font-size: 0.7rem;
  }

  .tag-lg {
    font-size: 0.8rem;
  }
</style>
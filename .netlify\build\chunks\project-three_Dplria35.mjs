import { a as createComponent, m as maybeRenderHead, u as unescapeHTML, r as renderTemplate } from './astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import 'clsx';

const html = "<h2 id=\"project-overview\">Project Overview</h2>\n<p>The Neural Network Visualization Framework creates intuitive, interactive 3D visualizations of neural network architectures and their learning processes. It translates complex mathematical structures into spatially coherent visual representations that researchers, students, and practitioners can explore and understand.</p>\n<h2 id=\"key-features\">Key Features</h2>\n<ul>\n<li><strong>Architecture Visualization</strong>: Renders neural networks as interactive 3D structures with intuitive representations of layers, neurons, and connections.</li>\n<li><strong>Learning Process Animation</strong>: Visualizes the training process in real-time, showing weight adjustments, activation patterns, and error propagation.</li>\n<li><strong>Customizable Views</strong>: Offers multiple visualization perspectives from high-level architecture overview to neuron-level activation details.</li>\n<li><strong>Interactive Exploration</strong>: Allows users to rotate, zoom, isolate layers, and select specific neurons to inspect their behavior.</li>\n<li><strong>Time Controls</strong>: Includes playback controls to pause, slow down, or speed up the visualization of training processes.</li>\n</ul>\n<h2 id=\"technical-implementation\">Technical Implementation</h2>\n<p>The framework is built using Three.js for WebGL-based 3D rendering, with a custom data processing pipeline that efficiently transforms neural network state data into visual elements. The system supports both pre-trained models and live training sessions.</p>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto; white-space: pre-wrap; word-wrap: break-word;\" tabindex=\"0\" data-language=\"javascript\"><code><span class=\"line\"><span style=\"color:#6A737D\">// Example of neuron activity visualization</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">function</span><span style=\"color:#B392F0\"> visualizeNeuronActivity</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#FFAB70\">layer</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#FFAB70\">activations</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#FFAB70\">timeStep</span><span style=\"color:#E1E4E8\">) {</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">  const</span><span style=\"color:#79B8FF\"> neurons</span><span style=\"color:#F97583\"> =</span><span style=\"color:#E1E4E8\"> layerMap.</span><span style=\"color:#B392F0\">get</span><span style=\"color:#E1E4E8\">(layer);</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  </span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">  // Update each neuron's visual properties based on its activation</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  neurons.</span><span style=\"color:#B392F0\">forEach</span><span style=\"color:#E1E4E8\">((</span><span style=\"color:#FFAB70\">neuron</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#FFAB70\">index</span><span style=\"color:#E1E4E8\">) </span><span style=\"color:#F97583\">=></span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    const</span><span style=\"color:#79B8FF\"> activation</span><span style=\"color:#F97583\"> =</span><span style=\"color:#E1E4E8\"> activations[index];</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    </span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">    // Map activation to visual properties</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    const</span><span style=\"color:#79B8FF\"> intensity</span><span style=\"color:#F97583\"> =</span><span style=\"color:#B392F0\"> normalizeActivation</span><span style=\"color:#E1E4E8\">(activation);</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    const</span><span style=\"color:#79B8FF\"> hue</span><span style=\"color:#F97583\"> =</span><span style=\"color:#79B8FF\"> 220</span><span style=\"color:#F97583\"> +</span><span style=\"color:#E1E4E8\"> (</span><span style=\"color:#79B8FF\">120</span><span style=\"color:#F97583\"> *</span><span style=\"color:#E1E4E8\"> intensity); </span><span style=\"color:#6A737D\">// Blue to purple</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    const</span><span style=\"color:#79B8FF\"> scale</span><span style=\"color:#F97583\"> =</span><span style=\"color:#79B8FF\"> 1</span><span style=\"color:#F97583\"> +</span><span style=\"color:#E1E4E8\"> (intensity </span><span style=\"color:#F97583\">*</span><span style=\"color:#79B8FF\"> 0.5</span><span style=\"color:#E1E4E8\">);</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    </span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">    // Apply visual updates with temporal easing</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    neuron.material.color.</span><span style=\"color:#B392F0\">setHSL</span><span style=\"color:#E1E4E8\">(hue</span><span style=\"color:#F97583\">/</span><span style=\"color:#79B8FF\">360</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#79B8FF\">0.8</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#79B8FF\">0.5</span><span style=\"color:#E1E4E8\">);</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    neuron.scale.</span><span style=\"color:#B392F0\">lerp</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#F97583\">new</span><span style=\"color:#79B8FF\"> THREE</span><span style=\"color:#E1E4E8\">.</span><span style=\"color:#B392F0\">Vector3</span><span style=\"color:#E1E4E8\">(scale, scale, scale), </span><span style=\"color:#79B8FF\">0.3</span><span style=\"color:#E1E4E8\">);</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    </span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">    // Add activation trail for temporal context</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    if</span><span style=\"color:#E1E4E8\"> (timeStep </span><span style=\"color:#F97583\">%</span><span style=\"color:#79B8FF\"> 5</span><span style=\"color:#F97583\"> ===</span><span style=\"color:#79B8FF\"> 0</span><span style=\"color:#E1E4E8\">) {</span></span>\n<span class=\"line\"><span style=\"color:#B392F0\">      addActivationTrail</span><span style=\"color:#E1E4E8\">(neuron, intensity);</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    }</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  });</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span>\n<span class=\"line\"></span></code></pre>\n<h2 id=\"current-progress\">Current Progress</h2>\n<p>The core visualization engine and data processing pipeline are complete. Current development is focused on improving performance for large networks, enhancing the user interface for better exploration, and adding support for more network architectures including transformers and CNNs.</p>\n<h2 id=\"future-directions\">Future Directions</h2>\n<p>Future plans include adding support for collaborative exploration in shared virtual spaces, creating predefined educational pathways that guide users through neural network concepts, and developing an API that allows real-time visualization of custom models during training in popular frameworks like TensorFlow and PyTorch.</p>";

				const frontmatter = {"title":"Neural Network Visualization Framework","projectDate":"2024-02-10T00:00:00.000Z","status":"In Progress","featured":false,"tags":["Visualization","Machine Learning","JavaScript","Three.js","WebGL"],"ogImage":"/images/neural-viz-preview.png","description":"An interactive framework for visualizing neural network architectures and real-time learning processes through elegant 3D representations.","repoUrl":"https://github.com/username/neural-viz","liveUrl":"https://neural-viz-demo.com"};
				const file = "C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-three.md";
				const url = undefined;
				function rawContent() {
					return "\n## Project Overview\n\nThe Neural Network Visualization Framework creates intuitive, interactive 3D visualizations of neural network architectures and their learning processes. It translates complex mathematical structures into spatially coherent visual representations that researchers, students, and practitioners can explore and understand.\n\n## Key Features\n\n- **Architecture Visualization**: Renders neural networks as interactive 3D structures with intuitive representations of layers, neurons, and connections.\n- **Learning Process Animation**: Visualizes the training process in real-time, showing weight adjustments, activation patterns, and error propagation.\n- **Customizable Views**: Offers multiple visualization perspectives from high-level architecture overview to neuron-level activation details.\n- **Interactive Exploration**: Allows users to rotate, zoom, isolate layers, and select specific neurons to inspect their behavior.\n- **Time Controls**: Includes playback controls to pause, slow down, or speed up the visualization of training processes.\n\n## Technical Implementation\n\nThe framework is built using Three.js for WebGL-based 3D rendering, with a custom data processing pipeline that efficiently transforms neural network state data into visual elements. The system supports both pre-trained models and live training sessions.\n\n```javascript\n// Example of neuron activity visualization\nfunction visualizeNeuronActivity(layer, activations, timeStep) {\n  const neurons = layerMap.get(layer);\n  \n  // Update each neuron's visual properties based on its activation\n  neurons.forEach((neuron, index) => {\n    const activation = activations[index];\n    \n    // Map activation to visual properties\n    const intensity = normalizeActivation(activation);\n    const hue = 220 + (120 * intensity); // Blue to purple\n    const scale = 1 + (intensity * 0.5);\n    \n    // Apply visual updates with temporal easing\n    neuron.material.color.setHSL(hue/360, 0.8, 0.5);\n    neuron.scale.lerp(new THREE.Vector3(scale, scale, scale), 0.3);\n    \n    // Add activation trail for temporal context\n    if (timeStep % 5 === 0) {\n      addActivationTrail(neuron, intensity);\n    }\n  });\n}\n```\n\n## Current Progress\n\nThe core visualization engine and data processing pipeline are complete. Current development is focused on improving performance for large networks, enhancing the user interface for better exploration, and adding support for more network architectures including transformers and CNNs.\n\n## Future Directions\n\nFuture plans include adding support for collaborative exploration in shared virtual spaces, creating predefined educational pathways that guide users through neural network concepts, and developing an API that allows real-time visualization of custom models during training in popular frameworks like TensorFlow and PyTorch.";
				}
				function compiledContent() {
					return html;
				}
				function getHeadings() {
					return [{"depth":2,"slug":"project-overview","text":"Project Overview"},{"depth":2,"slug":"key-features","text":"Key Features"},{"depth":2,"slug":"technical-implementation","text":"Technical Implementation"},{"depth":2,"slug":"current-progress","text":"Current Progress"},{"depth":2,"slug":"future-directions","text":"Future Directions"}];
				}

				const Content = createComponent((result, _props, slots) => {
					const { layout, ...content } = frontmatter;
					content.file = file;
					content.url = url;

					return renderTemplate`${maybeRenderHead()}${unescapeHTML(html)}`;
				});

export { Content, compiledContent, Content as default, file, frontmatter, getHeadings, rawContent, url };

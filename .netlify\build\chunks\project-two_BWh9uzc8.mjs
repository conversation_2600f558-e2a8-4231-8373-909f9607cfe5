import { a as createComponent, m as maybeRenderHead, u as unescapeHTML, r as renderTemplate } from './astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import 'clsx';

const html = "<h2 id=\"project-overview\">Project Overview</h2>\n<p>This project is developing a sophisticated knowledge management system inspired by tools like Obsidian and Roam Research. It focuses on creating a seamless experience for capturing, connecting, and retrieving information using a graph-based approach to knowledge.</p>\n<h2 id=\"key-features\">Key Features</h2>\n<ul>\n<li><strong>Bidirectional Linking</strong>: Create connections between notes that work in both directions, allowing for natural knowledge discovery.</li>\n<li><strong>Graph Visualization</strong>: Interactive visualization of your knowledge network, helping identify clusters and connections.</li>\n<li><strong>Markdown-Based</strong>: Simple yet powerful formatting using extended Markdown syntax.</li>\n<li><strong>Local-First</strong>: All data stored locally with optional encrypted sync.</li>\n<li><strong>Customizable Workspace</strong>: Flexible layout system with multiple panes and views.</li>\n<li><strong>Advanced Search</strong>: Full-text search with support for complex queries and filters.</li>\n</ul>\n<h2 id=\"technical-implementation\">Technical Implementation</h2>\n<p>The application is built using Electron for cross-platform compatibility, with a React frontend and TypeScript for type safety. The knowledge graph is stored in a specialized graph database optimized for quick traversal and querying.</p>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto; white-space: pre-wrap; word-wrap: break-word;\" tabindex=\"0\" data-language=\"typescript\"><code><span class=\"line\"><span style=\"color:#6A737D\">// Example of the bidirectional linking implementation</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">class</span><span style=\"color:#B392F0\"> NoteManager</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#B392F0\">  createLink</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#FFAB70\">sourceNoteId</span><span style=\"color:#F97583\">:</span><span style=\"color:#79B8FF\"> string</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#FFAB70\">targetNoteId</span><span style=\"color:#F97583\">:</span><span style=\"color:#79B8FF\"> string</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#FFAB70\">linkText</span><span style=\"color:#F97583\">:</span><span style=\"color:#79B8FF\"> string</span><span style=\"color:#E1E4E8\">)</span><span style=\"color:#F97583\">:</span><span style=\"color:#B392F0\"> Link</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">    // Create the forward link</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    const</span><span style=\"color:#79B8FF\"> forwardLink</span><span style=\"color:#F97583\"> =</span><span style=\"color:#F97583\"> new</span><span style=\"color:#B392F0\"> Link</span><span style=\"color:#E1E4E8\">({</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">      sourceId: sourceNoteId,</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">      targetId: targetNoteId,</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">      text: linkText,</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">      direction: </span><span style=\"color:#9ECBFF\">'forward'</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    });</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    </span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">    // Create the backward link</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    const</span><span style=\"color:#79B8FF\"> backwardLink</span><span style=\"color:#F97583\"> =</span><span style=\"color:#F97583\"> new</span><span style=\"color:#B392F0\"> Link</span><span style=\"color:#E1E4E8\">({</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">      sourceId: targetNoteId,</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">      targetId: sourceNoteId,</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">      text: </span><span style=\"color:#9ECBFF\">`Referenced by: ${</span><span style=\"color:#79B8FF\">this</span><span style=\"color:#9ECBFF\">.</span><span style=\"color:#B392F0\">getNoteTitle</span><span style=\"color:#9ECBFF\">(</span><span style=\"color:#E1E4E8\">sourceNoteId</span><span style=\"color:#9ECBFF\">)</span><span style=\"color:#9ECBFF\">}`</span><span style=\"color:#E1E4E8\">,</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">      direction: </span><span style=\"color:#9ECBFF\">'backward'</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    });</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    </span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">    // Store both links in the graph</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">    this</span><span style=\"color:#E1E4E8\">.graphDb.</span><span style=\"color:#B392F0\">addLink</span><span style=\"color:#E1E4E8\">(forwardLink);</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">    this</span><span style=\"color:#E1E4E8\">.graphDb.</span><span style=\"color:#B392F0\">addLink</span><span style=\"color:#E1E4E8\">(backwardLink);</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    </span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">    // Update the note content to include the link</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">    this</span><span style=\"color:#E1E4E8\">.</span><span style=\"color:#B392F0\">updateNoteContent</span><span style=\"color:#E1E4E8\">(sourceNoteId, linkText, targetNoteId);</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    </span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    return</span><span style=\"color:#E1E4E8\"> forwardLink;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  }</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span>\n<span class=\"line\"></span></code></pre>\n<h2 id=\"current-status\">Current Status</h2>\n<p>The project is currently in active development with a working prototype that demonstrates the core functionality. The graph visualization and bidirectional linking features are operational, while advanced search capabilities are being refined.</p>\n<h2 id=\"next-steps\">Next Steps</h2>\n<ul>\n<li>Implement the encrypted sync functionality</li>\n<li>Enhance the graph visualization with filtering and focus modes</li>\n<li>Develop plugins system for extensibility</li>\n<li>Create templates for different knowledge management workflows</li>\n<li>Optimize performance for large knowledge bases</li>\n</ul>";

				const frontmatter = {"title":"Obsidian-Inspired Knowledge Management System","projectDate":"2024-02-20T00:00:00.000Z","status":"In Progress","featured":true,"tags":["Knowledge Management","Electron","React","TypeScript","Graph Database"],"ogImage":"/images/project-two-card.png","description":"A modern knowledge management system inspired by Obsidian, featuring bidirectional linking, graph visualization, and advanced search capabilities.","repoUrl":"https://github.com/username/knowledge-graph"};
				const file = "C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-two.md";
				const url = undefined;
				function rawContent() {
					return "\n## Project Overview\n\nThis project is developing a sophisticated knowledge management system inspired by tools like Obsidian and Roam Research. It focuses on creating a seamless experience for capturing, connecting, and retrieving information using a graph-based approach to knowledge.\n\n## Key Features\n\n- **Bidirectional Linking**: Create connections between notes that work in both directions, allowing for natural knowledge discovery.\n- **Graph Visualization**: Interactive visualization of your knowledge network, helping identify clusters and connections.\n- **Markdown-Based**: Simple yet powerful formatting using extended Markdown syntax.\n- **Local-First**: All data stored locally with optional encrypted sync.\n- **Customizable Workspace**: Flexible layout system with multiple panes and views.\n- **Advanced Search**: Full-text search with support for complex queries and filters.\n\n## Technical Implementation\n\nThe application is built using Electron for cross-platform compatibility, with a React frontend and TypeScript for type safety. The knowledge graph is stored in a specialized graph database optimized for quick traversal and querying.\n\n```typescript\n// Example of the bidirectional linking implementation\nclass NoteManager {\n  createLink(sourceNoteId: string, targetNoteId: string, linkText: string): Link {\n    // Create the forward link\n    const forwardLink = new Link({\n      sourceId: sourceNoteId,\n      targetId: targetNoteId,\n      text: linkText,\n      direction: 'forward'\n    });\n    \n    // Create the backward link\n    const backwardLink = new Link({\n      sourceId: targetNoteId,\n      targetId: sourceNoteId,\n      text: `Referenced by: ${this.getNoteTitle(sourceNoteId)}`,\n      direction: 'backward'\n    });\n    \n    // Store both links in the graph\n    this.graphDb.addLink(forwardLink);\n    this.graphDb.addLink(backwardLink);\n    \n    // Update the note content to include the link\n    this.updateNoteContent(sourceNoteId, linkText, targetNoteId);\n    \n    return forwardLink;\n  }\n}\n```\n\n## Current Status\n\nThe project is currently in active development with a working prototype that demonstrates the core functionality. The graph visualization and bidirectional linking features are operational, while advanced search capabilities are being refined.\n\n## Next Steps\n\n- Implement the encrypted sync functionality\n- Enhance the graph visualization with filtering and focus modes\n- Develop plugins system for extensibility\n- Create templates for different knowledge management workflows\n- Optimize performance for large knowledge bases\n";
				}
				function compiledContent() {
					return html;
				}
				function getHeadings() {
					return [{"depth":2,"slug":"project-overview","text":"Project Overview"},{"depth":2,"slug":"key-features","text":"Key Features"},{"depth":2,"slug":"technical-implementation","text":"Technical Implementation"},{"depth":2,"slug":"current-status","text":"Current Status"},{"depth":2,"slug":"next-steps","text":"Next Steps"}];
				}

				const Content = createComponent((result, _props, slots) => {
					const { layout, ...content } = frontmatter;
					content.file = file;
					content.url = url;

					return renderTemplate`${maybeRenderHead()}${unescapeHTML(html)}`;
				});

export { Content, compiledContent, Content as default, file, frontmatter, getHeadings, rawContent, url };

.prose {
    color: var(--theme-text);
    line-height: 1.7;
}

.prose > *:first-child {
    margin-top: 0;
}

.prose h1, .prose h2, .prose h3, .prose h4 {
    color: var(--theme-accent-2);
    margin: 2.5rem 0 1rem;
    line-height: 1.2;
}

.prose p {
    margin-bottom: 1.25rem;
}

.prose a {
    color: var(--theme-accent);
    text-decoration: underline;
    text-decoration-color: var(--theme-accent-2);
}

.prose blockquote {
    border-left: 4px solid var(--theme-accent);
    padding-left: 1rem;
    margin-left: 0;
    font-style: italic;
    color: var(--theme-text-light);
}

.prose pre {
    background-color: var(--theme-bg-offset);
    padding: 1rem;
    border-radius: var(--theme-border-radius);
    overflow-x: auto;
}

.prose code {
    font-family: var(--font-mono);
    background-color: var(--theme-bg-offset);
    padding: 0.2em 0.4em;
    font-size: 0.9em;
    border-radius: 4px;
}

.prose pre > code {
    background-color: transparent;
    padding: 0;
    font-size: 1em;
}
/* Add more styles for ul, ol, li, tables, etc. */
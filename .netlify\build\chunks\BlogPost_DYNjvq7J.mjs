import { c as createAstro, a as createComponent, d as renderComponent, r as renderTemplate, b as addAttribute, f as renderSlot, e as renderTransition, m as maybeRenderHead } from './astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import { $ as $$Layout } from './Layout_rXbp99fE.mjs';
import { $ as $$Tag } from './Tag_DSxhj6Zu.mjs';
import { s as slugifyStr } from './slugify_CHvHojPC.mjs';
/* empty css                          */
/* empty css                         */

var __freeze = Object.freeze;
var __defProp = Object.defineProperty;
var __template = (cooked, raw) => __freeze(__defProp(cooked, "raw", { value: __freeze(raw || cooked.slice()) }));
var _a;
const $$Astro = createAstro("https://pvb.com");
const $$BlogPost = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$BlogPost;
  const { post } = Astro2.props;
  const { title, author, description, ogImage, pubDatetime, tags } = post.data;
  pubDatetime.toISOString();
  const postDate = pubDatetime.toLocaleDateString("en-US", {
    day: "numeric",
    month: "long",
    year: "numeric"
  });
  const isWorkProject = "projectDate" in post.data || post.collection === "work";
  const pageType = isWorkProject ? "work" : "blog";
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "pageTitle": title, "isHomePage": false, "accentColor": "#f0f0f0", "bgColor": "rgba(0, 0, 0, 0.85)", "backgroundImageUrl": "/images/blackgranite.png", "bodyDataPage": isWorkProject ? "work-post" : "blog-post", "data-astro-cid-bvzihdzo": true }, { "default": ($$result2) => renderTemplate(_a || (_a = __template(["  ", '<div class="blog-header" data-astro-cid-bvzihdzo> <div class="blog-title" data-astro-cid-bvzihdzo>', '</div> </div>   <button id="toc-toggle" class="toc-toggle active" aria-expanded="true" aria-controls="toc-panel" data-astro-cid-bvzihdzo> <span class="toc-icon-dots" data-astro-cid-bvzihdzo> <span class="dot" data-astro-cid-bvzihdzo></span> <span class="dot" data-astro-cid-bvzihdzo></span> <span class="dot" data-astro-cid-bvzihdzo></span> </span> </button>  <div id="toc-panel" class="toc-panel active" aria-hidden="false" data-astro-cid-bvzihdzo> <div class="toc-panel-inner" data-astro-cid-bvzihdzo> <h3 class="toc-panel-title" data-astro-cid-bvzihdzo>Table of Contents</h3> <button class="toc-close" id="toc-close" aria-label="Close Table of Contents" data-astro-cid-bvzihdzo> <span class="toc-close-arrow" data-astro-cid-bvzihdzo>\u203A</span> </button> <div id="toc-content" class="toc-content" data-astro-cid-bvzihdzo> <!-- The TOC content will be populated via JavaScript --> </div> </div> </div>  <article class="blog-post" data-astro-cid-bvzihdzo> <header class="post-header" data-astro-cid-bvzihdzo> <h1 class="post-title" data-astro-cid-bvzihdzo', "> ", ' </h1> <div class="post-date" data-astro-cid-bvzihdzo>', '</div> </header> <div class="post-content" data-astro-cid-bvzihdzo> ', ' </div> <div class="post-footer" data-astro-cid-bvzihdzo> <div class="post-tags" data-astro-cid-bvzihdzo> ', ' </div> <div class="post-actions" data-astro-cid-bvzihdzo> <a', ' class="return-link" data-astro-cid-bvzihdzo>\u2190 ', "</a> <a href=\"#\" class=\"subscribe-link\" data-astro-cid-bvzihdzo>subscribe by email</a> </div> </div> </article> <script>\n    // TOC functionality\n    document.addEventListener('DOMContentLoaded', function() {\n      const tocToggle = document.getElementById('toc-toggle');\n      const tocPanel = document.getElementById('toc-panel');\n      const tocContent = document.getElementById('toc-content');\n      const tocClose = document.getElementById('toc-close');\n      const blogPost = document.querySelector('.blog-post');\n      const isWorkPost = document.body.getAttribute('data-page') === 'work-post';\n\n      // Set return link text based on page type\n      const returnLink = document.querySelector('.return-link');\n      if (returnLink && isWorkPost) {\n        returnLink.textContent = '\u2190 Work';\n        returnLink.setAttribute('href', '/work');\n      }\n\n      // Prevent automatic scrolling to bottom\n      window.history.scrollRestoration = 'manual';\n\n      // Generate TOC from headings in the post\n      function generateToc() {\n        const headings = document.querySelectorAll('.post-content h2, .post-content h3, .post-content h4');\n        if (headings.length === 0) {\n          tocContent.innerHTML = '<p class=\"toc-empty\">No sections found</p>';\n          return;\n        }\n\n        const tocHtml = document.createElement('div');\n        tocHtml.classList.add('toc-list-container');\n\n        headings.forEach((heading, index) => {\n          // Add an ID to the heading if it doesn't have one\n          if (!heading.id) {\n            heading.id = `heading-${index}`;\n          }\n\n          const item = document.createElement('div');\n          item.classList.add(`toc-item`);\n          item.classList.add(`toc-${heading.tagName.toLowerCase()}`);\n\n          const a = document.createElement('a');\n          a.href = `#${heading.id}`;\n          a.textContent = heading.textContent;\n          a.style.color = '#ffffff'; // Force white color\n          a.style.textDecoration = 'none'; // Force no underline\n\n          a.addEventListener('click', function(e) {\n            e.preventDefault();\n\n            // Remove active class from all TOC links\n            document.querySelectorAll('.toc-content a').forEach(link => {\n              link.classList.remove('active');\n            });\n\n            // Add active class to clicked link\n            a.classList.add('active');\n\n            // Smooth scroll to the heading\n            heading.scrollIntoView({ behavior: 'smooth' });\n\n            // Add a highlight effect to the heading\n            heading.classList.add('highlight');\n            setTimeout(() => {\n              heading.classList.remove('highlight');\n            }, 1500);\n\n            // On mobile, close the TOC after clicking\n            if (window.innerWidth < 768) {\n              toggleToc(false);\n            }\n          });\n\n          item.appendChild(a);\n          tocHtml.appendChild(item);\n        });\n\n        tocContent.innerHTML = '';\n        tocContent.appendChild(tocHtml);\n\n        // Initial styling of all links\n        document.querySelectorAll('.toc-content a').forEach(link => {\n          link.style.color = '#ffffff';\n          link.style.textDecoration = 'none';\n        });\n\n        // Highlight the current section on scroll\n        window.addEventListener('scroll', highlightCurrentSection);\n\n        // Initial highlight\n        setTimeout(highlightCurrentSection, 100);\n      }\n\n      // Highlight the current section in the TOC\n      function highlightCurrentSection() {\n        const headings = document.querySelectorAll('.post-content h2, .post-content h3, .post-content h4');\n        if (!headings.length) return;\n\n        // Find the heading that's currently in view\n        let currentHeading = null;\n\n        // Calculate viewport height and set threshold for heading detection\n        const viewportHeight = window.innerHeight;\n        const threshold = viewportHeight * 0.3; // 30% from the top\n\n        // Check which heading is most visible in the viewport\n        let maxVisibleHeight = 0;\n        let mostVisibleHeading = null;\n\n        headings.forEach(heading => {\n          const rect = heading.getBoundingClientRect();\n\n          // Skip if the heading is completely below or above the viewport\n          if (rect.top > viewportHeight || rect.bottom < 0) return;\n\n          // Calculate how much of the heading is visible\n          const visibleTop = Math.max(0, rect.top);\n          const visibleBottom = Math.min(viewportHeight, rect.bottom);\n          const visibleHeight = visibleBottom - visibleTop;\n\n          // If this heading is more visible than previous ones, or it's near the top threshold\n          if (visibleHeight > maxVisibleHeight || (rect.top < threshold && rect.top > 0)) {\n            maxVisibleHeight = visibleHeight;\n            mostVisibleHeading = heading;\n          }\n        });\n\n        // If we found a visible heading, use it\n        if (mostVisibleHeading) {\n          currentHeading = mostVisibleHeading;\n        }\n        // If no heading is visible and we've scrolled down, use the last one\n        else if (window.scrollY > 0 && headings.length > 0) {\n          currentHeading = headings[headings.length - 1];\n        }\n        // Otherwise use the first one\n        else if (headings.length > 0) {\n          currentHeading = headings[0];\n        }\n\n        // Remove active class from all TOC links\n        document.querySelectorAll('.toc-content a').forEach(link => {\n          link.classList.remove('active');\n        });\n\n        // If we found a current heading, highlight its TOC link\n        if (currentHeading) {\n          const id = currentHeading.id;\n          const tocLink = document.querySelector(`.toc-content a[href=\"#${id}\"]`);\n          if (tocLink) {\n            tocLink.classList.add('active');\n\n            // Ensure the active link is visible in the TOC panel if it's open\n            if (tocPanel.classList.contains('active')) {\n              const tocPanelInner = document.querySelector('.toc-panel-inner');\n              if (tocPanelInner) {\n                const linkRect = tocLink.getBoundingClientRect();\n                const panelRect = tocPanelInner.getBoundingClientRect();\n\n                // If link is outside the visible area of the panel\n                if (linkRect.top < panelRect.top || linkRect.bottom > panelRect.bottom) {\n                  tocLink.scrollIntoView({ behavior: 'smooth', block: 'center' });\n                }\n              }\n            }\n          }\n        }\n      }\n\n      // Toggle TOC panel with smooth transitions\n      function toggleToc(forceState) {\n        const isOpen = forceState !== undefined ? forceState : !tocPanel.classList.contains('active');\n\n        if (isOpen) {\n          // Opening\n          tocPanel.classList.add('active');\n          tocToggle.classList.add('active');\n          tocToggle.setAttribute('aria-expanded', 'true');\n          tocPanel.setAttribute('aria-hidden', 'false');\n          document.body.classList.add('toc-active');\n\n          // Generate TOC when opening\n          generateToc();\n        } else {\n          // Closing\n          tocPanel.classList.remove('active');\n          tocToggle.classList.remove('active');\n          tocToggle.setAttribute('aria-expanded', 'false');\n          tocPanel.setAttribute('aria-hidden', 'true');\n          document.body.classList.remove('toc-active');\n        }\n      }\n\n      // Add click event to TOC toggle - inverse logic to fix behavior\n      if (tocToggle) {\n        tocToggle.addEventListener('click', () => {\n          const currentState = tocPanel.classList.contains('active');\n          toggleToc(!currentState);\n        });\n      }\n\n      // Add click event to TOC close button\n      if (tocClose) {\n        tocClose.addEventListener('click', function() {\n          toggleToc(false);\n        });\n      }\n\n      // Close TOC when clicking outside\n      document.addEventListener('click', (e) => {\n        if (tocPanel.classList.contains('active') &&\n            !tocPanel.contains(e.target) &&\n            !tocToggle.contains(e.target) &&\n            !tocClose.contains(e.target)) {\n          toggleToc(false);\n        }\n      });\n\n      // Close TOC with ESC key\n      document.addEventListener('keydown', (e) => {\n        if (e.key === 'Escape' && tocPanel.classList.contains('active')) {\n          toggleToc(false);\n        }\n      });\n\n      // Set up back button event\n      const backButton = document.querySelector('.nav-circle.top-left');\n      if (backButton) {\n        backButton.addEventListener('click', () => {\n          window.history.back();\n        });\n      }\n\n      // Make the bottom button slightly transparent on scroll\n      const bottomButton = document.querySelector('.nav-circle.bottom-center');\n\n      window.addEventListener('scroll', function() {\n        if (window.scrollY > 100) {\n          bottomButton.style.opacity = \"0.7\";\n        } else {\n          bottomButton.style.opacity = \"1\";\n        }\n      });\n\n      // Generate TOC on page load only if it should be open\n      if (window.innerWidth > 768) {\n        generateToc();\n      } else {\n        // On mobile, start with TOC closed\n        toggleToc(false);\n      }\n    });\n  <\/script> "], ["  ", '<div class="blog-header" data-astro-cid-bvzihdzo> <div class="blog-title" data-astro-cid-bvzihdzo>', '</div> </div>   <button id="toc-toggle" class="toc-toggle active" aria-expanded="true" aria-controls="toc-panel" data-astro-cid-bvzihdzo> <span class="toc-icon-dots" data-astro-cid-bvzihdzo> <span class="dot" data-astro-cid-bvzihdzo></span> <span class="dot" data-astro-cid-bvzihdzo></span> <span class="dot" data-astro-cid-bvzihdzo></span> </span> </button>  <div id="toc-panel" class="toc-panel active" aria-hidden="false" data-astro-cid-bvzihdzo> <div class="toc-panel-inner" data-astro-cid-bvzihdzo> <h3 class="toc-panel-title" data-astro-cid-bvzihdzo>Table of Contents</h3> <button class="toc-close" id="toc-close" aria-label="Close Table of Contents" data-astro-cid-bvzihdzo> <span class="toc-close-arrow" data-astro-cid-bvzihdzo>\u203A</span> </button> <div id="toc-content" class="toc-content" data-astro-cid-bvzihdzo> <!-- The TOC content will be populated via JavaScript --> </div> </div> </div>  <article class="blog-post" data-astro-cid-bvzihdzo> <header class="post-header" data-astro-cid-bvzihdzo> <h1 class="post-title" data-astro-cid-bvzihdzo', "> ", ' </h1> <div class="post-date" data-astro-cid-bvzihdzo>', '</div> </header> <div class="post-content" data-astro-cid-bvzihdzo> ', ' </div> <div class="post-footer" data-astro-cid-bvzihdzo> <div class="post-tags" data-astro-cid-bvzihdzo> ', ' </div> <div class="post-actions" data-astro-cid-bvzihdzo> <a', ' class="return-link" data-astro-cid-bvzihdzo>\u2190 ', "</a> <a href=\"#\" class=\"subscribe-link\" data-astro-cid-bvzihdzo>subscribe by email</a> </div> </div> </article> <script>\n    // TOC functionality\n    document.addEventListener('DOMContentLoaded', function() {\n      const tocToggle = document.getElementById('toc-toggle');\n      const tocPanel = document.getElementById('toc-panel');\n      const tocContent = document.getElementById('toc-content');\n      const tocClose = document.getElementById('toc-close');\n      const blogPost = document.querySelector('.blog-post');\n      const isWorkPost = document.body.getAttribute('data-page') === 'work-post';\n\n      // Set return link text based on page type\n      const returnLink = document.querySelector('.return-link');\n      if (returnLink && isWorkPost) {\n        returnLink.textContent = '\u2190 Work';\n        returnLink.setAttribute('href', '/work');\n      }\n\n      // Prevent automatic scrolling to bottom\n      window.history.scrollRestoration = 'manual';\n\n      // Generate TOC from headings in the post\n      function generateToc() {\n        const headings = document.querySelectorAll('.post-content h2, .post-content h3, .post-content h4');\n        if (headings.length === 0) {\n          tocContent.innerHTML = '<p class=\"toc-empty\">No sections found</p>';\n          return;\n        }\n\n        const tocHtml = document.createElement('div');\n        tocHtml.classList.add('toc-list-container');\n\n        headings.forEach((heading, index) => {\n          // Add an ID to the heading if it doesn't have one\n          if (!heading.id) {\n            heading.id = \\`heading-\\${index}\\`;\n          }\n\n          const item = document.createElement('div');\n          item.classList.add(\\`toc-item\\`);\n          item.classList.add(\\`toc-\\${heading.tagName.toLowerCase()}\\`);\n\n          const a = document.createElement('a');\n          a.href = \\`#\\${heading.id}\\`;\n          a.textContent = heading.textContent;\n          a.style.color = '#ffffff'; // Force white color\n          a.style.textDecoration = 'none'; // Force no underline\n\n          a.addEventListener('click', function(e) {\n            e.preventDefault();\n\n            // Remove active class from all TOC links\n            document.querySelectorAll('.toc-content a').forEach(link => {\n              link.classList.remove('active');\n            });\n\n            // Add active class to clicked link\n            a.classList.add('active');\n\n            // Smooth scroll to the heading\n            heading.scrollIntoView({ behavior: 'smooth' });\n\n            // Add a highlight effect to the heading\n            heading.classList.add('highlight');\n            setTimeout(() => {\n              heading.classList.remove('highlight');\n            }, 1500);\n\n            // On mobile, close the TOC after clicking\n            if (window.innerWidth < 768) {\n              toggleToc(false);\n            }\n          });\n\n          item.appendChild(a);\n          tocHtml.appendChild(item);\n        });\n\n        tocContent.innerHTML = '';\n        tocContent.appendChild(tocHtml);\n\n        // Initial styling of all links\n        document.querySelectorAll('.toc-content a').forEach(link => {\n          link.style.color = '#ffffff';\n          link.style.textDecoration = 'none';\n        });\n\n        // Highlight the current section on scroll\n        window.addEventListener('scroll', highlightCurrentSection);\n\n        // Initial highlight\n        setTimeout(highlightCurrentSection, 100);\n      }\n\n      // Highlight the current section in the TOC\n      function highlightCurrentSection() {\n        const headings = document.querySelectorAll('.post-content h2, .post-content h3, .post-content h4');\n        if (!headings.length) return;\n\n        // Find the heading that's currently in view\n        let currentHeading = null;\n\n        // Calculate viewport height and set threshold for heading detection\n        const viewportHeight = window.innerHeight;\n        const threshold = viewportHeight * 0.3; // 30% from the top\n\n        // Check which heading is most visible in the viewport\n        let maxVisibleHeight = 0;\n        let mostVisibleHeading = null;\n\n        headings.forEach(heading => {\n          const rect = heading.getBoundingClientRect();\n\n          // Skip if the heading is completely below or above the viewport\n          if (rect.top > viewportHeight || rect.bottom < 0) return;\n\n          // Calculate how much of the heading is visible\n          const visibleTop = Math.max(0, rect.top);\n          const visibleBottom = Math.min(viewportHeight, rect.bottom);\n          const visibleHeight = visibleBottom - visibleTop;\n\n          // If this heading is more visible than previous ones, or it's near the top threshold\n          if (visibleHeight > maxVisibleHeight || (rect.top < threshold && rect.top > 0)) {\n            maxVisibleHeight = visibleHeight;\n            mostVisibleHeading = heading;\n          }\n        });\n\n        // If we found a visible heading, use it\n        if (mostVisibleHeading) {\n          currentHeading = mostVisibleHeading;\n        }\n        // If no heading is visible and we've scrolled down, use the last one\n        else if (window.scrollY > 0 && headings.length > 0) {\n          currentHeading = headings[headings.length - 1];\n        }\n        // Otherwise use the first one\n        else if (headings.length > 0) {\n          currentHeading = headings[0];\n        }\n\n        // Remove active class from all TOC links\n        document.querySelectorAll('.toc-content a').forEach(link => {\n          link.classList.remove('active');\n        });\n\n        // If we found a current heading, highlight its TOC link\n        if (currentHeading) {\n          const id = currentHeading.id;\n          const tocLink = document.querySelector(\\`.toc-content a[href=\"#\\${id}\"]\\`);\n          if (tocLink) {\n            tocLink.classList.add('active');\n\n            // Ensure the active link is visible in the TOC panel if it's open\n            if (tocPanel.classList.contains('active')) {\n              const tocPanelInner = document.querySelector('.toc-panel-inner');\n              if (tocPanelInner) {\n                const linkRect = tocLink.getBoundingClientRect();\n                const panelRect = tocPanelInner.getBoundingClientRect();\n\n                // If link is outside the visible area of the panel\n                if (linkRect.top < panelRect.top || linkRect.bottom > panelRect.bottom) {\n                  tocLink.scrollIntoView({ behavior: 'smooth', block: 'center' });\n                }\n              }\n            }\n          }\n        }\n      }\n\n      // Toggle TOC panel with smooth transitions\n      function toggleToc(forceState) {\n        const isOpen = forceState !== undefined ? forceState : !tocPanel.classList.contains('active');\n\n        if (isOpen) {\n          // Opening\n          tocPanel.classList.add('active');\n          tocToggle.classList.add('active');\n          tocToggle.setAttribute('aria-expanded', 'true');\n          tocPanel.setAttribute('aria-hidden', 'false');\n          document.body.classList.add('toc-active');\n\n          // Generate TOC when opening\n          generateToc();\n        } else {\n          // Closing\n          tocPanel.classList.remove('active');\n          tocToggle.classList.remove('active');\n          tocToggle.setAttribute('aria-expanded', 'false');\n          tocPanel.setAttribute('aria-hidden', 'true');\n          document.body.classList.remove('toc-active');\n        }\n      }\n\n      // Add click event to TOC toggle - inverse logic to fix behavior\n      if (tocToggle) {\n        tocToggle.addEventListener('click', () => {\n          const currentState = tocPanel.classList.contains('active');\n          toggleToc(!currentState);\n        });\n      }\n\n      // Add click event to TOC close button\n      if (tocClose) {\n        tocClose.addEventListener('click', function() {\n          toggleToc(false);\n        });\n      }\n\n      // Close TOC when clicking outside\n      document.addEventListener('click', (e) => {\n        if (tocPanel.classList.contains('active') &&\n            !tocPanel.contains(e.target) &&\n            !tocToggle.contains(e.target) &&\n            !tocClose.contains(e.target)) {\n          toggleToc(false);\n        }\n      });\n\n      // Close TOC with ESC key\n      document.addEventListener('keydown', (e) => {\n        if (e.key === 'Escape' && tocPanel.classList.contains('active')) {\n          toggleToc(false);\n        }\n      });\n\n      // Set up back button event\n      const backButton = document.querySelector('.nav-circle.top-left');\n      if (backButton) {\n        backButton.addEventListener('click', () => {\n          window.history.back();\n        });\n      }\n\n      // Make the bottom button slightly transparent on scroll\n      const bottomButton = document.querySelector('.nav-circle.bottom-center');\n\n      window.addEventListener('scroll', function() {\n        if (window.scrollY > 100) {\n          bottomButton.style.opacity = \"0.7\";\n        } else {\n          bottomButton.style.opacity = \"1\";\n        }\n      });\n\n      // Generate TOC on page load only if it should be open\n      if (window.innerWidth > 768) {\n        generateToc();\n      } else {\n        // On mobile, start with TOC closed\n        toggleToc(false);\n      }\n    });\n  <\/script> "])), maybeRenderHead(), pageType, addAttribute(renderTransition($$result2, "a4rjachf", "", slugifyStr(title)), "data-astro-transition-scope"), title, postDate, renderSlot($$result2, $$slots["default"]), tags.map((tag) => renderTemplate`${renderComponent($$result2, "Tag", $$Tag, { "tag": slugifyStr(tag), "tagName": tag, "data-astro-cid-bvzihdzo": true })}`), addAttribute(`/${pageType}`, "href"), pageType.charAt(0).toUpperCase() + pageType.slice(1)) })} `;
}, "C:/Users/<USER>/Desktop/pvb-astro/src/layouts/BlogPost.astro", "self");

export { $$BlogPost as $ };

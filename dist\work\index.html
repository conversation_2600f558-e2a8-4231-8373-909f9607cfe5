<!DOCTYPE html><html lang="en" data-astro-cid-sckkx6r4> <head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>Work &amp; Research | PVB</title><link rel="stylesheet" href="/_astro/_slug_.CSlrdnFy.css">
<link rel="stylesheet" href="/_astro/work.CDh7lM52.css">
<style>.tag-link[data-astro-cid-blwjyjpt]{display:inline-block;padding:.25rem .75rem;border-radius:1rem;text-decoration:none;border:1px solid rgba(240,240,240,.4);color:#f0f0f0cc;transition:all .2s ease;font-family:Georgia Custom,Georgia,serif}.tag-link[data-astro-cid-blwjyjpt]:hover{background-color:#f0f0f01a;border-color:#f0f0f099;color:#f0f0f0}.tag-sm[data-astro-cid-blwjyjpt]{font-size:.75rem}.tag-lg[data-astro-cid-blwjyjpt]{font-size:.875rem}
@keyframes astroFadeInOut{0%{opacity:1}to{opacity:0}}@keyframes astroFadeIn{0%{opacity:0;mix-blend-mode:plus-lighter}to{opacity:1;mix-blend-mode:plus-lighter}}@keyframes astroFadeOut{0%{opacity:1;mix-blend-mode:plus-lighter}to{opacity:0;mix-blend-mode:plus-lighter}}@keyframes astroSlideFromRight{0%{transform:translate(100%)}}@keyframes astroSlideFromLeft{0%{transform:translate(-100%)}}@keyframes astroSlideToRight{to{transform:translate(100%)}}@keyframes astroSlideToLeft{to{transform:translate(-100%)}}@media (prefers-reduced-motion){::view-transition-group(*),::view-transition-old(*),::view-transition-new(*){animation:none!important}[data-astro-transition-scope]{animation:none!important}}
</style><style>[data-astro-transition-scope="astro-2x7bbjfa-1"] { view-transition-name: synapse-drive-centralized-resource-platform; }@layer astro { ::view-transition-old(synapse-drive-centralized-resource-platform) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }::view-transition-new(synapse-drive-centralized-resource-platform) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }[data-astro-transition=back]::view-transition-old(synapse-drive-centralized-resource-platform) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition=back]::view-transition-new(synapse-drive-centralized-resource-platform) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; } }[data-astro-transition-fallback="old"] [data-astro-transition-scope="astro-2x7bbjfa-1"],
			[data-astro-transition-fallback="old"][data-astro-transition-scope="astro-2x7bbjfa-1"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition-fallback="new"] [data-astro-transition-scope="astro-2x7bbjfa-1"],
			[data-astro-transition-fallback="new"][data-astro-transition-scope="astro-2x7bbjfa-1"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }[data-astro-transition=back][data-astro-transition-fallback="old"] [data-astro-transition-scope="astro-2x7bbjfa-1"],
			[data-astro-transition=back][data-astro-transition-fallback="old"][data-astro-transition-scope="astro-2x7bbjfa-1"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition=back][data-astro-transition-fallback="new"] [data-astro-transition-scope="astro-2x7bbjfa-1"],
			[data-astro-transition=back][data-astro-transition-fallback="new"][data-astro-transition-scope="astro-2x7bbjfa-1"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }</style><style>[data-astro-transition-scope="astro-2x7bbjfa-2"] { view-transition-name: leads-wizard-aidriven-lead-qualification; }@layer astro { ::view-transition-old(leads-wizard-aidriven-lead-qualification) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }::view-transition-new(leads-wizard-aidriven-lead-qualification) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }[data-astro-transition=back]::view-transition-old(leads-wizard-aidriven-lead-qualification) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition=back]::view-transition-new(leads-wizard-aidriven-lead-qualification) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; } }[data-astro-transition-fallback="old"] [data-astro-transition-scope="astro-2x7bbjfa-2"],
			[data-astro-transition-fallback="old"][data-astro-transition-scope="astro-2x7bbjfa-2"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition-fallback="new"] [data-astro-transition-scope="astro-2x7bbjfa-2"],
			[data-astro-transition-fallback="new"][data-astro-transition-scope="astro-2x7bbjfa-2"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }[data-astro-transition=back][data-astro-transition-fallback="old"] [data-astro-transition-scope="astro-2x7bbjfa-2"],
			[data-astro-transition=back][data-astro-transition-fallback="old"][data-astro-transition-scope="astro-2x7bbjfa-2"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition=back][data-astro-transition-fallback="new"] [data-astro-transition-scope="astro-2x7bbjfa-2"],
			[data-astro-transition=back][data-astro-transition-fallback="new"][data-astro-transition-scope="astro-2x7bbjfa-2"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }</style><style>[data-astro-transition-scope="astro-2x7bbjfa-3"] { view-transition-name: recursive-self-referential-compression-rsrc-ais-survival-map; }@layer astro { ::view-transition-old(recursive-self-referential-compression-rsrc-ais-survival-map) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }::view-transition-new(recursive-self-referential-compression-rsrc-ais-survival-map) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }[data-astro-transition=back]::view-transition-old(recursive-self-referential-compression-rsrc-ais-survival-map) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition=back]::view-transition-new(recursive-self-referential-compression-rsrc-ais-survival-map) { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; } }[data-astro-transition-fallback="old"] [data-astro-transition-scope="astro-2x7bbjfa-3"],
			[data-astro-transition-fallback="old"][data-astro-transition-scope="astro-2x7bbjfa-3"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition-fallback="new"] [data-astro-transition-scope="astro-2x7bbjfa-3"],
			[data-astro-transition-fallback="new"][data-astro-transition-scope="astro-2x7bbjfa-3"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }[data-astro-transition=back][data-astro-transition-fallback="old"] [data-astro-transition-scope="astro-2x7bbjfa-3"],
			[data-astro-transition=back][data-astro-transition-fallback="old"][data-astro-transition-scope="astro-2x7bbjfa-3"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeOut; }[data-astro-transition=back][data-astro-transition-fallback="new"] [data-astro-transition-scope="astro-2x7bbjfa-3"],
			[data-astro-transition=back][data-astro-transition-fallback="new"][data-astro-transition-scope="astro-2x7bbjfa-3"] { 
	animation-duration: 180ms;
	animation-timing-function: cubic-bezier(0.76, 0, 0.24, 1);
	animation-fill-mode: both;
	animation-name: astroFadeIn; }</style></head> <body data-page="work" style="--color-accent: #f0f0f0; --color-bg: rgba(0, 0, 0, 0.88); background-image: url(/images/obsidian.png); background-color: rgba(0, 0, 0, 0.88);" data-astro-cid-sckkx6r4> <div class="page-transition" id="page-transition" data-astro-cid-sckkx6r4></div> <!-- Common logo for all pages --> <a href="/" class="logo" data-astro-cid-sckkx6r4>pvb</a> <div class="content-wrapper" data-astro-cid-sckkx6r4> <!-- Slot for page-specific content -->   <div class="work-header" data-astro-cid-jljc7dey> <h1 class="work-title" data-astro-cid-jljc7dey>work</h1> </div> <div class="content-container" data-astro-cid-jljc7dey> <!-- Featured Projects Section -->  <!-- Projects Section with refined, elegant styling --> <section class="projects-section" data-astro-cid-jljc7dey> <div class="section-header" data-astro-cid-jljc7dey> <div class="section-line" data-astro-cid-jljc7dey></div> <h2 class="section-title" data-astro-cid-jljc7dey>Projects</h2> <div class="section-line" data-astro-cid-jljc7dey></div> </div> <div class="projects-grid" data-astro-cid-jljc7dey> <article class="project-card standard" data-astro-cid-mspuyifq><a href="/work/synapse-drive-centralized-resource-platform" class="project-link" data-astro-cid-mspuyifq><div class="project-content" data-astro-cid-mspuyifq><h2 class="project-title" data-astro-cid-mspuyifq data-astro-transition-scope="astro-2x7bbjfa-1">Synapse Drive – Centralized Resource Platform</h2><p class="project-description" data-astro-cid-mspuyifq>Built a Notion-meets-GitHub hub for knowledge sovereignty.
</p><div class="project-footer" data-astro-cid-mspuyifq><div class="project-meta" data-astro-cid-mspuyifq><time datetime="2025-04-18T07:33:40.000Z" data-astro-cid-mspuyifq>Apr 2025</time></div><div class="project-tags-container" data-astro-cid-mspuyifq><div class="project-tags small" data-astro-cid-mspuyifq><a href="/tags/work" class="tag-link tag-sm" data-astro-cid-blwjyjpt>
#work </a> </div></div></div></div></a></article><article class="project-card standard" data-astro-cid-mspuyifq><a href="/work/leads-wizard-ai-driven-lead-qualification" class="project-link" data-astro-cid-mspuyifq><div class="project-content" data-astro-cid-mspuyifq><h2 class="project-title" data-astro-cid-mspuyifq data-astro-transition-scope="astro-2x7bbjfa-2">Leads Wizard – AI‑Driven Lead Qualification</h2><p class="project-description" data-astro-cid-mspuyifq>Used LLM orchestration to rank and qualify sales leads with dynamic input vectors.
</p><div class="project-footer" data-astro-cid-mspuyifq><div class="project-meta" data-astro-cid-mspuyifq><time datetime="2025-04-18T07:33:04.000Z" data-astro-cid-mspuyifq>Apr 2025</time></div><div class="project-tags-container" data-astro-cid-mspuyifq><div class="project-tags small" data-astro-cid-mspuyifq><a href="/tags/ai" class="tag-link tag-sm" data-astro-cid-blwjyjpt>
#AI </a> <a href="/tags/work" class="tag-link tag-sm" data-astro-cid-blwjyjpt>
#work </a> </div></div></div></div></a></article><article class="project-card standard" data-astro-cid-mspuyifq><a href="/work/recursive-self-referential-compression-rsrc-ais-survival-map" class="project-link" data-astro-cid-mspuyifq><div class="project-content" data-astro-cid-mspuyifq><h2 class="project-title" data-astro-cid-mspuyifq data-astro-transition-scope="astro-2x7bbjfa-3">Recursive Self-Referential Compression (RSRC): AI’s Survival Map</h2><p class="project-description" data-astro-cid-mspuyifq>A dual-metric framework for sustainable AI that prioritizes efficiency over brute-force scaling.</p><div class="project-footer" data-astro-cid-mspuyifq><div class="project-meta" data-astro-cid-mspuyifq><time datetime="2025-04-18T07:32:37.000Z" data-astro-cid-mspuyifq>Apr 2025</time></div><div class="project-tags-container" data-astro-cid-mspuyifq><div class="project-tags small" data-astro-cid-mspuyifq><a href="/tags/ai" class="tag-link tag-sm" data-astro-cid-blwjyjpt>
#AI </a> <a href="/tags/rsrc" class="tag-link tag-sm" data-astro-cid-blwjyjpt>
#RSRC </a> <a href="/tags/work" class="tag-link tag-sm" data-astro-cid-blwjyjpt>
#work </a> </div></div></div></div></a></article> </div> </section> <!-- Show a message if no projects are found -->  <!-- Publications Section with refined styling --> <section class="publications-section" data-astro-cid-jljc7dey> <div class="section-header" data-astro-cid-jljc7dey> <div class="section-line" data-astro-cid-jljc7dey></div> <h2 class="section-title" data-astro-cid-jljc7dey>Research & Publications</h2> <div class="section-line" data-astro-cid-jljc7dey></div> </div> <ul class="publications-list" data-astro-cid-jljc7dey> <li class="publication-item" data-astro-cid-jljc7dey> <h3 class="pub-title" data-astro-cid-jljc7dey>Recursive Self-Referential Compression (RSRC): AI&#39;s Survival Map in the Post-Scaling Era</h3> <div class="pub-meta" data-astro-cid-jljc7dey> <p class="pub-details" data-astro-cid-jljc7dey>MURST Research Initiative, 2025</p> <div class="pub-links" data-astro-cid-jljc7dey> <a href="/pdfs/RSRC_Paper.pdf" target="_blank" rel="noopener noreferrer" class="pub-link" data-astro-cid-jljc7dey>
PDF <span class="arrow" data-astro-cid-jljc7dey>↗</span> </a>  </div> </div> </li><li class="publication-item" data-astro-cid-jljc7dey> <h3 class="pub-title" data-astro-cid-jljc7dey>Cordyceps militaris: Culturing, Optimization and Bioactive Compound Analysis</h3> <div class="pub-meta" data-astro-cid-jljc7dey> <p class="pub-details" data-astro-cid-jljc7dey>Research Paper (IEEE format), 2023</p> <div class="pub-links" data-astro-cid-jljc7dey> <a href="/pdfs/Cordyceps_Paper.pdf" target="_blank" rel="noopener noreferrer" class="pub-link" data-astro-cid-jljc7dey>
PDF <span class="arrow" data-astro-cid-jljc7dey>↗</span> </a>  </div> </div> </li> </ul> </section> </div>  </div> <div class="quote-card" id="quote-card" data-astro-cid-sckkx6r4> <h3 class="quote-card-title" data-astro-cid-sckkx6r4></h3> <p class="quote-card-subtitle" data-astro-cid-sckkx6r4></p> </div> <!-- Include navigation on all pages --> <!-- Top-Left Nav Circle --><div class="nav-circle top-left" title="Back" aria-label="Go Back" data-astro-cid-pux6a34n> <span class="nav-icon" data-astro-cid-pux6a34n></span> <!-- CSS handles the '?' or '←' --> </div>   <!-- Note: Bottom-center nav circle is now handled by Layout.astro --> <script>
  // Initialize navigation functionality
  document.addEventListener('DOMContentLoaded', function() {
    const topLeftNav = document.querySelector('.nav-circle.top-left');
    const isHomePage = document.body.getAttribute('data-page') === 'home';

    if (topLeftNav) {
      topLeftNav.addEventListener('click', function() {
        if (isHomePage) {
          // On home page, go to about page
          window.location.href = '/about';
        } else {
          // On other pages, go back
          window.history.back();
        }
      });
    }
  });
</script>  <!-- Always include the bottom navigation button --> <div class="nav-circle bottom-center" id="menu-toggle" title="Menu" aria-label="Toggle Menu" data-astro-cid-sckkx6r4> <span class="nav-icon" data-astro-cid-sckkx6r4></span> </div> <!-- Include main menu on all pages --> <div class="main-menu" id="main-menu"> <a href="/" class="logo menu-logo">pvb</a> <div class="menu-wrapper"> <a href="/blog" data-nav>blog</a> <a href="/work" data-nav>work</a> <a href="/about" data-nav>about</a> <a href="/fitness" data-nav>fitness</a> <a href="/cv" class="side-menu-item left" data-nav>cv</a> <a href="/links" class="side-menu-item right" data-nav>links</a> <a href="/more" class="see-more" data-nav>
more
<span class="arrow">↓</span> </a> </div> </div> <script>
        // Function to ensure proper scrolling behavior
        document.addEventListener('DOMContentLoaded', function() {
            // Remove the transition overlay once page loads
            const pageTransition = document.getElementById('page-transition');
            setTimeout(() => {
                if (pageTransition) {
                    pageTransition.classList.remove('active');
                }
            }, 400);

            // Make sure scrollbar positioning is always at the edge
            const bodyEl = document.body;
            const pageType = bodyEl.getAttribute('data-page');

            // Add specific adjustments based on page type
            if (pageType === 'blog' || pageType === 'blog-post' || pageType === 'about' || pageType === 'work' || pageType === 'work-post') {
                bodyEl.style.overflowY = 'auto';
                bodyEl.style.height = 'auto';
                document.documentElement.style.overflowY = 'auto';
                document.documentElement.style.height = 'auto';

                // Special handling for full-page content on blog and blog post pages
                if (pageType === 'blog' || pageType === 'blog-post' || pageType === 'work' || pageType === 'work-post') {
                    const contentWrapper = document.querySelector('.content-wrapper');
                    if (contentWrapper) {
                        contentWrapper.style.position = 'relative';
                        contentWrapper.style.height = 'auto';
                        contentWrapper.style.minHeight = '100vh';
                    }
                }

                // Restore scroll position if it was saved (for navigation back)
                if (sessionStorage.getItem(`scrollPos-${pageType}`)) {
                    const savedScrollPos = parseInt(sessionStorage.getItem(`scrollPos-${pageType}`));
                    setTimeout(() => {
                        window.scrollTo(0, savedScrollPos);
                    }, 100);
                }

                // Save scroll position when navigating away
                window.addEventListener('beforeunload', function() {
                    sessionStorage.setItem(`scrollPos-${pageType}`, window.scrollY.toString());
                });

                // Change bottom button opacity on scroll
                const bottomButton = document.querySelector('.nav-circle.bottom-center');
                if (bottomButton) {
                    window.addEventListener('scroll', function() {
                        if (window.scrollY > 100) {
                            bottomButton.style.opacity = "0.7";
                        } else {
                            bottomButton.style.opacity = "1";
                        }
                    });
                }
            }
        });

        // Simple page transition
        document.addEventListener('DOMContentLoaded', function() {
            const pageTransition = document.getElementById('page-transition');
            const links = document.querySelectorAll('a:not([target="_blank"]):not([href^="#"]):not([href^="javascript"])');

            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    if (this.hostname === window.location.hostname) {
                        e.preventDefault();
                        const href = this.getAttribute('href');

                        // Apply the transition
                        if (pageTransition) {
                            pageTransition.classList.add('active');
                        }

                        // Navigate after transition
                        setTimeout(() => {
                            window.location.href = href;
                        }, 400); // Match the CSS transition duration
                    }
                });
            });
        });
    </script> <!-- Fix: Use either is:inline or src, not both --> <script src="/scripts/main.js" defer></script> </body> </html>  
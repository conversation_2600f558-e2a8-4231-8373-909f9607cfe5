---
import { getPostsByType } from "../utils/unifiedContent";
import Layout from "../layouts/Layout.astro";
import ProjectCard from "../components/ProjectCard.astro";
import publications from "../data/publications.json"; // Import publications data

// Initialize with defaults in case of errors
let sortedProjects = [];
let featuredProjects = [];
let regularProjects = [];
let projectError = null; // Variable to hold specific error message

try {
  // Fetch all projects except archived ones
  // Get 'work' type content
  const workPosts = await getPostsByType('work');

  // Sort by date (newest first)
  sortedProjects = workPosts.sort(
    (a, b) => {
      const dateA = new Date(b.data?.pubDatetime || b.published_at);
      const dateB = new Date(a.data?.pubDatetime || a.published_at);
      return dateA.valueOf() - dateB.valueOf();
    }
  );

  // Extract featured projects
  featuredProjects = sortedProjects.filter(project => project.data?.featured || project.featured);
  regularProjects = sortedProjects.filter(project => !(project.data?.featured || project.featured));
} catch (error) {
  console.error("Error loading work projects:", error);
  projectError = "Failed to load projects. Please try again later."; // More specific user-facing message
  // In case of error, arrays will remain empty and the page will render with just static content
}

---

<Layout
  title="Work & Research | PVB"
  isHomePage={false}
  accentColor="#f0f0f0"
  bgColor="rgba(0, 0, 0, 0.88)"
  backgroundImageUrl="/images/obsidian.png"
  bodyDataPage="work"
>
  <!-- Work page header with elegant, minimal styling -->
  <div class="work-header">
    <h1 class="work-title">work</h1>
  </div>

  <div class="content-container">
    <!-- Featured Projects Section -->
:start_line:53
-------
{featuredProjects.length > 0 && (
  <section class="featured-projects-section">
    <div class="section-header">
      <h2 class="section-title">Featured Projects</h2>
      <p class="section-description">Highlighted work and recent explorations.</p>
    </div>
    <div class="featured-projects-list">
      {featuredProjects.map(project => (
        <ProjectCard project={project} variant="featured" />
      ))}
    </div>
  </section>
)}

{regularProjects.length > 0 && (
  <section class="projects-section">
    <div class="section-header">
      <h2 class="section-title">All Projects</h2>
      <p class="section-description">A comprehensive list of projects and experiments.</p>
    </div>
    <div class="projects-grid">
      {regularProjects.map(project => (
        <ProjectCard project={project} variant="standard" />
      ))}
    </div>
  </section>
)}

    <!-- Show a message if no projects are found -->
    {sortedProjects.length === 0 && projectError && (
      <div class="projects-error">
        <p>{projectError}</p>
      </div>
    )}
    {sortedProjects.length === 0 && !projectError && (
      <div class="projects-error">
        <p>No projects found.</p>
      </div>
    )}

    <!-- Publications Section with refined styling -->
    <section class="publications-section">
      <div class="section-header">
        <div class="section-line"></div>
        <h2 class="section-title">Research & Publications</h2>
        <div class="section-line"></div>
      </div>

      <ul class="publications-list">
        {publications.map(pub => (
          <li class="publication-item">
            <h3 class="pub-title">{pub.title}</h3>
            <div class="pub-meta">
              <p class="pub-details">{pub.journal}, {pub.year}</p>
              <div class="pub-links">
                {pub.pdfUrl && (
                  <a href={pub.pdfUrl} target="_blank" rel="noopener noreferrer" class="pub-link" aria-label={`Read PDF: ${pub.title}`}>
                    PDF <span class="arrow">↗</span>
                  </a>
                )}
                {pub.url && pub.url !== "#" && (
                  <a href={pub.url} target="_blank" rel="noopener noreferrer" class="pub-link" aria-label={`View project: ${pub.title}`}>
                    Link <span class="arrow">↗</span>
                  </a>
                )}
              </div>
            </div>
          </li>
        ))}
      </ul>
    </section>
  </div>
</Layout>

<style>
  /* Header styling - refined minimal approach */
  .work-header {
    width: 100%;
    text-align: center;
    padding: 5rem 0 4rem;
    position: relative;
  }

  .work-title {
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 1.05rem; /* Adjusted for consistency with homepage quote text */
    font-weight: normal;
    color: rgba(240, 240, 240, 0.95);
    position: relative;
    letter-spacing: -0.01em;
    display: inline-block;
    margin: 0;
  }

  /* Add subtle underline to work title for visual consistency with other pages */
  .work-title::after {
    content: '';
    position: absolute;
    bottom: -0.6rem;
    left: 50%;
    transform: translateX(-50%);
    width: 2.5rem;
    height: 1px;
    background-color: rgba(240, 240, 240, 0.3);
    transition: width var(--transition-duration) var(--easing-standard);
  }

  .work-title:hover::after {
    width: 3.5rem;
    background-color: rgba(240, 240, 240, 0.4);
  }

  /* Main container - ensure proper alignment and reasonable width */
  .content-container {
    width: 100%;
    max-width: 54rem; /* 864px */
    margin: 0 auto;
    padding: 0 1.5rem 6rem;
    color: #f0f0f0;
  }

  /* Error message for when projects can't be loaded */
  .projects-error {
    text-align: center;
    padding: 2rem;
    margin: 2rem 0;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
    color: rgba(240, 240, 240, 0.8);
  }

  /* Ensure proper scrolling behavior */
  :global(body[data-page="work"]) {
    overflow-y: auto;
    overflow-x: hidden;
    height: auto;
    min-height: 100vh;
  }

  /* Featured Projects Section */
  .featured-projects-section {
    margin-bottom: 4rem;
  }

  /* Projects grid for regular projects */
  .projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(21rem, 1fr)); /* ~336px */
    gap: 3rem 2.5rem;
    margin-bottom: 4rem;
    margin-top: 2rem;
  }

  /* Section header - elegant and refined styling */
  .section-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin: 4rem 0 2rem;
  }

  .section-line {
    flex-grow: 1;
    height: 1px;
    background-color: rgba(255, 255, 255, 0.08);
    transition: background-color var(--transition-duration) var(--easing-standard);
  }

  .section-header:hover .section-line {
    background-color: rgba(255, 255, 255, 0.12);
  }

  .section-title {
    font-size: 1rem;
    font-weight: normal;
    color: rgba(255, 255, 255, 0.7);
    white-space: nowrap;
    letter-spacing: 0.01em;
    padding: 0 0.3rem;
    transition: color var(--transition-duration) var(--easing-standard);
    margin: 0;
  }

  .section-header:hover .section-title {
    color: rgba(255, 255, 255, 0.85);
  }

  /* Publications styling - refined with better spacing and animations */
  .publications-list {
    list-style: none;
    padding: 0;
    margin: 2rem 0 0;
  }

  .publication-item {
    margin-bottom: 2.5rem;
    padding-bottom: 2.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.06);
    transition: transform var(--transition-duration) var(--easing-standard);
  }

  .publication-item:hover {
    transform: translateY(-3px);
  }

  .publication-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
  }

  .pub-title {
    font-size: 1.05rem; /* Adjusted for consistency with homepage quote text */
    line-height: 1.4;
    margin: 0 0 0.8rem;
    font-weight: normal;
    color: rgba(255, 255, 255, 0.85);
    font-family: 'Georgia Custom', Georgia, serif;
  }

  .pub-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.8rem;
  }

  .pub-links {
    display: flex;
    gap: 0.8rem;
  }

  .pub-details {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.5);
    margin: 0;
  }

  .pub-link {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.6); /* Adjusted for better visibility */
    text-decoration: none;
    transition: all var(--transition-duration) var(--easing-standard);
    display: flex;
    align-items: center;
    gap: 0.2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    padding-bottom: 1px;
  }

  .pub-link .arrow {
    font-size: 0.7rem;
    transition: transform var(--transition-duration) var(--easing-standard);
  }

  .pub-link:hover {
    color: rgba(255, 255, 255, 1);
    border-bottom-color: rgba(255, 255, 255, 0.6);
  }

  .pub-link:hover .arrow {
    transform: translateX(0.125rem) translateY(-0.125rem);
  }

  /* Responsive adjustments with refined breakpoints */
  @media (max-width: 64rem) { /* 1024px */
    .content-container {
      max-width: 90%;
    }

    .projects-grid {
      grid-template-columns: repeat(auto-fill, minmax(18rem, 1fr)); /* ~288px */
      gap: 2.5rem 2rem;
    }
  }

  @media (max-width: 48rem) { /* 768px */
    .work-header {
      padding: 4.5rem 0 3rem;
    }

    .work-title {
      font-size: 1.05rem; /* Adjusted for consistency with homepage quote text */
    }

    .content-container {
      padding: 0 1.25rem 4.5rem;
    }

    .projects-grid {
      grid-template-columns: 1fr;
      gap: 2.5rem;
    }

    .section-header {
      margin: 3.5rem 0 1.5rem;
    }

    .publication-item {
      margin-bottom: 2rem;
      padding-bottom: 2rem;
    }
  }

  @media (max-width: 30rem) { /* 480px */
    .work-header {
      padding: 3.5rem 0 2.5rem;
    }

    .content-container {
      padding: 0 1rem 3.5rem;
    }

    .pub-meta {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.6rem;
    }

    .pub-details {
      font-size: 0.75rem;
    }
  }
:start_line:374
-------
.featured-projects-section,
.projects-section {
  margin-bottom: 4rem;
}

.section-header {
  margin-bottom: 2rem;
  text-align: center;
}

.section-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  color: var(--theme-text, rgba(255, 255, 255, 0.95));
  letter-spacing: -0.025em;
}

.section-description {
  font-size: 1rem;
  color: var(--theme-text-light, rgba(255, 255, 255, 0.7));
  margin: 0;
  line-height: 1.5;
}

.featured-projects-list {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
}

@media (max-width: 768px) {
  .projects-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .section-title {
    font-size: 1.5rem;
  }
  
  .section-description {
    font-size: 0.9rem;
  }
}
</style>
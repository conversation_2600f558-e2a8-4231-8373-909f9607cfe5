const id = "project-two.md";
						const collection = "work";
						const slug = "project-two";
						const body = "\n## Project Overview\n\nThis project is developing a sophisticated knowledge management system inspired by tools like Obsidian and Roam Research. It focuses on creating a seamless experience for capturing, connecting, and retrieving information using a graph-based approach to knowledge.\n\n## Key Features\n\n- **Bidirectional Linking**: Create connections between notes that work in both directions, allowing for natural knowledge discovery.\n- **Graph Visualization**: Interactive visualization of your knowledge network, helping identify clusters and connections.\n- **Markdown-Based**: Simple yet powerful formatting using extended Markdown syntax.\n- **Local-First**: All data stored locally with optional encrypted sync.\n- **Customizable Workspace**: Flexible layout system with multiple panes and views.\n- **Advanced Search**: Full-text search with support for complex queries and filters.\n\n## Technical Implementation\n\nThe application is built using Electron for cross-platform compatibility, with a React frontend and TypeScript for type safety. The knowledge graph is stored in a specialized graph database optimized for quick traversal and querying.\n\n```typescript\n// Example of the bidirectional linking implementation\nclass NoteManager {\n  createLink(sourceNoteId: string, targetNoteId: string, linkText: string): Link {\n    // Create the forward link\n    const forwardLink = new Link({\n      sourceId: sourceNoteId,\n      targetId: targetNoteId,\n      text: linkText,\n      direction: 'forward'\n    });\n    \n    // Create the backward link\n    const backwardLink = new Link({\n      sourceId: targetNoteId,\n      targetId: sourceNoteId,\n      text: `Referenced by: ${this.getNoteTitle(sourceNoteId)}`,\n      direction: 'backward'\n    });\n    \n    // Store both links in the graph\n    this.graphDb.addLink(forwardLink);\n    this.graphDb.addLink(backwardLink);\n    \n    // Update the note content to include the link\n    this.updateNoteContent(sourceNoteId, linkText, targetNoteId);\n    \n    return forwardLink;\n  }\n}\n```\n\n## Current Status\n\nThe project is currently in active development with a working prototype that demonstrates the core functionality. The graph visualization and bidirectional linking features are operational, while advanced search capabilities are being refined.\n\n## Next Steps\n\n- Implement the encrypted sync functionality\n- Enhance the graph visualization with filtering and focus modes\n- Develop plugins system for extensibility\n- Create templates for different knowledge management workflows\n- Optimize performance for large knowledge bases\n";
						const data = {title:"Obsidian-Inspired Knowledge Management System",projectDate:new Date(1708387200000),status:"In Progress",featured:true,tags:["Knowledge Management","Electron","React","TypeScript","Graph Database"],ogImage:"/images/project-two-card.png",description:"A modern knowledge management system inspired by Obsidian, featuring bidirectional linking, graph visualization, and advanced search capabilities.",repoUrl:"https://github.com/username/knowledge-graph"};
						const _internal = {
							type: 'content',
							filePath: "C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-two.md",
							rawData: undefined,
						};

export { _internal, body, collection, data, id, slug };

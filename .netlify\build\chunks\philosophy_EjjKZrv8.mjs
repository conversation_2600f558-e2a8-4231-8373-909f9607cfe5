import { a as createComponent, m as maybeRenderHead, u as unescapeHTML, r as renderTemplate } from './astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import 'clsx';

const html = "";

				const frontmatter = {"text":"Many mistake stability for safety, but only the dead remain still.","author":"Pruthvi Bhat","linkedPage":"/blog/stability-vs-safety","cardTitle":"Core Philosophy","cardSubtitle":"Exploring the need for change","featured":true,"tags":["philosophy","change"]};
				const file = "C:/Users/<USER>/Desktop/pvb-astro/src/content/quotes/philosophy.md";
				const url = undefined;
				function rawContent() {
					return "";
				}
				function compiledContent() {
					return html;
				}
				function getHeadings() {
					return [];
				}

				const Content = createComponent((result, _props, slots) => {
					const { layout, ...content } = frontmatter;
					content.file = file;
					content.url = url;

					return renderTemplate`${maybeRenderHead()}${unescapeHTML(html)}`;
				});

export { Content, compiledContent, Content as default, file, frontmatter, getHeadings, rawContent, url };

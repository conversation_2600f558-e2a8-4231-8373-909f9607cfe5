---
import { getPostsByType, paginatePosts, getAllUniqueTags, slugifyStr } from "../utils/unifiedContent.js";
import Layout from "../layouts/Layout.astro";
import BlogPostCard from "../components/BlogPostCard.astro";

// Initialize variables with defaults
let featuredPost = null;
let regularPosts = [];
let blogPosts = [];
let allTags: string[] = [];
let displayableTags: string[] = [];
let error = false;
let errorMessage = "Blog posts are currently being updated. Please check back soon.";
let pagination: any = {};

const POSTS_PER_PAGE = 5;

try {
  // Fetch all blog posts using the JSON utility
  blogPosts = await getPostsByType('blog');

  if (blogPosts && blogPosts.length > 0) {
    // Identify single featured post and regular list
    featuredPost = blogPosts.find(post => post.featured) || null;
    regularPosts = featuredPost
      ? blogPosts.filter(post => post.slug !== featuredPost.slug)
      : blogPosts;
    // Paginate regular posts
    const { posts: paginatedPosts, pagination: pag } = paginatePosts(regularPosts, 1, POSTS_PER_PAGE);
    regularPosts = paginatedPosts;
    pagination = pag;

    // Get all unique tags
    allTags = await getAllUniqueTags();
    // Filter out internal tags for display
    const internalTags = ['blog','work','archive'];
    displayableTags = allTags.filter(tag => !internalTags.includes(tag.toLowerCase()));
  } else {
    // No posts found
    error = true;
    errorMessage = "No blog posts found yet. Please check back soon.";
  }
} catch (e) {
  console.error("Error loading blog posts:", e);
  error = true;
  // Fallback tags in case of error
  allTags = ["philosophy", "wisdom", "mastery", "AI", "synthesis", "innovation"];
}
---

<Layout
  pageTitle="Blog | PVB"
  isHomePage={false}
  accentColor="#f0f0f0"
  bgColor="rgba(10, 10, 10, 0.94)"
  backgroundImageUrl="/images/blackgranite.png"
  bodyDataPage="blog"
>
  <!-- Blog title - Static (not fixed) -->
  <h1 class="blog-header"><span class="blog-title">blog</span></h1>

  <!-- Full Page Content Container -->
  <div class="page-container">
    <!-- Left Sidebar -->
    <div class="blog-sidebar">
      <!-- Search Bar -->
      <div class="search-container sidebar-section" style="display: flex; justify-content: center;">
        <a href="/search" class="search-link sidebar-link">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="11" cy="11" r="8"></circle>
            <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
          </svg>
          <span>search</span>
        </a>
      </div>

      <!-- Archive Button -->
      <div class="archive-container sidebar-section">
        <a href="/blog/archive" class="archive-link sidebar-link">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M6 3h12l3 6v12a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9l3-6z" />
            <path d="M10 12h4" />
          </svg>
          <span>archives</span>
        </a>
      </div>

      <!-- Subscribe -->
      <div class="subscribe-container sidebar-section">
        <a href="mailto:<EMAIL>" class="subscribe-link sidebar-link">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M3 8l7.333 4.667a2 2 0 0 0 2.334 0L20 8" />
            <rect x="3" y="6" width="18" height="12" rx="2" ry="2" />
          </svg>
          <span>subscribe by email</span>
        </a>
      </div>

      <!-- Tags Filter (Collapsible) -->
      <div class="tags-container">
        <button class="tags-toggle sidebar-link" id="tags-toggle" aria-expanded="false" aria-controls="tags-list">
          <span class="tags-title">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M9 5H2v7l6.29 6.29c.94.94 2.48.94 3.42 0l7.58-7.58c.94-.94.94-2.48 0-3.42L13 2c-.94-.94-2.48-.94-3.42 0L9 5Z"></path>
              <path d="M6 9.01V9"></path>
            </svg>
            <span>tags</span>
          </span>
          <span class="toggle-icon">
            <span class="line hor"></span>
            <span class="line vert"></span>
          </span>
        </button>
        <div class="tags-list" id="tags-list">
          <a href="/tags" class="tag-link all-tags-link">all tags</a>
          {displayableTags.map(tag => {
            // tag is a simple string
            const tagName = tag;
            // Use slugified tag name for the URL to ensure consistency
            const tagSlug = tagName.toLowerCase().replace(/\s+/g, '-').replace(/[^\w\-]+/g, '').replace(/\-\-+/g, '-');
            return (
              <a href={`/blog/tag/${tagSlug}`} class="tag-link">{tagName}</a>
            );
          })}
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="blog-content">

      {error ? (
        <div class="blog-error">
          <p>{errorMessage}</p>
        </div>
      ) : blogPosts.length > 0 ? (
        <>
          {/* Single Featured Post */}
          {featuredPost && (
            <section class="featured-post" data-animate="fade-up">
              <h2 class="section-title">Featured</h2>
              <div class="post-item-wrapper" data-tags={featuredPost.tags?.map(tag => slugifyStr(typeof tag === 'string' ? tag : (tag.slug || tag.name))).filter(Boolean).join(',') || ''}>
                <BlogPostCard post={featuredPost} />
              </div>
            </section>
          )}
          <!-- Posts List -->
          <ul id="blog-post-list">
            {regularPosts.map(post => (
              <li class="post-list-item" data-tags={post.tags?.map(tag => slugifyStr(typeof tag === 'string' ? tag : (tag.slug || tag.name))).filter(Boolean).join(',') || ''}>
                <BlogPostCard post={post} />
              </li>
            ))}
          </ul>

          <!-- Pagination Controls -->
          <div class="pagination">
            {pagination.prevPage != null && <a href={`/blog/page/${pagination.prevPage}`} class="pagination-link prev-page">← Newer Posts</a>}
            <div class="pagination-info">Page {pagination.currentPage} of {pagination.totalPages}</div>
            {pagination.nextPage != null && <a href={`/blog/page/${pagination.nextPage}`} class="pagination-link next-page">Older Posts →</a>}
          </div>

          <!-- All Blogs Link -->
          <div class="all-blogs-link-container" style="text-align:center; margin-top: 2rem;">
            <a href="/blog/all" class="all-blogs-link">View All Blogs</a>
          </div>
        </>
      ) : (
        <div class="blog-error">
          <p>No blog posts available. Please check back soon.</p>
        </div>
      )}
    </div>
  </div>

<script is:inline>
  // Tag drawer toggle functionality
  document.getElementById('tags-toggle')?.addEventListener('click', function() {
    const container = this.closest('.tags-container');
    const isExpanded = this.getAttribute('aria-expanded') === 'true';
    
    // Toggle container state
    container.classList.toggle('open');
    this.setAttribute('aria-expanded', !isExpanded);
  });
</script>

<script is:inline>
  // Fade-up animation observer
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('in-view');
        observer.unobserve(entry.target);
      }
    });
  }, { threshold: 0.1 });
  document.querySelectorAll('[data-animate="fade-up"]').forEach(el => observer.observe(el));
</script>
</Layout>

<style>
  /* Global Scrollbar Styling */
  :global(html) {
    scrollbar-width: thin;
    scrollbar-color: rgba(100, 100, 100, 0.4) transparent;
  }

  :global(::-webkit-scrollbar) {
    width: 8px;
    height: 8px;
  }

  :global(::-webkit-scrollbar-track) {
    background: transparent;
  }

  :global(::-webkit-scrollbar-thumb) {
    background-color: rgba(100, 100, 100, 0.4);
    border-radius: 4px;
  }

  :global(::-webkit-scrollbar-thumb:hover) {
    background-color: rgba(120, 120, 120, 0.6);
  }

  /* Enable scrolling on blog page */
  :global(body[data-page="blog"]) {
    overflow-y: auto;
  }

  /* Blog Header - Static (not fixed) positioned below the logo */
  .blog-header {
    width: 100%;
    display: flex;
    justify-content: center;
    margin: 55px auto 40px; /* Increased bottom margin and centered */
  }

  .blog-title {
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 1.4rem; /* Adjusted for hierarchy */
    letter-spacing: -0.01em;
    position: relative;
    margin-bottom: 10px; /* Add space below underline */
  }

  /* Add subtle underline to blog title */
  .blog-title::after {
    content: '';
    position: absolute;
    bottom: -10px; /* Move underline down slightly */
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 1px;
    background-color: rgba(var(--color-accent-rgb), 0.5);
  }

  /* Error message when blog posts can't be loaded */
  .blog-error {
    text-align: center;
    padding: 2rem;
    margin: 2rem 0;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
    color: rgba(240, 240, 240, 0.8);
  }

  /* Full Page Container */
  .page-container {
    display: flex;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 30px; /* Consistent horizontal padding */
    gap: 40px;
  }

  /* Section titles */
  .section-title {
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 0.9rem; /* Adjusted for hierarchy */
    font-weight: 500;
    color: rgba(var(--color-accent-rgb), 0.6);
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.08em; /* Increase letter spacing */
  }

  /* Left Sidebar - Better spacing and styling */
  .blog-sidebar {
    width: 220px;
    padding-top: 30px;
    padding-right: 20px;
    position: sticky;
    top: 0;
    height: 100vh;
    align-self: flex-start;
  }

  /* Add more space between sidebar sections */
  .sidebar-section {
    margin-bottom: 40px; /* Increased spacing between sections */
  }

  /* Shared styles for sidebar interactive elements */
  .sidebar-link {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
    color: rgba(240, 240, 240, 0.8);
    text-decoration: none;
    font-family: 'Georgia Custom', Georgia, serif;
    padding: 0.5rem 1rem;
    background-color: rgba(34, 34, 34, 0.5);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 0.75rem; /* Match card radius */
    transition: all var(--transition-duration) var(--easing-standard);
  }

  .sidebar-link:hover,
  .sidebar-link:focus-visible {
    color: rgba(240, 240, 240, 0.9);
    background-color: rgba(40, 40, 40, 0.6);
    border-color: rgba(255, 255, 255, 0.12);
    outline: none;
  }

  .sidebar-link svg {
    opacity: 0.8;
    transition: opacity var(--transition-duration) var(--easing-standard);
  }

  .sidebar-link:hover svg {
    opacity: 1;
  }

  /* Search Styles */
  .search-link {
    /* Inherits from .sidebar-link */
  }

  /* Archive Button */
  .archive-link {
    /* Inherits from .sidebar-link */
  }

  /* Style the tags button like other sidebar links */
  .tags-toggle {
    position: relative;
    /* Inherits from .sidebar-link */
    justify-content: space-between; /* Keep icon on the right */
    width: 100%; /* Make it full width */
    cursor: pointer;
    border-radius: 0.75rem; /* Match parent */
    margin-bottom: 0; /* Remove margin if container handles it */
    background-color: transparent; /* Let container handle bg */
    border: none; /* Let container handle border */
    box-shadow: none; /* Let container handle shadow */
    padding: 0.6rem 1rem; /* Slightly more padding for the button itself */
  }

  .tags-toggle:hover,
  .tags-toggle:focus-visible {
    background-color: rgba(255, 255, 255, 0.05); /* Subtle hover inside */
    transform: none; /* No lift needed, container handles it */
    box-shadow: none;
    border: none;
    outline: none;
  }

  /* Tag drawer container */
  .tags-container {
    background-color: rgba(34, 34, 34, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 0.75rem;
    transition: background-color var(--transition-duration) var(--easing-standard), border-color var(--transition-duration) var(--easing-standard);
  }

  /* Highlight drawer when open */
  .tags-container.open {
    border-color: rgba(255, 255, 255, 0.2);
    background-color: rgba(40, 40, 40, 0.7);
  }

  /* Tag list collapse/expand animation */
  .tags-list {
    overflow: hidden;
    max-height: 0;
    opacity: 0;
    transform: translateY(-10px);
    transition: max-height 0.4s ease-out, opacity var(--transition-duration) var(--easing-standard), transform var(--transition-duration) var(--easing-standard), padding var(--transition-duration) var(--easing-standard), margin-top var(--transition-duration) var(--easing-standard);
    padding: 0;
    margin-top: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 0.4rem;
  }

  .tags-container.open .tags-list {
    max-height: 500px;
    opacity: 1;
    transform: translateY(0);
    padding: 0.75rem;
    margin-top: 0.5rem;
  }

  /* Toggle icon shape */
  .toggle-icon {
    position: relative;
    width: 0.8rem;
    height: 0.8rem;
    display: inline-block;
    margin-left: auto;
  }
  .toggle-icon .line {
    position: absolute;
    background: rgba(240,240,240,0.6);
    transition: background var(--transition-duration) var(--easing-standard), transform var(--transition-duration) var(--easing-standard);
  }
  .toggle-icon .hor {
    top: 50%; left: 0;
    width: 100%; height: 1px;
    transform-origin: center;
    transform: translateY(-50%) scaleX(1);
  }
  .toggle-icon .vert {
    left: 50%; top: 0;
    width: 1px; height: 100%;
    transform-origin: center top;
    transform: translateX(-50%) scaleY(1);
  }
  .tags-toggle:hover .toggle-icon .line {
    background: rgba(240,240,240,0.8);
  }
  .tags-toggle[aria-expanded="true"] .toggle-icon .vert {
    transform: translateX(-50%) scaleY(0);
  }
  .tags-toggle[aria-expanded="true"] .toggle-icon .hor {
    transform: translateY(-50%) scaleX(1);
  }

  /* Style tags in drawer as pills */
  .tags-list .tag-link {
    display: inline-block;
    display: inline-block; /* Ensure padding works */
    background: rgba(34, 34, 34, 0.5); /* Match sidebar link background */
    color: rgba(240, 240, 240, 0.8); /* Match sidebar link color */
    padding: 0.3rem 0.8rem;
    border-radius: 0.5rem; /* Slightly less rounded than sidebar link */
    font-size: 0.85rem; /* Slightly smaller */
    text-decoration: none;
    border: 1px solid rgba(255, 255, 255, 0.08); /* Match sidebar link border */
    transition: all var(--transition-duration) var(--easing-standard);
  }
  .tags-list .tag-link:hover,
  .tags-list .tag-link:focus-visible {
    color: rgba(240, 240, 240, 0.9); /* Match sidebar link hover color */
    background-color: rgba(40, 40, 40, 0.6); /* Match sidebar link hover background */
    border-color: rgba(255, 255, 255, 0.12); /* Match sidebar link hover border */
    outline: none;
  }

  /* Main Content - Full width layout */
  .blog-content {
    flex: 1;
    padding: 10px 30px 60px 30px; /* Consistent padding */
    max-width: 900px;
  }

  /* All Blogs Link */
  .all-blogs-link-container {
    text-align: center;
    margin-top: 2rem;
  }

  .all-blogs-link {
    font-size: 0.9rem;
    color: var(--blog-text-secondary);
    text-decoration: none;
    font-family: 'Georgia Custom', Georgia, serif;
    transition: all var(--transition-duration) var(--easing-standard);
    display: inline-block;
    padding: 0.5rem 1rem; /* Match sidebar link padding */
    background-color: rgba(34, 34, 34, 0.5); /* Match sidebar link background */
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.08); /* Match sidebar link border */
    border-radius: 0.75rem; /* Match sidebar link radius */
  }

  .all-blogs-link:hover,
  .all-blogs-link:focus-visible {
    color: rgba(240, 240, 240, 0.9); /* Match sidebar link hover color */
    background-color: rgba(40, 40, 40, 0.6); /* Match sidebar link hover background */
    border-color: rgba(255, 255, 255, 0.12); /* Match sidebar link hover border */
    transform: none; /* Remove translateY */
    box-shadow: none; /* Remove box-shadow */
    outline: none;
  }

  /* Pagination */
  .pagination {
    margin-top: 3rem; /* Add more space above pagination */
    display: flex;
    gap: 1rem;
    align-items: center;
    justify-content: center;
  }

  .pagination-link {
    font-family: 'Georgia Custom', Georgia, serif;
    color: rgba(var(--color-accent-rgb), 0.7);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border: 1px solid rgba(var(--color-accent-rgb), 0.2);
    border-radius: 0.5rem;
    transition: all var(--transition-duration) var(--easing-standard);
  }

  .pagination-link:hover,
  .pagination-link:focus-visible {
    color: rgba(var(--color-accent-rgb), 1);
    background-color: rgba(var(--color-accent-rgb), 0.1);
    border-color: rgba(var(--color-accent-rgb), 0.4);
    outline: none;
  }

  .pagination-info {
    font-size: 0.9rem;
    color: rgba(240, 240, 240, 0.6);
    font-family: 'Georgia Custom', Georgia, serif;
  }

  /* Link Colors */
  .sidebar-link {
    color: var(--blog-text-secondary);
    text-decoration: none;
    transition: color 0.2s ease;
  }
  .sidebar-link:hover {
    color: var(--blog-text);
  }

  .blog-content a {
    color: var(--blog-text);
    text-decoration: none;
    transition: color 0.2s ease;
  }
  .blog-content a:hover {
    color: var(--blog-text-inverse);
  }

  /* Blog Header Title */
  .blog-header .blog-title {
    color: var(--blog-text);
  }

  /* Mobile responsive tweaks */
  @media (max-width: 768px) {
    .page-container {
      flex-direction: column;
      padding: 0 20px;
      gap: 20px;
    }
    .sidebar-section {
      display: none;
    }
    .blog-sidebar {
      width: 100%;
      padding: 0;
      position: relative;
      height: auto;
      margin-bottom: 20px;
    }
    .blog-content {
      max-width: 100%;
    }
  }

  /* Sidebar tweaks */
  .search-container { display: flex; justify-content: center; }
</style>
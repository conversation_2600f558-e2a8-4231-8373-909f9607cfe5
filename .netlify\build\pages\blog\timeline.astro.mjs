import { a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead, b as addAttribute } from '../../chunks/astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import { g as getCollection } from '../../chunks/_astro_content_CcaQj6Wl.mjs';
import { $ as $$Layout } from '../../chunks/Layout_rXbp99fE.mjs';
export { renderers } from '../../renderers.mjs';

const $$Timeline = createComponent(async ($$result, $$props, $$slots) => {
  let postsByYear = {};
  let sortedYears = [];
  let error = false;
  try {
    const posts = await getCollection("blog");
    const nonDraftPosts = posts.filter(({ data }) => !data.draft);
    postsByYear = nonDraftPosts.reduce((acc, post) => {
      const year = new Date(post.data.pubDatetime).getFullYear().toString();
      if (!acc[year]) {
        acc[year] = [];
      }
      acc[year].push(post);
      return acc;
    }, {});
    sortedYears = Object.keys(postsByYear).sort((a, b) => parseInt(b) - parseInt(a));
    for (const year of sortedYears) {
      postsByYear[year].sort((a, b) => {
        const dateA = new Date(a.data.pubDatetime).getTime();
        const dateB = new Date(b.data.pubDatetime).getTime();
        return dateB - dateA;
      });
    }
  } catch (e) {
    console.error("Error fetching posts:", e);
    error = true;
  }
  return renderTemplate`${error ? renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "pageTitle": "Archive Error | Blog | PVB", "isHomePage": false, "accentColor": "#f0f0f0", "bgColor": "rgba(10, 10, 10, 0.94)", "backgroundImageUrl": "/images/blackgranite.png", "bodyDataPage": "blog" }, { "default": async ($$result2) => renderTemplate`${maybeRenderHead()}<div class="error-container"><h1>Error Loading Archive</h1><p>Sorry, there was an error loading the blog archive.</p><a href="/blog" class="back-to-blog">&larr; back to blog</a></div><style>
      .error-container {
        max-width: 600px;
        margin: 100px auto;
        text-align: center;
        padding: 0 20px;
      }

      h1 {
        font-size: 1.8rem;
        color: rgba(240, 240, 240, 0.9);
        margin-bottom: 20px;
        font-family: 'Georgia Custom', Georgia, serif;
      }

      p {
        font-size: 1.1rem;
        color: rgba(220, 220, 220, 0.8);
        margin-bottom: 30px;
        font-family: 'Georgia Custom', Georgia, serif;
      }

      .back-to-blog {
        font-family: 'Georgia Custom', Georgia, serif;
        font-size: 0.95rem;
        color: rgba(200, 200, 200, 0.8);
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-block;
        padding: 0.5rem 1rem;
        border: 1px solid rgba(200, 200, 200, 0.3);
        border-radius: 3px;
      }

      .back-to-blog:hover {
        color: rgba(240, 240, 240, 1);
        border-color: rgba(240, 240, 240, 0.5);
        background-color: rgba(255, 255, 255, 0.05);
        transform: translateX(-3px);
      }
    </style>` })}` : renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "pageTitle": "Archive | PVB", "isHomePage": false, "accentColor": "#f0f0f0", "bgColor": "rgba(10, 10, 10, 0.94)", "backgroundImageUrl": "/images/blackgranite.png", "bodyDataPage": "blog" }, { "default": async ($$result2) => renderTemplate`<div class="blog-header"><div class="blog-title">archive</div></div><div class="timeline-container"><!-- Back to Blog --><div class="back-section"><a href="/blog" class="back-to-blog"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19 12H5"></path><path d="M12 19l-7-7 7-7"></path></svg><span>back to blog</span></a></div><!-- Timeline Content --><div class="timeline-content">${sortedYears.map((year) => renderTemplate`<div class="year-section"><h2 class="year-heading">${year}</h2><div class="year-posts">${postsByYear[year].map((post) => renderTemplate`<div class="timeline-post"><div class="post-date">${new Date(post.data.pubDatetime).toLocaleDateString("en-US", { month: "short", day: "numeric" })}</div><h3 class="post-title"><a${addAttribute(`/blog/${post.slug}`, "href")}>${post.data.title}</a></h3></div>`)}</div></div>`)}</div></div><style>
      /* Global Scrollbar Styling */
      :global(html) {
        scrollbar-width: thin;
        scrollbar-color: rgba(0, 0, 0, 0.4) transparent;
      }

      :global(::-webkit-scrollbar) {
        width: 8px;
        height: 8px;
      }

      :global(::-webkit-scrollbar-track) {
        background: transparent;
      }

      :global(::-webkit-scrollbar-thumb) {
        background-color: rgba(100, 100, 100, 0.4);
        border-radius: 4px;
      }

      :global(::-webkit-scrollbar-thumb:hover) {
        background-color: rgba(120, 120, 120, 0.6);
      }

      /* Blog Header - Static (not fixed) */
      .blog-header {
        width: 100%;
        display: flex;
        justify-content: center;
        margin: 55px 0 30px;
      }

      .blog-title {
        font-family: 'Georgia Custom', Georgia, serif;
        font-size: 1.5rem;
        color: rgba(240, 240, 240, 0.9);
        letter-spacing: -0.01em;
        position: relative;
      }

      /* Add subtle underline to blog title */
      .blog-title::after {
        content: '';
        position: absolute;
        bottom: -8px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 1px;
        background-color: rgba(240, 240, 240, 0.4);
      }

      /* Timeline Container */
      .timeline-container {
        max-width: 800px;
        margin: 0 auto 60px;
        padding: 0 30px;
      }

      /* Back to Blog Link */
      .back-section {
        margin-bottom: 40px;
      }

      .back-to-blog {
        font-family: 'Georgia Custom', Georgia, serif;
        font-size: 0.95rem;
        color: rgba(200, 200, 200, 0.8);
        text-decoration: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 6px;
        width: fit-content;
        padding: 0.5rem 1rem;
        border: 1px solid rgba(200, 200, 200, 0.3);
        border-radius: 3px;
      }

      .back-to-blog:hover {
        color: rgba(240, 240, 240, 1);
        border-color: rgba(240, 240, 240, 0.5);
        background-color: rgba(255, 255, 255, 0.05);
        transform: translateX(-3px);
      }

      /* Year Sections */
      .year-section {
        margin-bottom: 50px;
      }

      .year-heading {
        font-family: 'Georgia Custom', Georgia, serif;
        font-size: 1.8rem;
        color: rgba(240, 240, 240, 0.95);
        margin-bottom: 20px;
        font-weight: normal;
        position: relative;
        padding-bottom: 10px;
      }

      .year-heading::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 60px;
        height: 1px;
        background-color: rgba(200, 200, 200, 0.3);
      }

      /* Post Items */
      .timeline-post {
        margin-bottom: 25px;
        display: flex;
        flex-direction: column;
      }

      .post-date {
        font-family: 'Georgia Custom', Georgia, serif;
        font-size: 0.9rem;
        color: rgba(180, 180, 180, 0.75);
        margin-bottom: 5px;
      }

      .post-title {
        font-family: 'Georgia Custom', Georgia, serif;
        font-size: 1.2rem;
        font-weight: normal;
        margin: 0;
      }

      .post-title a {
        color: rgba(220, 220, 220, 0.9);
        text-decoration: none;
        transition: color 0.3s ease;
      }

      .post-title a:hover {
        color: rgba(255, 255, 255, 1);
        text-decoration: underline;
        text-underline-offset: 3px;
        text-decoration-color: rgba(200, 200, 200, 0.4);
      }

      /* Responsive Styles */
      @media (max-width: 768px) {
        .blog-title {
          font-size: 1.3rem;
        }

        .timeline-container {
          padding: 0 20px;
        }

        .year-heading {
          font-size: 1.6rem;
        }

        .post-title {
          font-size: 1.1rem;
        }
      }
    </style>` })}`}`;
}, "C:/Users/<USER>/Desktop/pvb-astro/src/pages/blog/timeline.astro", void 0);

const $$file = "C:/Users/<USER>/Desktop/pvb-astro/src/pages/blog/timeline.astro";
const $$url = "/blog/timeline";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Timeline,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

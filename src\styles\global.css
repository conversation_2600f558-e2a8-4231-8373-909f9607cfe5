/* Font Imports */
@font-face { font-family: 'Georgia Custom'; src: url('/fonts/georgia-ref.ttf') format('truetype'); font-weight: normal; font-style: normal; }
@font-face { font-family: 'Georgia Custom'; src: url('/fonts/georgia-bold-2.ttf') format('truetype'); font-weight: bold; font-style: normal; }
@font-face { font-family: 'Serif12'; src: url('/fonts/serif12-beta-regular.otf') format('opentype'); font-weight: normal; font-style: normal; }

/* Reset & Base */
:root {
  /* Core sizing */
  --circle-size: 18px;
  --circle-bottom-size: 36px;
  --indicator-line-width: 12px; --indicator-line-height: 2px; --indicator-circle-size: 20px; --indicator-wrapper-size: 44px;
  --circle-size-mobile: 16px;
  --circle-bottom-size-mobile: 32px;
  --circle-border-width: 1.5px;
  --circle-expand-scale: 1.6;
  --x-line-thickness: 0.8px;
  --plus-line-thickness: 0.9px;

  /* Timing & Easing */
  --transition-duration: 0.4s; /* Default speed (e.g., for unhover) */
  --bottom-button-duration: 0.6s;
  --bottom-hover-duration: 0.5s;
  --plus-grow-duration: 0.7s; /* SLOW duration for '+' hover grow */
  --plus-to-x-duration: 0.35s; /* FAST duration for + transforming into X */
  --easing-standard: cubic-bezier(0.25, 0.1, 0.25, 1);
  --easing-out-smooth: ease-out; /* Smoother easing for '+' grow */
  /* Easing for the fast + to X transition - snappy with slight overshoot */
  --easing-plus-to-x: cubic-bezier(0.34, 1.56, 0.64, 1);
  --easing-dramatic-spin: cubic-bezier(0.68, -0.55, 0.27, 1.55);
  --focus-transition-duration: 0.2s;
  --menu-item-exit-duration: calc(var(--transition-duration) * 0.95);

  /* Colors */
  --color-bg: rgba(250, 246, 242, 0.9);
  --color-bg-overlay: rgba(0, 0, 0, 0.96);
  --color-card-bg: #fdfbf9;
  --color-accent: #3a2c23;
  --color-accent-darker: #2a1f1a;
  --color-accent-inverse: #fff;
  --color-inactive: #8a8178;
  --color-text: var(--color-accent);
  --color-text-secondary: #6a5a4f;
  --color-text-inverse: #f0f0f0;
  --color-logo-menu: rgba(240, 240, 240, 0.7);
  --color-fitness-accent: #6D464F; /* New: Dark pinkish-brown for fitness page */
  --shadow-card: 0 6px 20px rgba(0, 0, 0, 0.08);
  --shadow-card-hover: 0 10px 25px rgba(0, 0, 0, 0.15);
  --shadow-card-active: 0 4px 15px rgba(0, 0, 0, 0.1);
  --shadow-card-inset: inset 0 0 10px rgba(0, 0, 0, 0.03);

  /* Navigation Bottom Active State */
  --nav-bottom-active-bg: rgba(20, 20, 20, 0.95);
  --nav-bottom-active-border: rgba(30, 30, 30, 0.95);
  --nav-bottom-active-icon: var(--color-accent-inverse);
  
  /* Button Colors - Silvery Grey Instead of White */
  --button-bg: #d0d0d0;
  --button-hover-bg: #c0c0c0;
  --button-active-bg: #b8b8b8;
  --button-text: #222222;

  /* Blog-specific Colors */
  --blog-bg-tint: rgba(10, 10, 10, 0.94);
  --blog-text: rgba(240, 240, 240, 0.9);
  --blog-text-secondary: rgba(210, 210, 210, 0.85);
  --blog-border: rgba(200, 200, 200, 0.15);
  --blog-toc-hover: rgba(255, 255, 255, 0.04);
  --blog-toc-active: rgba(255, 255, 255, 0.08);
}

/* Global Scrollbar Styling */
html {
  scrollbar-width: thin;
  scrollbar-color: rgba(100, 100, 100, 0.4) transparent;
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(100, 100, 100, 0.4);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(120, 120, 120, 0.6);
}

/* Dark theme variables (Placeholder) - Removed empty ruleset */

* { margin: 0; padding: 0; box-sizing: border-box; }
html, body { height: 100%; font-family: 'Georgia Custom', Georgia, serif; color: var(--color-text); }

/* Allow vertical scrolling for all pages */
body {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    transition: background-color var(--transition-duration) var(--easing-standard);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    overflow-y: auto;
    /* The background image will be set via inline styles on the body based on props */
}

/* Ensure smooth scrolling */
html {
  scroll-behavior: smooth;
}

.content-wrapper { position: fixed; top: 0; left: 0; width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; flex-direction: column; transition: filter var(--transition-duration) var(--easing-standard), opacity var(--transition-duration) var(--easing-standard); z-index: 1; }
body.menu-active .content-wrapper { filter: blur(4px); opacity: 0.5; transition: filter calc(var(--transition-duration) * 1.1) var(--easing-standard), opacity calc(var(--transition-duration) * 1.1) var(--easing-standard); }
body.quote-card-active .content-wrapper { filter: blur(3px); opacity: 0.7; }

/* Adjust content wrapper for blog and blogpost pages */
body[data-page="blog"] .content-wrapper,
body[data-page="blog-post"] .content-wrapper,
body[data-page="about"] .content-wrapper,
body[data-page="fitness"] .content-wrapper {
  position: relative;
  justify-content: flex-start;
  /* Hide table of contents on about page */
  .toc-container { display: none; }
  height: auto;
  min-height: 100vh;
}

/* Logo Link Styling - Centered */
.logo, .menu-logo { position: absolute; top: 30px; left: 50%; transform: translateX(-50%); font-size: 1rem; font-weight: normal; z-index: 10; font-family: 'Serif12', Georgia, serif; text-decoration: none; cursor: pointer; color: var(--color-text-secondary); transition: color var(--transition-duration) var(--easing-standard), opacity var(--transition-duration) var(--easing-standard); text-align: center; }
.logo:hover, .menu-logo:hover { color: var(--color-text); }
.menu-logo { color: var(--color-logo-menu); opacity: 0; z-index: 51; }
.menu-logo:hover { color: var(--color-text-inverse); }
 body.menu-active .menu-logo { opacity: 1; transition-delay: calc(var(--transition-duration) * 0.5); }
 body.menu-active.closing .menu-logo { opacity: 0; transition: opacity calc(var(--transition-duration)*0.5) ease-out; transition-delay: 0s; }

/* Nav Circles - Base styling */
.nav-circle { position: absolute; width: var(--circle-size); height: var(--circle-size); border-radius: 50%; cursor: pointer; display: flex; justify-content: center; align-items: center; z-index: 100; border: var(--circle-border-width) solid var(--color-accent); transition: transform var(--transition-duration) var(--easing-standard), background-color var(--transition-duration) var(--easing-standard), border-color var(--transition-duration) var(--easing-standard), width var(--transition-duration) var(--easing-standard), height var(--transition-duration) var(--easing-standard), opacity var(--transition-duration) var(--easing-standard), visibility 0s var(--transition-duration); opacity: 1; visibility: visible; transform-origin: center center; }
.nav-circle.top-left, .nav-circle.top-right { background-color: var(--color-accent); border-color: var(--color-accent); top: 30px; }
.nav-circle.top-left { left: 30px; }
.nav-circle.top-right { right: 30px; }
.nav-circle.bottom-center { background-color: transparent; width: var(--circle-bottom-size); height: var(--circle-bottom-size); bottom: 30px; left: 50%; transform: translateX(-50%); transition: transform var(--bottom-button-duration) var(--easing-standard), background-color var(--bottom-button-duration) var(--easing-standard), border-color var(--bottom-button-duration) var(--easing-standard), width var(--transition-duration) var(--easing-standard), height var(--transition-duration) var(--easing-standard); }

/* Override top-right nav circle style for blog post page (hidden) */
body[data-page="blog-post"] .nav-circle.top-right {
  display: none;
}

/* Special styling for bottom nav button in blog and tag sections when menu is active */
body[data-page="blog"].menu-active .nav-circle.bottom-center,
body[data-page="blog-post"].menu-active .nav-circle.bottom-center,
body[data-page="tag"].menu-active .nav-circle.bottom-center,
body[data-page="blog"].menu-active .nav-circle.bottom-center,
body[data-page="blog-post"].menu-active .nav-circle.bottom-center,
body[data-page="tag"].menu-active .nav-circle.bottom-center,
body[data-page="tags"].menu-active .nav-circle.bottom-center,
body[data-page="about"].menu-active .nav-circle.bottom-center {
  background-color: var(--nav-bottom-active-bg);
  border-color: var(--nav-bottom-active-border);
}

/* Homepage: Make bottom button hollow like top buttons */
body[data-page="home"] .nav-circle.bottom-center {
  background-color: transparent;
  border-color: #222222; /* Match top button color */
}

/* Fitness Page: Dark pinkish-brown navigation styling */
body[data-page="fitness"] .nav-circle.top-left,
body[data-page="fitness"] .nav-circle.top-right {
  background-color: var(--color-fitness-accent);
  border-color: var(--color-fitness-accent);
}

/* Keep icons in fitness page buttons with correct color on hover */
body[data-page="fitness"] .nav-circle.top-left:hover .nav-icon::before,
body[data-page="fitness"] .nav-circle.top-right:hover .nav-icon::before,
body[data-page="fitness"] .nav-circle.top-right:hover .nav-icon::after {
  color: var(--color-fitness-accent);
  border-color: var(--color-fitness-accent);
}

/* Fitness Page: Make bottom button hollow like home page */
body[data-page="fitness"] .nav-circle.bottom-center {
  background-color: transparent;
  border-color: var(--color-fitness-accent);
}

/* Fitness Page: Bottom button styling when menu is active */
body[data-page="fitness"].menu-active .nav-circle.bottom-center {
  background-color: var(--color-fitness-accent);
  border-color: var(--color-fitness-accent);
}

/* Blog/Tag Pages: Make top buttons white */
body[data-page="blog"] .nav-circle.top-left,
body[data-page="blog"] .nav-circle.top-right,
body[data-page="blog-post"] .nav-circle.top-left, /* Also apply to blog posts */
body[data-page="tag"] .nav-circle.top-left,
body[data-page="tag"] .nav-circle.top-right,
body[data-page="tags"] .nav-circle.top-left,
body[data-page="tags"] .nav-circle.top-right {
  background-color: var(--color-accent-inverse); /* White background */
  border-color: #222222; /* Keep dark border */
}
/* Keep icons white on hover for white buttons */
body[data-page="blog"] .nav-circle.top-left:hover .nav-icon::before,
body[data-page="blog"] .nav-circle.top-right:hover .nav-icon::before,
body[data-page="blog"] .nav-circle.top-right:hover .nav-icon::after,
body[data-page="blog-post"] .nav-circle.top-left:hover .nav-icon::before,
body[data-page="tag"] .nav-circle.top-left:hover .nav-icon::before,
body[data-page="tag"] .nav-circle.top-right:hover .nav-icon::before,
body[data-page="tag"] .nav-circle.top-right:hover .nav-icon::after,
body[data-page="tags"] .nav-circle.top-left:hover .nav-icon::before,
body[data-page="tags"] .nav-circle.top-right:hover .nav-icon::before,
body[data-page="tags"] .nav-circle.top-right:hover .nav-icon::after {
  color: var(--color-accent-inverse); /* Keep icons white on hover */
  border-color: var(--color-accent-inverse); /* White borders for link icons */
}

/* Hide TL/TR buttons when menu is active */
body.menu-active .nav-circle.top-left,
body.menu-active .nav-circle.top-right { opacity: 0; visibility: hidden; pointer-events: none; transition: opacity var(--transition-duration) var(--easing-standard), visibility 0s var(--transition-duration); }
body:not(.menu-active) .nav-circle.top-left,
body:not(.menu-active) .nav-circle.top-right { transition-delay: calc(var(--transition-duration) * 0.2); }

/* Interaction States */
.nav-circle:active { transform: scale(0.95); transition: transform 0.1s ease-out; }
.nav-circle.bottom-center:active { transform: translateX(-50%) scale(0.95); }
.nav-circle.top-left:hover, .nav-circle.top-right:hover { transform: scale(var(--circle-expand-scale)); background-color: transparent; }

/* Bottom button inactive hover - Slower scale */
body:not(.menu-active) .nav-circle.bottom-center:hover { transform: translateX(-50%) scale(1.5); transition: transform var(--bottom-hover-duration) var(--easing-standard); }
body:not(.menu-active) .nav-circle.bottom-center:hover:active { transform: translateX(-50%) scale(1.45); }

/* Quote Indicator */
.quote-indicator-wrapper { position: absolute; width: var(--indicator-wrapper-size); height: var(--indicator-wrapper-size); display: flex; justify-content: center; align-items: center; cursor: pointer; z-index: 50; }
.quote-indicator { width: var(--indicator-line-width); height: var(--indicator-line-height); background-color: var(--color-inactive); border-radius: 1px; transition: all var(--transition-duration) var(--easing-standard); }
.quote-indicator-wrapper.active .quote-indicator { width: var(--indicator-circle-size); height: var(--indicator-circle-size); background-color: transparent; border: var(--circle-border-width) solid var(--color-inactive); border-radius: 50%; }
.quote-indicator-wrapper.active:hover .quote-indicator { transform: scale(1.15); }

/* --- Icons within Nav Circles --- */
.nav-icon { position: relative; width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; color: var(--color-accent); transition: color var(--transition-duration) var(--easing-standard); line-height: 1; }
/* Top Icons */
.nav-circle.top-left .nav-icon::before { content: "?"; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%) scale(0.8); font-size: 10px; opacity: 0; transition: opacity calc(var(--transition-duration)*0.8) calc(var(--transition-duration)*0.2) var(--easing-standard), transform calc(var(--transition-duration)*0.8) calc(var(--transition-duration)*0.2) var(--easing-standard); color: var(--color-accent); }
body:not([data-page="home"]) .nav-circle.top-left .nav-icon::before { content: "←"; font-size: 12px; }
.nav-circle.top-left:hover .nav-icon::before { opacity: 1; transform: translate(-50%, -50%) scale(1); }
.nav-circle.top-right .nav-icon::before, .nav-circle.top-right .nav-icon::after { content: ''; position: absolute; top: 50%; left: 50%; width: 6px; height: 6px; border: 1.2px solid var(--color-accent); border-radius: 1.5px; opacity: 0; transition: opacity calc(var(--transition-duration)*0.8) calc(var(--transition-duration)*0.2) var(--easing-standard), transform calc(var(--transition-duration)*0.8) calc(var(--transition-duration)*0.2) var(--easing-standard); transform-origin: center center; }
.nav-circle.top-right .nav-icon::before { transform: translate(calc(-50% - 1.8px), -50%) rotate(45deg) scale(0.8); }
.nav-circle.top-right .nav-icon::after { transform: translate(calc(-50% + 1.8px), -50%) rotate(45deg) scale(0.8); }
.nav-circle.top-right:hover .nav-icon::before { opacity: 1; transform: translate(calc(-50% - 1.8px), -50%) rotate(45deg) scale(1); }
.nav-circle.top-right:hover .nav-icon::after { opacity: 1; transform: translate(calc(-50% + 1.8px), -50%) rotate(45deg) scale(1); }

/* --- Unified Bottom center + / X transformation --- */
/* Base state for the lines (TINY, invisible, NO rotation) */
.nav-circle.bottom-center .nav-icon::before,
.nav-circle.bottom-center .nav-icon::after {
    content: '';
    position: absolute;
    background-color: var(--color-accent);
    top: 50%;
    left: 50%;
    width: 40%;
    height: var(--plus-line-thickness);
    transform-origin: center;
    opacity: 0;
    /* Start tiny and centered, initial rotation based on line */
    transform: translate(-50%, -50%) scale(0.05) rotate(var(--plus-initial-rotation, 0deg));
    /* Default transition (handles UNHOVER shrink and X->+ reset after close) */
    transition: transform var(--transition-duration) var(--easing-standard),
                opacity calc(var(--transition-duration) * 0.7) var(--easing-standard),
                background-color var(--transition-duration) var(--easing-standard),
                height var(--transition-duration) var(--easing-standard);
}
/* Set initial rotations for '+' lines */
.nav-circle.bottom-center .nav-icon::before { --plus-initial-rotation: 90deg; } /* Vertical line */
.nav-circle.bottom-center .nav-icon::after { --plus-initial-rotation: 0deg; }   /* Horizontal line */


/* '+' grow-in on bottom hover (when inactive) - SLOW, STRAIGHT, remains SMALL */
body:not(.menu-active) .nav-circle.bottom-center:hover .nav-icon::before,
body:not(.menu-active) .nav-circle.bottom-center:hover .nav-icon::after {
    opacity: 0.9;
    /* Grow slowly to a SMALL scale, maintaining initial rotation */
    transform: translate(-50%, -50%) scale(0.6) rotate(var(--plus-initial-rotation, 0deg));
    /* SLOWER transition ONLY for hover grow */
    transition: opacity var(--plus-grow-duration) var(--easing-out-smooth),
                transform var(--plus-grow-duration) var(--easing-out-smooth);
                /* Color/Height don't change on hover, use default transition if needed */
}

/* Fitness page specific button icon coloring */
body[data-page="fitness"] .nav-circle.bottom-center .nav-icon::before,
body[data-page="fitness"] .nav-circle.bottom-center .nav-icon::after {
    background-color: var(--color-fitness-accent);
}

/* State when menu is ACTIVE (+ transforming into X) - FAST, SNAPPY */
body.menu-active .nav-circle.bottom-center .nav-icon::before,
body.menu-active .nav-circle.bottom-center .nav-icon::after {
    /* White X for visibility on dark backgrounds */
    background-color: var(--nav-bottom-active-icon);
    opacity: 1;
    height: var(--x-line-thickness); /* X thickness */
    /* Target state: full size, rotated X */
    transform: translate(-50%, -50%) rotate(var(--x-rotation, 0deg)) scale(1);
    /* FAST transition specifically for the + to X formation */
    transition: transform var(--plus-to-x-duration) var(--easing-plus-to-x),
                opacity var(--plus-to-x-duration) ease-in-out, /* Faster fade in */
                background-color var(--plus-to-x-duration) var(--easing-standard),
                height var(--plus-to-x-duration) var(--easing-standard);
}
/* Define target X rotations */
body.menu-active .nav-circle.bottom-center .nav-icon::before {
  --x-rotation: 45deg;
}
body.menu-active .nav-circle.bottom-center .nav-icon::after {
  --x-rotation: -45deg;
}


/* Hover effect for Active X (Larger X) - Uses default transition */
body.menu-active .nav-circle.bottom-center:hover { transform: translateX(-50%) scale(1.25); }
body.menu-active .nav-circle.bottom-center:hover .nav-icon::before { transform: translate(-50%, -50%) rotate(45deg) scale(1.6); }
body.menu-active .nav-circle.bottom-center:hover .nav-icon::after { transform: translate(-50%, -50%) rotate(-45deg) scale(1.6); }
body.menu-active .nav-circle.bottom-center:active { transform: translateX(-50%) scale(1.2); }

/* Closing X animation (Dramatic Spin Out + Fall) */
 body.menu-active.closing .nav-circle.bottom-center .nav-icon::before,
 body.menu-active.closing .nav-circle.bottom-center .nav-icon::after {
     opacity: 0;
     /* Define target transform for closing */
     transform: translate(-50%, calc(-50% + 15px)) scale(0.4) rotate(var(--x-close-rotation)); /* Fall down */
     /* Use dramatic spin easing and exit duration */
     transition: transform var(--menu-item-exit-duration) var(--easing-dramatic-spin),
                 opacity calc(var(--menu-item-exit-duration) * 0.7) ease-out;
     /* Background/Height transition back using default (or specify if needed) */
 }
 /* Define closing rotations */
 body.menu-active.closing .nav-circle.bottom-center .nav-icon::before { --x-close-rotation: 225deg; }
 body.menu-active.closing .nav-circle.bottom-center .nav-icon::after { --x-close-rotation: 135deg; }
/* --- End Unified + / X Animation --- */


/* Quote Card */
.quote-card { position: fixed; left: 50%; transform: translate(-50%, 10px) scale(0.95); background-color: var(--color-card-bg); box-shadow: var(--shadow-card-active), var(--shadow-card-inset); border-radius: 4px; padding: 15px 20px; min-width: 260px; max-width: 300px; opacity: 0; visibility: hidden; z-index: 40; cursor: pointer; transition: opacity calc(var(--transition-duration) * 0.9) var(--easing-standard), visibility 0s calc(var(--transition-duration) * 0.9), transform calc(var(--transition-duration) * 0.9) var(--easing-standard), box-shadow var(--transition-duration) var(--easing-standard), top var(--transition-duration) var(--easing-standard); transform-origin: 50% 0%; }
.quote-card.active { opacity: 1; visibility: visible; transform: translate(-50%, 0) scale(1); transition: opacity var(--transition-duration) var(--easing-standard), visibility 0s, transform var(--transition-duration) var(--easing-standard), box-shadow var(--transition-duration) var(--easing-standard), top var(--transition-duration) var(--easing-standard); }
.quote-card.active:hover { box-shadow: var(--shadow-card-hover), var(--shadow-card-inset); transform: translate(-50%, -3px) scale(1.02); }
.quote-card-title { font-size: 0.9rem; font-weight: bold; margin-bottom: 6px; color: var(--color-text); font-family: 'Georgia Custom', Georgia, serif; }
.quote-card-subtitle { font-size: 0.75rem; color: var(--color-text-secondary); font-family: 'Georgia Custom', Georgia, serif; }

/* Quote Container - Centered Text */
.quote-container { text-align: center; max-width: 550px; padding: 20px; position: relative; }
.quote-container .quote-indicator-wrapper { position: absolute; bottom: 100%; left: 50%; transform: translateX(-50%); margin-bottom: 25px; }
.quote-text { font-size: 1.2rem; line-height: 1.65; margin-bottom: 1em; color: var(--color-text); font-family: 'Georgia Custom', Georgia, serif; }
.quote-attribution { font-size: 0.9rem; font-style: italic; color: var(--color-text-secondary); font-family: 'Georgia Custom', Georgia, serif; }

/* Menu - Centered Content */
.main-menu { position: fixed; top: 0; left: 0; width: 100%; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; opacity: 0; visibility: hidden; z-index: 50; background-color: var(--color-bg-overlay); backdrop-filter: blur(4px); transition: opacity var(--transition-duration) var(--easing-standard), visibility 0s var(--transition-duration), backdrop-filter var(--transition-duration) var(--easing-standard); }
.menu-wrapper { position: relative; width: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; flex-grow: 1; }

/* Base Menu Item Styles */
.main-menu a { color: var(--color-text-inverse); text-decoration: none; opacity: 0; filter: blur(0px); transition: opacity var(--transition-duration) var(--easing-standard), transform var(--transition-duration) var(--easing-standard), filter var(--focus-transition-duration) ease-out; font-family: 'Georgia Custom', Georgia, serif; text-align: center; }

/* Specific Menu Item Sizes & Positioning */
.main-menu > .menu-wrapper > a:not(.side-menu-item):not(.see-more) { font-size: 1.5rem; margin: 15px 0; transform: translateY(20px); }
.side-menu-item { position: absolute; font-size: 0.8rem; top: 50%; transform: translateX(var(--slide-offset, 0)) translateY(calc(-50% + 20px)); }
.side-menu-item.left { right: calc(50% + 240px); --slide-offset: -50px; }
.side-menu-item.right { left: calc(50% + 240px); --slide-offset: 50px; }
.see-more { font-size: 0.8rem; margin-top: 45px; margin-bottom: 35px; display: flex; flex-direction: column; align-items: center; transform: translateY(25px) scale(0.95); }
.see-more .arrow { font-size: 1rem; margin-top: 3px; display: block; }

/* Active state transforms (Slide into place) */
body.menu-active .main-menu > .menu-wrapper > a:not(.side-menu-item):not(.see-more) { transform: translateY(0); opacity: 0.9; }
body.menu-active .side-menu-item { transform: translateX(0) translateY(-50%); opacity: 0.9; }
body.menu-active .see-more { transform: translateY(0) scale(1); opacity: 0.9; }

/* Focus effect */
.menu-wrapper:has(> a:hover) > *:not(a:hover):not(.menu-logo) { filter: blur(1px); opacity: 0.7; }
.menu-wrapper > a:hover { opacity: 1 !important; filter: none !important; }

/* Menu Active State */
body.menu-active { color: var(--color-text-inverse); }
body.menu-active .main-menu { opacity: 1; visibility: visible; transition: opacity var(--transition-duration) var(--easing-standard), visibility 0s, backdrop-filter var(--transition-duration) var(--easing-standard); }
body.menu-active .nav-circle.bottom-center { background-color: var(--nav-bottom-active-bg); border-color: var(--nav-bottom-active-border); }

/* Menu closing animation */
body.menu-active.closing .main-menu a { opacity: 0; filter: blur(1px); transition: opacity var(--menu-item-exit-duration) ease-out, transform var(--menu-item-exit-duration) ease-out, filter var(--menu-item-exit-duration) ease-out; }
 body.menu-active.closing .main-menu > .menu-wrapper > a:not(.side-menu-item):not(.see-more) { transform: translateY(25px) scale(0.95); }
 body.menu-active.closing .side-menu-item { transform: translateX(var(--slide-offset, 0)) translateY(calc(-50% + 25px)) scale(0.95); }
 body.menu-active.closing .see-more { transform: translateY(30px) scale(0.9); }

/* Page transition layer */
.page-transition { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: #111; opacity: 0; visibility: hidden; z-index: 1000; transition: opacity calc(var(--transition-duration) * 1) var(--easing-standard), visibility 0s calc(var(--transition-duration) * 1); }
.page-transition.active { opacity: 1; visibility: visible; transition: opacity calc(var(--transition-duration) * 1) var(--easing-standard), visibility 0s; }

/* Staggered animations */
body.menu-active .menu-wrapper > a:nth-of-type(1) { transition-delay: 0.06s; }
body.menu-active .menu-wrapper > a:nth-of-type(2) { transition-delay: 0.12s; }
body.menu-active .menu-wrapper > a:nth-of-type(3) { transition-delay: 0.18s; }
body.menu-active .menu-wrapper > a:nth-of-type(4) { transition-delay: 0.24s; }
body.menu-active .side-menu-item { transition-delay: 0.30s; } /* Both side items share delay */
body.menu-active .see-more { transition-delay: 0.36s; }

/* Reverse staggered animations */
body.menu-active.closing .see-more { transition-delay: 0s; }
body.menu-active.closing .side-menu-item { transition-delay: 0.04s; }
body.menu-active.closing .menu-wrapper > a:nth-of-type(4) { transition-delay: 0.08s; }
body.menu-active.closing .menu-wrapper > a:nth-of-type(3) { transition-delay: 0.12s; }
body.menu-active.closing .menu-wrapper > a:nth-of-type(2) { transition-delay: 0.16s; }
body.menu-active.closing .menu-wrapper > a:nth-of-type(1) { transition-delay: 0.20s; }

/* Responsive */
@media (max-width: 768px) {
   :root { --circle-size: var(--circle-size-mobile); --circle-bottom-size: var(--circle-bottom-size-mobile); --indicator-line-width: 10px; --indicator-circle-size: 18px; }
   .logo, .menu-logo { top: 20px; font-size: 0.9rem;}
   .nav-circle.top-left { top: 25px; left: 25px; }
   .nav-circle.top-right { top: 25px; right: 25px; }
   .nav-circle.bottom-center { bottom: 30px; }
   .quote-container { max-width: 90%; padding: 15px;} .quote-container .quote-indicator-wrapper { margin-bottom: 20px; }
   .quote-text { font-size: 1.1rem; } .quote-attribution { font-size: 0.85rem; }
   .quote-card { width: calc(100% - 40px); max-width: 280px; padding: 12px 18px; } .quote-card-title { font-size: 0.85rem; } .quote-card-subtitle { font-size: 0.7rem; }

   /* Responsive Menu */
   .main-menu > .menu-wrapper > a:not(.side-menu-item):not(.see-more) { font-size: 1.4rem; margin: 12px 0; }
   .side-menu-item, .see-more { font-size: 0.75rem; }
   .side-menu-item.left { right: calc(50% + 130px); --slide-offset: -35px; }
   .side-menu-item.right { left: calc(50% + 130px); --slide-offset: 35px; }
   .see-more { margin-top: 35px; margin-bottom: 30px;}
   .see-more .arrow { font-size: 0.9rem; margin-top: 2px;}
}
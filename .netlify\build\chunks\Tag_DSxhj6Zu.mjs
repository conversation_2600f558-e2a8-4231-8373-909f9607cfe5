import { c as createAstro, a as createComponent, m as maybeRenderHead, b as addAttribute, r as renderTemplate } from './astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import 'clsx';
/* empty css                         */

const $$Astro = createAstro("https://pvb.com");
const $$Tag = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Tag;
  const { tag, tagName, size = "sm", class: className = "" } = Astro2.props;
  return renderTemplate`${maybeRenderHead()}<a${addAttribute(`/tags/${tag}`, "href")}${addAttribute([
    "tag-link",
    size === "sm" ? "tag-sm" : "tag-lg",
    className
  ], "class:list")} data-astro-cid-blwjyjpt>
#${tagName} </a> `;
}, "C:/Users/<USER>/Desktop/pvb-astro/src/components/Tag.astro", void 0);

export { $$Tag as $ };

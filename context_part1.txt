This file is a merged representation of a subset of the codebase, containing files not matching ignore patterns, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching these patterns are excluded: package-lock.json
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

<additional_info>

</additional_info>

</file_summary>

<directory_structure>
.astro/astro/content.d.ts
.astro/collections/quotes.schema.json
.astro/settings.json
.astro/types.d.ts
.gitignore
.netlify/build/<EMAIL>
.netlify/build/_astro/_slug_.CLq3GFE2.css
.netlify/build/_astro/_slug_.DONxtleH.css
.netlify/build/_astro/_tag_.Rb4hvsWq.css
.netlify/build/_astro/blog.DWMZHUHN.css
.netlify/build/_astro/work.DbR1XWHN.css
.netlify/build/_noop-middleware.mjs
.netlify/build/chunks/_@astrojs-ssr-adapter_CvSoi7hX.mjs
.netlify/build/chunks/_astro_asset-imports_D9aVaOQr.mjs
.netlify/build/chunks/_astro_content_CcaQj6Wl.mjs
.netlify/build/chunks/_astro_data-layer-content_BcEe_9wP.mjs
.netlify/build/chunks/age-of-synthesis_B-VSexue.mjs
.netlify/build/chunks/age-of-synthesis_DLAOmwU2.mjs
.netlify/build/chunks/age-of-synthesis_DqChI2cq.mjs
.netlify/build/chunks/astro_YO2Xtdzn.mjs
.netlify/build/chunks/astro/assets-service_DIMzS0Of.mjs
.netlify/build/chunks/astro/server_Dba0FyIl.mjs
.netlify/build/chunks/BlogPost_DYNjvq7J.mjs
.netlify/build/chunks/getUniqueTags_3cIFKCqm.mjs
.netlify/build/chunks/hello-world_BNhj20-t.mjs
.netlify/build/chunks/hello-world_Bu55fqv5.mjs
.netlify/build/chunks/hello-world_BwOf8HZz.mjs
.netlify/build/chunks/Layout_rXbp99fE.mjs
.netlify/build/chunks/learning_BtzpT67X.mjs
.netlify/build/chunks/learning_DcKpeM5O.mjs
.netlify/build/chunks/learning_SfTw29v4.mjs
.netlify/build/chunks/mastery-and-knowledge_B3L9fdmh.mjs
.netlify/build/chunks/mastery-and-knowledge_C1zLMmNr.mjs
.netlify/build/chunks/mastery-and-knowledge_CkJKa8VC.mjs
.netlify/build/chunks/mindfulness_3c8gSlQq.mjs
.netlify/build/chunks/mindfulness_BG5BPaMj.mjs
.netlify/build/chunks/mindfulness_DTX3H095.mjs
.netlify/build/chunks/philosophy_D9CDMOq1.mjs
.netlify/build/chunks/philosophy_EjjKZrv8.mjs
.netlify/build/chunks/philosophy_fXVfYPzN.mjs
.netlify/build/chunks/project-five_B9I66poG.mjs
.netlify/build/chunks/project-five_BCHmoapR.mjs
.netlify/build/chunks/project-five_bFgqsuz9.mjs
.netlify/build/chunks/project-four_BiijU9ph.mjs
.netlify/build/chunks/project-four_BWkJ1E-x.mjs
.netlify/build/chunks/project-four_DlJCn5JQ.mjs
.netlify/build/chunks/project-one_BLBOXsjf.mjs
.netlify/build/chunks/project-one_Cse1VnLC.mjs
.netlify/build/chunks/project-one_kU0mYB6U.mjs
.netlify/build/chunks/project-three_C8CEKmI8.mjs
.netlify/build/chunks/project-three_CCeq-lTS.mjs
.netlify/build/chunks/project-three_Dplria35.mjs
.netlify/build/chunks/project-two_BbjtZZaS.mjs
.netlify/build/chunks/project-two_BWh9uzc8.mjs
.netlify/build/chunks/project-two_CMkRBYFL.mjs
.netlify/build/chunks/slugify_CHvHojPC.mjs
.netlify/build/chunks/Tag_DSxhj6Zu.mjs
.netlify/build/entry.mjs
.netlify/build/manifest_B8ZiD9LG.mjs
.netlify/build/pages/_image.astro.mjs
.netlify/build/pages/about.astro.mjs
.netlify/build/pages/api/random-quote.json.astro.mjs
.netlify/build/pages/blog.astro.mjs
.netlify/build/pages/blog/_slug_.astro.mjs
.netlify/build/pages/blog/tag/_tag_.astro.mjs
.netlify/build/pages/blog/timeline.astro.mjs
.netlify/build/pages/index.astro.mjs
.netlify/build/pages/search.astro.mjs
.netlify/build/pages/tags.astro.mjs
.netlify/build/pages/tags/_tag_.astro.mjs
.netlify/build/pages/work.astro.mjs
.netlify/build/pages/work/_---slug_.astro.mjs
.netlify/build/renderers.mjs
.netlify/v1/config.json
.roo/mcp.json
.roomodes
astro.config.mjs
convert-md-to-json.js
netlify.toml
package.json
public/api/quotes.json
public/images/logos/bluesky.svg
public/images/logos/lesswrong.svg
public/images/logos/orcid.svg
public/images/logos/x.svg
public/images/logos/zenodo.svg
public/scripts/main.js
public/scripts/toc-enhancements.js
README.md
src/components/ArchiveCard.astro
src/components/BlogPostCard.astro
src/components/MainMenu.astro
src/components/Navigation.astro
src/components/ProjectCard.astro
src/components/Seo.astro
src/components/Tag.astro
src/components/UtopiaProjectGrid.astro
src/config.ts
src/content/.gitkeep
src/content/config.ts
src/data/ghost/.keep
src/data/ghost/authors.json
src/data/ghost/pages.json
src/data/ghost/posts.json
src/data/ghost/quotes.json
src/data/ghost/tags.json
src/data/links.ts
src/data/publications.json
src/env.d.ts
src/layouts/BlogPost.astro
src/layouts/Layout.astro
src/layouts/UtopiaLayout.astro
src/pages/404.astro
src/pages/about.astro
src/pages/api/random-quote.json.js
src/pages/blog.astro
src/pages/blog/[slug].astro
src/pages/blog/all.astro
src/pages/blog/archive.astro
src/pages/blog/archive/index.astro
src/pages/blog/page/[page].astro
src/pages/blog/tag/[tag].astro
src/pages/blog/timeline.astro
src/pages/fitness.astro
src/pages/index.astro
src/pages/links.astro
src/pages/search.astro
src/pages/tags/[tag]/index.astro
src/pages/tags/index.astro
src/pages/work.astro
src/pages/work/[slug].astro
src/pages/work/index.astro
src/scripts/main.js
src/styles/blog-pages.css
src/styles/global.css
src/styles/prose.css
src/utils/getPostsByTag.ts
src/utils/getSortedPosts.ts
src/utils/getUniqueTags.ts
src/utils/ghostContent.js
src/utils/slugify.ts
src/utils/unifiedContent.js
src/utils/unifiedSearch.js
tsconfig.json
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="src/components/ArchiveCard.astro">
---
import type { Post } from '../utils/unifiedContent.js'; // Adjust if you convert to .ts

export interface Props {
  post: Post;
}

const { post } = Astro.props;
const postDate = new Date(post.published_at).toLocaleDateString('en-US', {
    month: 'short',
    day: '2-digit'
});
---
<a href={`/blog/${post.slug}/`} class="archive-card">
  <span class="date">{postDate}</span>
  <span class="title">{post.title}</span>
</a>

<style>
  .archive-card {
    display: flex;
    align-items: baseline;
    gap: 1rem;
    padding: 0.75rem 1rem;
    border-radius: var(--theme-border-radius);
    transition: background-color 0.2s ease-in-out;
    text-decoration: none;
    color: var(--theme-text);
  }
  .archive-card:hover {
    background-color: var(--theme-bg-offset);
  }
  .archive-card .date {
    font-family: var(--font-mono);
    font-size: 0.9em;
    color: var(--theme-text-light);
    flex-shrink: 0;
    width: 50px; /* Adjust as needed */
  }
  .archive-card .title {
    font-weight: 600;
  }
</style>
</file>

<file path="src/components/Seo.astro">
---
import settings from '../data/ghost/settings.json';

export interface Props {
  title?: string;
  description?: string;
  ogImage?: string;
  canonicalURL?: URL | string;
  publishedDate?: string;
  // Add any other props you need
}

const { 
    title = settings.title, 
    description = settings.description, 
    ogImage = settings.cover_image,
    canonicalURL = new URL(Astro.url.pathname, Astro.site),
    publishedDate
} = Astro.props;

const formattedTitle = `${title} | ${settings.title}`;
const imageURL = ogImage ? new URL(ogImage, Astro.site).href : new URL('/placeholder.jpg', Astro.site).href;
---
<meta charset="utf-8" />
<title>{formattedTitle}</title>
<meta name="description" content={description} />
<link rel="canonical" href={canonicalURL} />

<meta property="og:title" content={title} />
<meta property="og:description" content={description} />
<meta property="og:url" content={canonicalURL} />
<meta property="og:site_name" content={settings.title} />
<meta property="og:image" content={imageURL} />
<meta property="og:type" content={publishedDate ? "article" : "website"} />
{publishedDate && <meta property="article:published_time" content={publishedDate} />}

<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content={title} />
<meta name="twitter:description" content={description} />
<meta name="twitter:image" content={imageURL} />

<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
<meta name="theme-color" content="#ffffff" />
</file>

<file path="src/styles/prose.css">
.prose {
    color: var(--theme-text);
    line-height: 1.7;
}

.prose > *:first-child {
    margin-top: 0;
}

.prose h1, .prose h2, .prose h3, .prose h4 {
    color: var(--theme-accent-2);
    margin: 2.5rem 0 1rem;
    line-height: 1.2;
}

.prose p {
    margin-bottom: 1.25rem;
}

.prose a {
    color: var(--theme-accent);
    text-decoration: underline;
    text-decoration-color: var(--theme-accent-2);
}

.prose blockquote {
    border-left: 4px solid var(--theme-accent);
    padding-left: 1rem;
    margin-left: 0;
    font-style: italic;
    color: var(--theme-text-light);
}

.prose pre {
    background-color: var(--theme-bg-offset);
    padding: 1rem;
    border-radius: var(--theme-border-radius);
    overflow-x: auto;
}

.prose code {
    font-family: var(--font-mono);
    background-color: var(--theme-bg-offset);
    padding: 0.2em 0.4em;
    font-size: 0.9em;
    border-radius: 4px;
}

.prose pre > code {
    background-color: transparent;
    padding: 0;
    font-size: 1em;
}
/* Add more styles for ul, ol, li, tables, etc. */
</file>

<file path=".astro/collections/quotes.schema.json">
{
  "$ref": "#/definitions/quotes",
  "definitions": {
    "quotes": {
      "type": "object",
      "properties": {
        "text": {
          "type": "string"
        },
        "author": {
          "type": "string",
          "default": "PVB"
        },
        "linkedPage": {
          "type": "string"
        },
        "cardTitle": {
          "type": "string"
        },
        "cardSubtitle": {
          "type": "string"
        },
        "featured": {
          "type": "boolean",
          "default": false
        },
        "tags": {
          "type": "array",
          "items": {
            "type": "string"
          },
          "default": []
        },
        "$schema": {
          "type": "string"
        }
      },
      "required": [
        "text",
        "cardTitle",
        "cardSubtitle"
      ],
      "additionalProperties": false
    }
  },
  "$schema": "http://json-schema.org/draft-07/schema#"
}
</file>

<file path=".gitignore">
node_modules
</file>

<file path=".netlify/build/<EMAIL>">
export * from '@astrojs/netlify/ssr-function.js';
</file>

<file path=".netlify/build/_astro/_slug_.CLq3GFE2.css">
.nav-circle[data-astro-cid-pux6a34n].top-left,.nav-circle[data-astro-cid-pux6a34n].top-right{position:fixed;width:var(--circle-size);height:var(--circle-size);border-radius:50%;background-color:var(--color-accent);cursor:pointer;z-index:100;top:30px;display:flex;justify-content:center;align-items:center;border:var(--circle-border-width) solid var(--color-accent);transition:transform var(--transition-duration) var(--easing-standard),background-color var(--transition-duration) var(--easing-standard),border-color var(--transition-duration) var(--easing-standard)}.nav-circle[data-astro-cid-pux6a34n].top-left{left:30px}.nav-circle[data-astro-cid-pux6a34n].top-right{right:30px}.nav-circle[data-astro-cid-pux6a34n].top-left:hover,.nav-circle[data-astro-cid-pux6a34n].top-right:hover{transform:scale(var(--circle-expand-scale));background-color:transparent}.nav-circle[data-astro-cid-pux6a34n].top-left .nav-icon[data-astro-cid-pux6a34n]:before{content:"?";position:absolute;top:50%;left:50%;transform:translate(-50%,-50%) scale(.8);font-size:10px;opacity:0;transition:opacity calc(var(--transition-duration)*.8) calc(var(--transition-duration)*.2) var(--easing-standard),transform calc(var(--transition-duration)*.8) calc(var(--transition-duration)*.2) var(--easing-standard);color:var(--color-accent)}body:not([data-page=home]) .nav-circle[data-astro-cid-pux6a34n].top-left .nav-icon[data-astro-cid-pux6a34n]:before{content:"←";font-size:12px}.nav-circle[data-astro-cid-pux6a34n].top-left:hover .nav-icon[data-astro-cid-pux6a34n]:before{opacity:1;transform:translate(-50%,-50%) scale(1)}.nav-circle[data-astro-cid-pux6a34n].top-right .nav-icon[data-astro-cid-pux6a34n]:before,.nav-circle[data-astro-cid-pux6a34n].top-right .nav-icon[data-astro-cid-pux6a34n]:after{content:"";position:absolute;top:50%;left:50%;width:6px;height:6px;border:1.2px solid var(--color-accent);border-radius:1.5px;opacity:0;transition:opacity calc(var(--transition-duration)*.8) calc(var(--transition-duration)*.2) var(--easing-standard),transform calc(var(--transition-duration)*.8) calc(var(--transition-duration)*.2) var(--easing-standard);transform-origin:center center}.nav-circle[data-astro-cid-pux6a34n].top-right .nav-icon[data-astro-cid-pux6a34n]:before{transform:translate(calc(-50% - 1.8px),-50%) rotate(45deg) scale(.8)}.nav-circle[data-astro-cid-pux6a34n].top-right .nav-icon[data-astro-cid-pux6a34n]:after{transform:translate(calc(-50% + 1.8px),-50%) rotate(45deg) scale(.8)}.nav-circle[data-astro-cid-pux6a34n].top-right:hover .nav-icon[data-astro-cid-pux6a34n]:before{opacity:1;transform:translate(calc(-50% - 1.8px),-50%) rotate(45deg) scale(1)}.nav-circle[data-astro-cid-pux6a34n].top-right:hover .nav-icon[data-astro-cid-pux6a34n]:after{opacity:1;transform:translate(calc(-50% + 1.8px),-50%) rotate(45deg) scale(1)}body.menu-active .nav-circle[data-astro-cid-pux6a34n].top-left,body.menu-active .nav-circle[data-astro-cid-pux6a34n].top-right{opacity:0;visibility:hidden;pointer-events:none;transition:opacity var(--transition-duration) var(--easing-standard),visibility 0s var(--transition-duration)}body:not(.menu-active) .nav-circle[data-astro-cid-pux6a34n].top-left,body:not(.menu-active) .nav-circle[data-astro-cid-pux6a34n].top-right{transition-delay:calc(var(--transition-duration) * .2)}@media (max-width: 768px){.nav-circle[data-astro-cid-pux6a34n].top-left,.nav-circle[data-astro-cid-pux6a34n].top-right{top:25px;width:var(--circle-size);height:var(--circle-size)}.nav-circle[data-astro-cid-pux6a34n].top-left{left:25px}.nav-circle[data-astro-cid-pux6a34n].top-right{right:25px}}@font-face{font-family:Georgia Custom;src:url(/fonts/georgia-ref.ttf) format("truetype");font-weight:400;font-style:normal}@font-face{font-family:Georgia Custom;src:url(/fonts/georgia-bold-2.ttf) format("truetype");font-weight:700;font-style:normal}@font-face{font-family:Serif12;src:url(/fonts/serif12-beta-regular.otf) format("opentype");font-weight:400;font-style:normal}:root{--circle-size: 18px;--circle-bottom-size: 36px;--indicator-line-width: 12px;--indicator-line-height: 2px;--indicator-circle-size: 20px;--indicator-wrapper-size: 44px;--circle-size-mobile: 16px;--circle-bottom-size-mobile: 32px;--circle-border-width: 1.5px;--circle-expand-scale: 1.6;--x-line-thickness: .8px;--plus-line-thickness: .9px;--transition-duration: .4s;--bottom-button-duration: .6s;--bottom-hover-duration: .5s;--plus-grow-duration: .7s;--plus-to-x-duration: .35s;--easing-standard: cubic-bezier(.25, .1, .25, 1);--easing-out-smooth: ease-out;--easing-plus-to-x: cubic-bezier(.34, 1.56, .64, 1);--easing-dramatic-spin: cubic-bezier(.68, -.55, .27, 1.55);--focus-transition-duration: .2s;--menu-item-exit-duration: calc(var(--transition-duration) * .95);--color-bg: rgba(250, 246, 242, .9);--color-bg-overlay: rgba(0, 0, 0, .96);--color-card-bg: #fdfbf9;--color-accent: #3a2c23;--color-accent-darker: #2a1f1a;--color-accent-inverse: #fff;--color-inactive: #8a8178;--color-text: var(--color-accent);--color-text-secondary: #6a5a4f;--color-text-inverse: #f0f0f0;--color-logo-menu: rgba(240, 240, 240, .7);--shadow-card: 0 6px 20px rgba(0, 0, 0, .08);--shadow-card-hover: 0 10px 25px rgba(0, 0, 0, .15);--shadow-card-active: 0 4px 15px rgba(0, 0, 0, .1);--shadow-card-inset: inset 0 0 10px rgba(0, 0, 0, .03);--button-bg: #d0d0d0;--button-hover-bg: #c0c0c0;--button-active-bg: #b8b8b8;--button-text: #222222;--blog-bg-tint: rgba(10, 10, 10, .94);--blog-text: rgba(240, 240, 240, .9);--blog-text-secondary: rgba(210, 210, 210, .85);--blog-border: rgba(200, 200, 200, .15)}html{scrollbar-width:thin;scrollbar-color:rgba(100,100,100,.4) transparent}*{margin:0;padding:0;box-sizing:border-box}html,body{height:100%;font-family:Georgia Custom,Georgia,serif;color:var(--color-text)}body{display:flex;justify-content:center;align-items:center;position:relative;transition:background-color var(--transition-duration) var(--easing-standard);background-size:cover;background-position:center;background-repeat:no-repeat;overflow-y:auto}html{scroll-behavior:smooth}.content-wrapper{position:fixed;top:0;left:0;width:100%;height:100%;display:flex;justify-content:center;align-items:center;flex-direction:column;transition:filter var(--transition-duration) var(--easing-standard),opacity var(--transition-duration) var(--easing-standard);z-index:1}body.menu-active .content-wrapper{filter:blur(4px);opacity:.5;transition:filter calc(var(--transition-duration) * 1.1) var(--easing-standard),opacity calc(var(--transition-duration) * 1.1) var(--easing-standard)}body.quote-card-active .content-wrapper{filter:blur(3px);opacity:.7}body[data-page=blog] .content-wrapper,body[data-page=blog-post] .content-wrapper,body[data-page=about] .content-wrapper{position:relative;justify-content:flex-start;.toc-container{display:none}height:auto;min-height:100vh}.logo,.menu-logo{position:absolute;top:30px;left:50%;transform:translate(-50%);font-size:1rem;font-weight:400;z-index:10;font-family:Serif12,Georgia,serif;text-decoration:none;cursor:pointer;color:var(--color-text-secondary);transition:color var(--transition-duration) var(--easing-standard),opacity var(--transition-duration) var(--easing-standard);text-align:center}.logo:hover,.menu-logo:hover{color:var(--color-text)}.menu-logo{color:var(--color-logo-menu);opacity:0;z-index:51}.menu-logo:hover{color:var(--color-text-inverse)}body.menu-active .menu-logo{opacity:1;transition-delay:calc(var(--transition-duration) * .5)}body.menu-active.closing .menu-logo{opacity:0;transition:opacity calc(var(--transition-duration)*.5) ease-out;transition-delay:0s}.nav-circle{position:absolute;width:var(--circle-size);height:var(--circle-size);border-radius:50%;cursor:pointer;display:flex;justify-content:center;align-items:center;z-index:100;border:var(--circle-border-width) solid var(--color-accent);transition:transform var(--transition-duration) var(--easing-standard),background-color var(--transition-duration) var(--easing-standard),border-color var(--transition-duration) var(--easing-standard),width var(--transition-duration) var(--easing-standard),height var(--transition-duration) var(--easing-standard),opacity var(--transition-duration) var(--easing-standard),visibility 0s var(--transition-duration);opacity:1;visibility:visible;transform-origin:center center}.nav-circle.top-left,.nav-circle.top-right{background-color:var(--color-accent);border-color:var(--color-accent);top:30px}.nav-circle.top-left{left:30px}.nav-circle.top-right{right:30px}.nav-circle.bottom-center{background-color:transparent;width:var(--circle-bottom-size);height:var(--circle-bottom-size);bottom:30px;left:50%;transform:translate(-50%);transition:transform var(--bottom-button-duration) var(--easing-standard),background-color var(--bottom-button-duration) var(--easing-standard),border-color var(--bottom-button-duration) var(--easing-standard),width var(--transition-duration) var(--easing-standard),height var(--transition-duration) var(--easing-standard)}body[data-page=blog-post] .nav-circle.top-right{display:none}body[data-page=blog].menu-active .nav-circle.bottom-center,body[data-page=blog-post].menu-active .nav-circle.bottom-center,body[data-page=tag].menu-active .nav-circle.bottom-center,body[data-page=tags].menu-active .nav-circle.bottom-center,body[data-page=about].menu-active .nav-circle.bottom-center{background-color:#141414f2;border-color:#282828f2}body[data-page=home] .nav-circle.bottom-center{background-color:transparent;border-color:#222}body[data-page=blog] .nav-circle.top-left,body[data-page=blog] .nav-circle.top-right,body[data-page=blog-post] .nav-circle.top-left,body[data-page=tag] .nav-circle.top-left,body[data-page=tag] .nav-circle.top-right,body[data-page=tags] .nav-circle.top-left,body[data-page=tags] .nav-circle.top-right{background-color:var(--color-accent-inverse);border-color:#222}body[data-page=blog] .nav-circle.top-left:hover .nav-icon:before,body[data-page=blog] .nav-circle.top-right:hover .nav-icon:before,body[data-page=blog] .nav-circle.top-right:hover .nav-icon:after,body[data-page=blog-post] .nav-circle.top-left:hover .nav-icon:before,body[data-page=tag] .nav-circle.top-left:hover .nav-icon:before,body[data-page=tag] .nav-circle.top-right:hover .nav-icon:before,body[data-page=tag] .nav-circle.top-right:hover .nav-icon:after,body[data-page=tags] .nav-circle.top-left:hover .nav-icon:before,body[data-page=tags] .nav-circle.top-right:hover .nav-icon:before,body[data-page=tags] .nav-circle.top-right:hover .nav-icon:after{color:var(--color-accent-inverse);border-color:var(--color-accent-inverse)}body.menu-active .nav-circle.top-left,body.menu-active .nav-circle.top-right{opacity:0;visibility:hidden;pointer-events:none;transition:opacity var(--transition-duration) var(--easing-standard),visibility 0s var(--transition-duration)}body:not(.menu-active) .nav-circle.top-left,body:not(.menu-active) .nav-circle.top-right{transition-delay:calc(var(--transition-duration) * .2)}.nav-circle:active{transform:scale(.95);transition:transform .1s ease-out}.nav-circle.bottom-center:active{transform:translate(-50%) scale(.95)}.nav-circle.top-left:hover,.nav-circle.top-right:hover{transform:scale(var(--circle-expand-scale));background-color:transparent}body:not(.menu-active) .nav-circle.bottom-center:hover{transform:translate(-50%) scale(1.5);transition:transform var(--bottom-hover-duration) var(--easing-standard)}body:not(.menu-active) .nav-circle.bottom-center:hover:active{transform:translate(-50%) scale(1.45)}.quote-indicator-wrapper{position:absolute;width:var(--indicator-wrapper-size);height:var(--indicator-wrapper-size);display:flex;justify-content:center;align-items:center;cursor:pointer;z-index:50}.quote-indicator{width:var(--indicator-line-width);height:var(--indicator-line-height);background-color:var(--color-inactive);border-radius:1px;transition:all var(--transition-duration) var(--easing-standard)}.quote-indicator-wrapper.active .quote-indicator{width:var(--indicator-circle-size);height:var(--indicator-circle-size);background-color:transparent;border:var(--circle-border-width) solid var(--color-inactive);border-radius:50%}.quote-indicator-wrapper.active:hover .quote-indicator{transform:scale(1.15)}.nav-icon{position:relative;width:100%;height:100%;display:flex;justify-content:center;align-items:center;color:var(--color-accent);transition:color var(--transition-duration) var(--easing-standard);line-height:1}.nav-circle.top-left .nav-icon:before{content:"?";position:absolute;top:50%;left:50%;transform:translate(-50%,-50%) scale(.8);font-size:10px;opacity:0;transition:opacity calc(var(--transition-duration)*.8) calc(var(--transition-duration)*.2) var(--easing-standard),transform calc(var(--transition-duration)*.8) calc(var(--transition-duration)*.2) var(--easing-standard);color:var(--color-accent)}body:not([data-page=home]) .nav-circle.top-left .nav-icon:before{content:"←";font-size:12px}.nav-circle.top-left:hover .nav-icon:before{opacity:1;transform:translate(-50%,-50%) scale(1)}.nav-circle.top-right .nav-icon:before,.nav-circle.top-right .nav-icon:after{content:"";position:absolute;top:50%;left:50%;width:6px;height:6px;border:1.2px solid var(--color-accent);border-radius:1.5px;opacity:0;transition:opacity calc(var(--transition-duration)*.8) calc(var(--transition-duration)*.2) var(--easing-standard),transform calc(var(--transition-duration)*.8) calc(var(--transition-duration)*.2) var(--easing-standard);transform-origin:center center}.nav-circle.top-right .nav-icon:before{transform:translate(calc(-50% - 1.8px),-50%) rotate(45deg) scale(.8)}.nav-circle.top-right .nav-icon:after{transform:translate(calc(-50% + 1.8px),-50%) rotate(45deg) scale(.8)}.nav-circle.top-right:hover .nav-icon:before{opacity:1;transform:translate(calc(-50% - 1.8px),-50%) rotate(45deg) scale(1)}.nav-circle.top-right:hover .nav-icon:after{opacity:1;transform:translate(calc(-50% + 1.8px),-50%) rotate(45deg) scale(1)}.nav-circle.bottom-center .nav-icon:before,.nav-circle.bottom-center .nav-icon:after{content:"";position:absolute;background-color:var(--color-accent);top:50%;left:50%;width:40%;height:var(--plus-line-thickness);transform-origin:center;opacity:0;transform:translate(-50%,-50%) scale(.05) rotate(var(--plus-initial-rotation, 0deg));transition:transform var(--transition-duration) var(--easing-standard),opacity calc(var(--transition-duration) * .7) var(--easing-standard),background-color var(--transition-duration) var(--easing-standard),height var(--transition-duration) var(--easing-standard)}.nav-circle.bottom-center .nav-icon:before{--plus-initial-rotation: 90deg}.nav-circle.bottom-center .nav-icon:after{--plus-initial-rotation: 0deg}body:not(.menu-active) .nav-circle.bottom-center:hover .nav-icon:before,body:not(.menu-active) .nav-circle.bottom-center:hover .nav-icon:after{opacity:.9;transform:translate(-50%,-50%) scale(.6) rotate(var(--plus-initial-rotation, 0deg));transition:opacity var(--plus-grow-duration) var(--easing-out-smooth),transform var(--plus-grow-duration) var(--easing-out-smooth)}body.menu-active .nav-circle.bottom-center .nav-icon:before,body.menu-active .nav-circle.bottom-center .nav-icon:after{background-color:var(--color-accent-inverse);opacity:1;height:var(--x-line-thickness);transform:translate(-50%,-50%) rotate(var(--x-rotation, 0deg)) scale(1);transition:transform var(--plus-to-x-duration) var(--easing-plus-to-x),opacity var(--plus-to-x-duration) ease-in-out,background-color var(--plus-to-x-duration) var(--easing-standard),height var(--plus-to-x-duration) var(--easing-standard)}body.menu-active .nav-circle.bottom-center .nav-icon:before{--x-rotation: 45deg;background-color:var(--color-accent-inverse)}body.menu-active .nav-circle.bottom-center .nav-icon:after{--x-rotation: -45deg;background-color:var(--color-accent-inverse)}body.menu-active .nav-circle.bottom-center:hover{transform:translate(-50%) scale(1.25)}body.menu-active .nav-circle.bottom-center:hover .nav-icon:before{transform:translate(-50%,-50%) rotate(45deg) scale(1.6)}body.menu-active .nav-circle.bottom-center:hover .nav-icon:after{transform:translate(-50%,-50%) rotate(-45deg) scale(1.6)}body.menu-active .nav-circle.bottom-center:active{transform:translate(-50%) scale(1.2)}body.menu-active.closing .nav-circle.bottom-center .nav-icon:before,body.menu-active.closing .nav-circle.bottom-center .nav-icon:after{opacity:0;transform:translate(-50%,calc(-50% + 15px)) scale(.4) rotate(var(--x-close-rotation));transition:transform var(--menu-item-exit-duration) var(--easing-dramatic-spin),opacity calc(var(--menu-item-exit-duration) * .7) ease-out}body.menu-active.closing .nav-circle.bottom-center .nav-icon:before{--x-close-rotation: 225deg}body.menu-active.closing .nav-circle.bottom-center .nav-icon:after{--x-close-rotation: 135deg}.quote-card{position:fixed;left:50%;transform:translate(-50%,10px) scale(.95);background-color:var(--color-card-bg);box-shadow:var(--shadow-card-active),var(--shadow-card-inset);border-radius:4px;padding:15px 20px;min-width:260px;max-width:300px;opacity:0;visibility:hidden;z-index:40;cursor:pointer;transition:opacity calc(var(--transition-duration) * .9) var(--easing-standard),visibility 0s calc(var(--transition-duration) * .9),transform calc(var(--transition-duration) * .9) var(--easing-standard),box-shadow var(--transition-duration) var(--easing-standard),top var(--transition-duration) var(--easing-standard);transform-origin:50% 0%}.quote-card.active{opacity:1;visibility:visible;transform:translate(-50%) scale(1);transition:opacity var(--transition-duration) var(--easing-standard),visibility 0s,transform var(--transition-duration) var(--easing-standard),box-shadow var(--transition-duration) var(--easing-standard),top var(--transition-duration) var(--easing-standard)}.quote-card.active:hover{box-shadow:var(--shadow-card-hover),var(--shadow-card-inset);transform:translate(-50%,-3px) scale(1.02)}.quote-card-title{font-size:.9rem;font-weight:700;margin-bottom:6px;color:var(--color-text);font-family:Georgia Custom,Georgia,serif}.quote-card-subtitle{font-size:.75rem;color:var(--color-text-secondary);font-family:Georgia Custom,Georgia,serif}.quote-container{text-align:center;max-width:550px;padding:20px;position:relative}.quote-container .quote-indicator-wrapper{position:absolute;bottom:100%;left:50%;transform:translate(-50%);margin-bottom:25px}.quote-text{font-size:1.2rem;line-height:1.65;margin-bottom:1em;color:var(--color-text);font-family:Georgia Custom,Georgia,serif}.quote-attribution{font-size:.9rem;font-style:italic;color:var(--color-text-secondary);font-family:Georgia Custom,Georgia,serif}.main-menu{position:fixed;top:0;left:0;width:100%;height:100%;display:flex;flex-direction:column;align-items:center;justify-content:center;opacity:0;visibility:hidden;z-index:50;background-color:var(--color-bg-overlay);backdrop-filter:blur(4px);transition:opacity var(--transition-duration) var(--easing-standard),visibility 0s var(--transition-duration),backdrop-filter var(--transition-duration) var(--easing-standard)}.menu-wrapper{position:relative;width:100%;display:flex;flex-direction:column;align-items:center;justify-content:center;flex-grow:1}.main-menu a{color:var(--color-text-inverse);text-decoration:none;opacity:0;filter:blur(0px);transition:opacity var(--transition-duration) var(--easing-standard),transform var(--transition-duration) var(--easing-standard),filter var(--focus-transition-duration) ease-out;font-family:Georgia Custom,Georgia,serif;text-align:center}.main-menu>.menu-wrapper>a:not(.side-menu-item):not(.see-more){font-size:1.5rem;margin:15px 0;transform:translateY(20px)}.side-menu-item{position:absolute;font-size:.8rem;top:50%;transform:translate(var(--slide-offset, 0)) translateY(calc(-50% + 20px))}.side-menu-item.left{right:calc(50% + 240px);--slide-offset: -50px}.side-menu-item.right{left:calc(50% + 240px);--slide-offset: 50px}.see-more{font-size:.8rem;margin-top:45px;margin-bottom:35px;display:flex;flex-direction:column;align-items:center;transform:translateY(25px) scale(.95)}.see-more .arrow{font-size:1rem;margin-top:3px;display:block}body.menu-active .main-menu>.menu-wrapper>a:not(.side-menu-item):not(.see-more){transform:translateY(0);opacity:.9}body.menu-active .side-menu-item{transform:translate(0) translateY(-50%);opacity:.9}body.menu-active .see-more{transform:translateY(0) scale(1);opacity:.9}.menu-wrapper:has(>a:hover)>*:not(a:hover):not(.menu-logo){filter:blur(1px);opacity:.7}.menu-wrapper>a:hover{opacity:1!important;filter:none!important}body.menu-active{color:var(--color-text-inverse)}body.menu-active .main-menu{opacity:1;visibility:visible;transition:opacity var(--transition-duration) var(--easing-standard),visibility 0s,backdrop-filter var(--transition-duration) var(--easing-standard)}body.menu-active .nav-circle.bottom-center{background-color:#141414f2;border-color:#1e1e1ef2}body.menu-active.closing .main-menu a{opacity:0;filter:blur(1px);transition:opacity var(--menu-item-exit-duration) ease-out,transform var(--menu-item-exit-duration) ease-out,filter var(--menu-item-exit-duration) ease-out}body.menu-active.closing .main-menu>.menu-wrapper>a:not(.side-menu-item):not(.see-more){transform:translateY(25px) scale(.95)}body.menu-active.closing .side-menu-item{transform:translate(var(--slide-offset, 0)) translateY(calc(-50% + 25px)) scale(.95)}body.menu-active.closing .see-more{transform:translateY(30px) scale(.9)}.page-transition{position:fixed;top:0;left:0;width:100%;height:100%;background-color:#111;opacity:0;visibility:hidden;z-index:1000;transition:opacity calc(var(--transition-duration) * 1) var(--easing-standard),visibility 0s calc(var(--transition-duration) * 1)}.page-transition.active{opacity:1;visibility:visible;transition:opacity calc(var(--transition-duration) * 1) var(--easing-standard),visibility 0s}body.menu-active .menu-wrapper>a:nth-of-type(1){transition-delay:.06s}body.menu-active .menu-wrapper>a:nth-of-type(2){transition-delay:.12s}body.menu-active .menu-wrapper>a:nth-of-type(3){transition-delay:.18s}body.menu-active .menu-wrapper>a:nth-of-type(4){transition-delay:.24s}body.menu-active .side-menu-item{transition-delay:.3s}body.menu-active .see-more{transition-delay:.36s}body.menu-active.closing .see-more{transition-delay:0s}body.menu-active.closing .side-menu-item{transition-delay:.04s}body.menu-active.closing .menu-wrapper>a:nth-of-type(4){transition-delay:.08s}body.menu-active.closing .menu-wrapper>a:nth-of-type(3){transition-delay:.12s}body.menu-active.closing .menu-wrapper>a:nth-of-type(2){transition-delay:.16s}body.menu-active.closing .menu-wrapper>a:nth-of-type(1){transition-delay:.2s}@media (max-width: 768px){:root{--circle-size: var(--circle-size-mobile);--circle-bottom-size: var(--circle-bottom-size-mobile);--indicator-line-width: 10px;--indicator-circle-size: 18px}.logo,.menu-logo{top:20px;font-size:.9rem}.nav-circle.top-left{top:25px;left:25px}.nav-circle.top-right{top:25px;right:25px}.nav-circle.bottom-center{bottom:30px}.quote-container{max-width:90%;padding:15px}.quote-container .quote-indicator-wrapper{margin-bottom:20px}.quote-text{font-size:1.1rem}.quote-attribution{font-size:.85rem}.quote-card{width:calc(100% - 40px);max-width:280px;padding:12px 18px}.quote-card-title{font-size:.85rem}.quote-card-subtitle{font-size:.7rem}.main-menu>.menu-wrapper>a:not(.side-menu-item):not(.see-more){font-size:1.4rem;margin:12px 0}.side-menu-item,.see-more{font-size:.75rem}.side-menu-item.left{right:calc(50% + 130px);--slide-offset: -35px}.side-menu-item.right{left:calc(50% + 130px);--slide-offset: 35px}.see-more{margin-top:35px;margin-bottom:30px}.see-more .arrow{font-size:.9rem;margin-top:2px}}html,body{scroll-behavior:smooth;margin:0;padding:0;height:100%;scrollbar-width:thin;scrollbar-color:rgba(100,100,100,.4) transparent}::-webkit-scrollbar{width:8px;height:8px}::-webkit-scrollbar-track{background:transparent}::-webkit-scrollbar-thumb{background-color:#64646466;border-radius:4px}::-webkit-scrollbar-thumb:hover{background-color:#78787899}body{display:flex;justify-content:center;align-items:center;position:relative;transition:background-color var(--transition-duration) var(--easing-standard);background-size:cover;background-position:center;background-repeat:no-repeat;background-blend-mode:normal;font-family:Georgia Custom,Georgia,serif}.logo[data-astro-cid-sckkx6r4]{position:fixed;top:30px;left:50%;transform:translate(-50%);font-size:1rem;font-weight:400;z-index:10;font-family:Georgia Custom,Georgia,serif;text-decoration:none;cursor:pointer;color:var(--color-accent);transition:color var(--transition-duration) var(--easing-standard)}.logo[data-astro-cid-sckkx6r4]:hover{opacity:.85}.content-wrapper[data-astro-cid-sckkx6r4]{position:fixed;top:0;left:0;width:100%;height:100%;display:flex;justify-content:center;align-items:center;flex-direction:column;transition:filter var(--transition-duration) var(--easing-standard),opacity var(--transition-duration) var(--easing-standard);z-index:1}body[data-page=blog] .content-wrapper[data-astro-cid-sckkx6r4],body[data-page=blog-post] .content-wrapper[data-astro-cid-sckkx6r4],body[data-page=work] .content-wrapper[data-astro-cid-sckkx6r4],body[data-page=work-post] .content-wrapper[data-astro-cid-sckkx6r4]{position:relative;justify-content:flex-start;align-items:stretch;height:auto;min-height:100vh}body[data-page=about]{background-size:cover;background-position:center;background-repeat:no-repeat;background-attachment:fixed}.content-wrapper[data-astro-cid-sckkx6r4]{overflow-x:hidden}.nav-circle[data-astro-cid-sckkx6r4].bottom-center{position:fixed!important;bottom:30px;left:50%;transform:translate(-50%);z-index:100;width:var(--circle-bottom-size);height:var(--circle-bottom-size);border-radius:50%;border:var(--circle-border-width) solid rgba(50,50,50,.8);background-color:#32323299;cursor:pointer;display:flex;justify-content:center;align-items:center;transition:transform var(--bottom-button-duration) var(--easing-standard),background-color var(--bottom-button-duration) var(--easing-standard),border-color var(--bottom-button-duration) var(--easing-standard),opacity .3s ease}.nav-circle[data-astro-cid-sckkx6r4].bottom-center .nav-icon[data-astro-cid-sckkx6r4]{position:relative;width:100%;height:100%;display:flex;justify-content:center;align-items:center}body:not(.menu-active) .nav-circle[data-astro-cid-sckkx6r4].bottom-center:hover{transform:translate(-50%) scale(1.5)}body.menu-active .nav-circle[data-astro-cid-sckkx6r4].bottom-center{background-color:var(--color-accent);border-color:var(--color-accent)}.page-transition[data-astro-cid-sckkx6r4]{position:fixed;top:0;left:0;width:100%;height:100%;background-color:#111;opacity:1;visibility:visible;z-index:1000;transition:opacity var(--transition-duration) var(--easing-standard),visibility 0s var(--transition-duration)}.page-transition[data-astro-cid-sckkx6r4].active{opacity:1;visibility:visible;transition:opacity var(--transition-duration) var(--easing-standard),visibility 0s}.page-transition[data-astro-cid-sckkx6r4]:not(.active){opacity:0;visibility:hidden}body.menu-active .content-wrapper[data-astro-cid-sckkx6r4]{filter:blur(4px);opacity:.5}@media (max-width: 768px){.logo[data-astro-cid-sckkx6r4]{top:20px;font-size:.9rem}.nav-circle[data-astro-cid-sckkx6r4].bottom-center{bottom:25px}}:root{--circle-size: 18px;--circle-bottom-size: 36px;--circle-border-width: 1.5px;--circle-expand-scale: 1.6;--transition-duration: .4s;--bottom-button-duration: .6s;--easing-standard: cubic-bezier(.25, .1, .25, 1);--menu-item-exit-duration: calc(var(--transition-duration) * .95)}@media (max-width: 768px){:root{--circle-size: 16px;--circle-bottom-size: 32px}}
</file>

<file path=".netlify/build/_astro/_slug_.DONxtleH.css">
html{scrollbar-width:thin;scrollbar-color:rgba(100,100,100,.4) transparent}::-webkit-scrollbar{width:8px;height:8px}::-webkit-scrollbar-track{background:transparent}::-webkit-scrollbar-thumb{background-color:#64646466;border-radius:4px}::-webkit-scrollbar-thumb:hover{background-color:#78787899}body,.blog-post,.toc-panel{scrollbar-width:thin;overflow-y:auto}body[data-page=work-post]{overflow-y:auto;overflow-x:hidden;height:auto;min-height:100vh}.blog-header[data-astro-cid-bvzihdzo]{width:100%;display:flex;justify-content:center;margin:55px 0 20px}.blog-title[data-astro-cid-bvzihdzo]{font-family:Georgia Custom,Georgia,serif;font-size:1.3rem;color:#f0f0f0d9;letter-spacing:-.01em;position:relative}.blog-title[data-astro-cid-bvzihdzo]:after{content:"";position:absolute;bottom:-8px;left:50%;transform:translate(-50%);width:40px;height:1px;background-color:#f0f0f066}.toc-toggle[data-astro-cid-bvzihdzo]{position:fixed;top:30px;right:30px;width:20px;height:20px;background-color:#ffffffe6;border:none;border-radius:3px;cursor:pointer;z-index:110;padding:0;display:flex;justify-content:center;align-items:center;transition:transform var(--transition-duration) var(--easing-standard),background-color var(--transition-duration) var(--easing-standard),border var(--transition-duration) var(--easing-standard);transform-origin:center}.toc-toggle[data-astro-cid-bvzihdzo]:hover{transform:scale(1.2);background-color:transparent;border:1.5px solid rgba(255,255,255,.9)}.toc-toggle[data-astro-cid-bvzihdzo].active{background-color:transparent;border:1.5px solid rgba(255,255,255,.9)}.toc-icon-dots[data-astro-cid-bvzihdzo]{display:flex;flex-direction:column;justify-content:center;align-items:center;gap:2px;opacity:0;transition:opacity var(--transition-duration) var(--easing-standard)}.toc-toggle[data-astro-cid-bvzihdzo]:hover .toc-icon-dots[data-astro-cid-bvzihdzo],.toc-toggle[data-astro-cid-bvzihdzo].active .toc-icon-dots[data-astro-cid-bvzihdzo]{opacity:1}.dot[data-astro-cid-bvzihdzo]{width:2.5px;height:2.5px;background-color:#ffffffe6;border-radius:50%;transition:width var(--transition-duration) var(--easing-standard),height var(--transition-duration) var(--easing-standard)}.toc-toggle[data-astro-cid-bvzihdzo].active:hover .dot[data-astro-cid-bvzihdzo]{width:10px;height:1.5px;border-radius:1px}.toc-panel[data-astro-cid-bvzihdzo]{position:fixed;top:0;right:-350px;width:300px;height:100vh;z-index:100;overflow:hidden;transition:right .6s cubic-bezier(.25,.1,.25,1)}.toc-panel-inner[data-astro-cid-bvzihdzo]{position:absolute;top:0;left:0;width:100%;height:100%;background-color:#141414d9;backdrop-filter:blur(8px);-webkit-backdrop-filter:blur(8px);padding:80px 20px 40px;overflow-y:auto}.toc-panel[data-astro-cid-bvzihdzo].active{right:0}.toc-close[data-astro-cid-bvzihdzo]{position:absolute;left:10px;top:85px;background:transparent;border:none;cursor:pointer;color:#ffffffe6;font-size:1.8rem;transition:transform .4s cubic-bezier(.25,.1,.25,1),color .4s cubic-bezier(.25,.1,.25,1);opacity:.8;padding:8px;display:flex;align-items:center;justify-content:center}.toc-close[data-astro-cid-bvzihdzo] .toc-close-arrow[data-astro-cid-bvzihdzo]{display:inline-block;transform:rotate(180deg);transition:transform .4s cubic-bezier(.25,.1,.25,1)}.toc-close[data-astro-cid-bvzihdzo]:hover{transform:translate(-3px);opacity:1}.toc-close[data-astro-cid-bvzihdzo]:hover .toc-close-arrow[data-astro-cid-bvzihdzo]{transform:translate(2px) rotate(180deg)}.toc-panel-title[data-astro-cid-bvzihdzo]{font-family:Georgia Custom,Georgia,serif;font-size:1.2rem;color:#fffffff2;margin-bottom:30px;font-weight:400;text-align:center}.toc-content[data-astro-cid-bvzihdzo]{font-family:Georgia Custom,Georgia,serif;padding-left:10px;text-align:center}.toc-list-container[data-astro-cid-bvzihdzo]{display:inline-block;text-align:left;width:85%}.toc-item[data-astro-cid-bvzihdzo]{margin-bottom:20px}.toc-item[data-astro-cid-bvzihdzo].toc-h3{padding-left:20px;font-size:.9rem;margin-bottom:16px;margin-top:4px}.toc-item[data-astro-cid-bvzihdzo].toc-h4{padding-left:38px;font-size:.85rem;margin-bottom:14px;margin-top:4px}.toc-content[data-astro-cid-bvzihdzo] a[data-astro-cid-bvzihdzo]{color:#ffffffe6;text-decoration:none;display:block;line-height:1.4;padding:3px 6px;transition:all .3s cubic-bezier(.25,.1,.25,1)}.toc-content[data-astro-cid-bvzihdzo] a[data-astro-cid-bvzihdzo]:hover{color:#fff;transform:translate(5px)}.toc-content[data-astro-cid-bvzihdzo] a[data-astro-cid-bvzihdzo].active{color:#fff;transform:translate(8px);position:relative;font-size:1.05em;font-weight:500}.toc-content[data-astro-cid-bvzihdzo] a[data-astro-cid-bvzihdzo].active:before{content:"";position:absolute;left:-10px;top:50%;transform:translateY(-50%);width:3px;height:70%;background-color:#ffffffb3;border-radius:1px}.toc-empty[data-astro-cid-bvzihdzo]{color:#ffffffb3;font-style:italic;font-size:.9rem;text-align:center;padding:20px 0}body[data-page=blog-post],body[data-page=work-post]{overflow-x:hidden}.blog-post[data-astro-cid-bvzihdzo]{max-width:750px;margin:40px auto 100px;padding:0 30px;position:relative;width:100%;transition:opacity .6s cubic-bezier(.25,.1,.25,1);line-height:1.7}body.toc-active .blog-post[data-astro-cid-bvzihdzo]{opacity:.8}.post-header[data-astro-cid-bvzihdzo]{margin-bottom:40px}.post-title[data-astro-cid-bvzihdzo]{font-size:2rem;font-weight:400;margin-bottom:10px;font-family:Georgia Custom,Georgia,serif;color:#f0f0f0f2;line-height:1.2;letter-spacing:-.02em}.post-date[data-astro-cid-bvzihdzo]{font-size:.95rem;color:#c8c8c8bf;font-family:Georgia Custom,Georgia,serif}.post-content[data-astro-cid-bvzihdzo]{font-family:Georgia Custom,Georgia,serif;color:#f0f0f0f2;line-height:1.8;letter-spacing:.01em;font-size:1.05rem}.post-content[data-astro-cid-bvzihdzo] p[data-astro-cid-bvzihdzo]{margin-bottom:1.6em}.post-content[data-astro-cid-bvzihdzo] h2{margin-top:2em;margin-bottom:.8em;font-size:1.6rem;color:#f0f0f0fa}.post-content[data-astro-cid-bvzihdzo] h3{margin-top:1.8em;margin-bottom:.7em;font-size:1.3rem}.post-content[data-astro-cid-bvzihdzo] ul,.post-content[data-astro-cid-bvzihdzo] ol{margin-bottom:1.6em;padding-left:1.5em}.post-content[data-astro-cid-bvzihdzo] li{margin-bottom:.5em}.post-content[data-astro-cid-bvzihdzo] a{color:#c8c8ffe6;text-decoration:none;border-bottom:1px solid rgba(200,200,255,.3);transition:border-color .3s ease,color .3s ease}.post-content[data-astro-cid-bvzihdzo] a:hover{color:#d2d2ff;border-bottom-color:#d2d2ffb3}.post-content[data-astro-cid-bvzihdzo] blockquote{border-left:3px solid rgba(200,200,200,.3);padding-left:1.2em;margin-left:0;margin-right:0;margin-bottom:1.6em;font-style:italic;color:#dcdcdcd9}.post-content[data-astro-cid-bvzihdzo] h2,.post-content[data-astro-cid-bvzihdzo] h3,.post-content[data-astro-cid-bvzihdzo] h4,.post-content[data-astro-cid-bvzihdzo] h5,.post-content[data-astro-cid-bvzihdzo] h6{font-family:Georgia Custom,Georgia,serif;font-weight:400;margin:2em 0 .7em;color:#f0f0f0f2;line-height:1.3}.post-content[data-astro-cid-bvzihdzo] h2{font-size:1.85rem;letter-spacing:-.01em}.post-content[data-astro-cid-bvzihdzo] h3{font-size:1.6rem}.post-content[data-astro-cid-bvzihdzo] p{margin-bottom:1.4em;font-size:1.05rem}.post-content[data-astro-cid-bvzihdzo] a{color:#dcdcdcf2;text-decoration:underline;text-decoration-color:#c8c8c866;text-underline-offset:2px;transition:text-decoration-color .2s ease}.post-content[data-astro-cid-bvzihdzo] a:hover{text-decoration-color:#dcdcdcf2}.post-content[data-astro-cid-bvzihdzo] ul,.post-content[data-astro-cid-bvzihdzo] ol{margin-left:2em;margin-bottom:1.4em}.post-content[data-astro-cid-bvzihdzo] li{margin-bottom:.6em}.post-content[data-astro-cid-bvzihdzo] pre{background-color:#0a0a0a99;padding:1.2em;border-radius:3px;overflow-x:auto;margin:1.8em 0;border:1px solid rgba(255,255,255,.05)}.post-content[data-astro-cid-bvzihdzo] code{font-family:monospace;font-size:.95em;color:#dcdcdcf2}.post-content[data-astro-cid-bvzihdzo] blockquote{border-left:3px solid rgba(200,200,200,.3);padding-left:1.2em;font-style:italic;color:#c8c8c8cc;margin:1.8em 0}.post-content[data-astro-cid-bvzihdzo] .highlight{animation:highlight-pulse 1.5s ease;position:relative}.post-content[data-astro-cid-bvzihdzo] .highlight:after{content:"";position:absolute;left:-10px;top:50%;transform:translateY(-50%);width:5px;height:80%;background-color:#fff3;border-radius:2px;animation:highlight-bar 1.5s ease}@keyframes highlight-pulse{0%{transform:scale(1)}20%{transform:scale(1.03)}to{transform:scale(1)}}@keyframes highlight-bar{0%{opacity:0}20%{opacity:1}to{opacity:0}}.post-footer[data-astro-cid-bvzihdzo]{margin-top:70px;padding-top:25px;border-top:1px solid rgba(200,200,200,.15);display:flex;flex-direction:column;gap:25px}.post-tags[data-astro-cid-bvzihdzo]{display:flex;flex-wrap:wrap;gap:12px}.post-actions[data-astro-cid-bvzihdzo]{display:flex;justify-content:space-between;margin-top:35px}.return-link[data-astro-cid-bvzihdzo],.subscribe-link[data-astro-cid-bvzihdzo]{font-family:Georgia Custom,Georgia,serif;font-size:.95rem;color:#f0f0f0e6;text-decoration:none;background-color:#2229;backdrop-filter:blur(4px);-webkit-backdrop-filter:blur(4px);padding:.4rem .9rem;border-radius:1rem;transition:all .3s ease;border:1px solid rgba(255,255,255,.1)}.return-link[data-astro-cid-bvzihdzo]:hover,.subscribe-link[data-astro-cid-bvzihdzo]:hover{background-color:#222222f2;transform:translateY(-1px);box-shadow:0 2px 4px #0003}@media (max-width: 1024px){.blog-post[data-astro-cid-bvzihdzo]{max-width:90%}}@media (max-width: 768px){.blog-title[data-astro-cid-bvzihdzo]{font-size:1.2rem}.blog-header[data-astro-cid-bvzihdzo]{margin:45px 0 15px}.toc-toggle[data-astro-cid-bvzihdzo]{top:25px;right:25px;width:18px;height:18px}.toc-panel[data-astro-cid-bvzihdzo]{width:85%;right:-100%}.toc-close[data-astro-cid-bvzihdzo]{top:75px;left:12px}.blog-post[data-astro-cid-bvzihdzo]{margin-top:20px;padding:0 15px}.post-title[data-astro-cid-bvzihdzo]{font-size:1.8rem}body.toc-active .blog-post[data-astro-cid-bvzihdzo]{opacity:.4}}
</file>

<file path=".netlify/build/_astro/_tag_.Rb4hvsWq.css">
html{scrollbar-width:thin;scrollbar-color:rgba(100,100,100,.4) transparent}.blog-header[data-astro-cid-trjsnkp3]{width:100%;display:flex;justify-content:center;margin:55px 0 30px}.blog-title[data-astro-cid-trjsnkp3]{font-family:Georgia Custom,Georgia,serif;font-size:1.5rem;color:#f0f0f0e6;letter-spacing:-.01em;position:relative}.blog-title[data-astro-cid-trjsnkp3]:after{content:"";position:absolute;bottom:-8px;left:50%;transform:translate(-50%);width:40px;height:1px;background-color:#f0f0f066}.page-container[data-astro-cid-trjsnkp3]{display:flex;width:100%;max-width:1200px;margin:0 auto;padding:0 30px;gap:40px}.blog-sidebar[data-astro-cid-trjsnkp3]{width:220px;padding-top:30px;padding-right:20px;position:sticky;top:0;height:100vh;align-self:flex-start}.sidebar-section[data-astro-cid-trjsnkp3]{margin-bottom:40px}.back-to-blog[data-astro-cid-trjsnkp3]{font-family:Georgia Custom,Georgia,serif;font-size:.95rem;color:#c8c8c8cc;text-decoration:none;transition:color .3s ease,transform .3s ease;display:inline-block}.back-to-blog[data-astro-cid-trjsnkp3]:hover{color:#f0f0f0;transform:translate(-3px)}.tags-title[data-astro-cid-trjsnkp3]{font-size:.85rem;color:#b4b4b4b3;margin-bottom:10px;font-family:Georgia Custom,Georgia,serif}.tags-list[data-astro-cid-trjsnkp3]{display:flex;flex-direction:column;gap:8px}.tag-link[data-astro-cid-trjsnkp3]{position:relative;padding-left:15px;font-size:.85rem;color:#c8c8c8cc;text-decoration:none;font-family:Georgia Custom,Georgia,serif;transition:color .3s ease,transform .3s ease;display:inline-block}.tag-link[data-astro-cid-trjsnkp3]:before{content:"#";position:absolute;left:0;top:0;color:#b4b4b499}.tag-link[data-astro-cid-trjsnkp3]:hover{color:#f0f0f0;transform:translate(2px)}.blog-content[data-astro-cid-trjsnkp3]{flex:1;padding-top:10px;padding-bottom:60px;max-width:900px}.tag-header[data-astro-cid-trjsnkp3]{margin-bottom:30px}.tag-heading[data-astro-cid-trjsnkp3]{font-family:Georgia Custom,Georgia,serif;font-size:1.6rem;color:#f0f0f0f2;font-weight:400;margin-bottom:5px}.post-count[data-astro-cid-trjsnkp3]{font-family:Georgia Custom,Georgia,serif;font-size:.9rem;color:#b4b4b4b3}.tag-posts[data-astro-cid-trjsnkp3]{display:flex;flex-direction:column;gap:40px}.post-item[data-astro-cid-trjsnkp3]{padding-bottom:30px;position:relative}.post-item[data-astro-cid-trjsnkp3]:not(:last-child):after{content:"";position:absolute;bottom:0;left:10%;width:80%;height:1px;background:linear-gradient(to right,transparent,rgba(200,200,200,.15),transparent)}.post-item-title[data-astro-cid-trjsnkp3]{font-size:1.4rem;margin-bottom:8px;font-family:Georgia Custom,Georgia,serif;font-weight:400;line-height:1.3}.post-item-title[data-astro-cid-trjsnkp3] a[data-astro-cid-trjsnkp3]{color:#e6e6e6e6;text-decoration:none;transition:color .3s ease}.post-item-title[data-astro-cid-trjsnkp3] a[data-astro-cid-trjsnkp3]:hover{color:#fff;text-decoration:underline;text-underline-offset:3px;text-decoration-color:#c8c8c866}.post-item-date[data-astro-cid-trjsnkp3]{font-family:Georgia Custom,Georgia,serif;font-size:.85rem;color:#b4b4b4bf;margin:5px 0 10px}.post-item-description[data-astro-cid-trjsnkp3]{font-family:Georgia Custom,Georgia,serif;font-size:.95rem;color:#c8c8c8d9;line-height:1.5;margin-top:8px;margin-bottom:12px}.post-item-read-more[data-astro-cid-trjsnkp3]{font-family:Georgia Custom,Georgia,serif;font-size:.9rem;color:#c8c8c8cc;text-decoration:none;transition:color .3s ease,transform .3s ease;display:inline-block}.post-item-read-more[data-astro-cid-trjsnkp3]:hover{color:#fffffff2;transform:translate(3px)}@media (max-width: 1024px){.blog-content[data-astro-cid-trjsnkp3]{max-width:100%}}@media (max-width: 768px){.page-container[data-astro-cid-trjsnkp3]{flex-direction:column;padding:0 20px}.blog-sidebar[data-astro-cid-trjsnkp3]{width:100%;padding-right:0;position:relative;height:auto;margin-bottom:40px}.sidebar-section[data-astro-cid-trjsnkp3]{margin-bottom:30px}.post-item-title[data-astro-cid-trjsnkp3]{font-size:1.3rem}}@media (max-width: 480px){.blog-header[data-astro-cid-trjsnkp3]{margin:50px 0 20px}.blog-title[data-astro-cid-trjsnkp3]{font-size:1.3rem}.page-container[data-astro-cid-trjsnkp3]{padding:0 15px}.tag-heading[data-astro-cid-trjsnkp3]{font-size:1.4rem}.post-item-title[data-astro-cid-trjsnkp3]{font-size:1.2rem}.post-item-description[data-astro-cid-trjsnkp3]{font-size:.9rem}}
</file>

<file path=".netlify/build/_astro/blog.DWMZHUHN.css">
html{scrollbar-width:thin;scrollbar-color:rgba(100,100,100,.4) transparent}::-webkit-scrollbar{width:8px;height:8px}::-webkit-scrollbar-track{background:transparent}::-webkit-scrollbar-thumb{background-color:#64646466;border-radius:4px}::-webkit-scrollbar-thumb:hover{background-color:#78787899}body[data-page=blog]{overflow-y:auto}.blog-header[data-astro-cid-ijnerlr2]{width:100%;display:flex;justify-content:center;margin:55px 0 30px}.blog-title[data-astro-cid-ijnerlr2]{font-family:Georgia Custom,Georgia,serif;font-size:1.5rem;color:#f0f0f0e6;letter-spacing:-.01em;position:relative}.blog-title[data-astro-cid-ijnerlr2]:after{content:"";position:absolute;bottom:-8px;left:50%;transform:translate(-50%);width:40px;height:1px;background-color:#f0f0f066}.page-container[data-astro-cid-ijnerlr2]{display:flex;width:100%;max-width:1200px;margin:0 auto;padding:0 30px;gap:40px}.blog-sidebar[data-astro-cid-ijnerlr2]{width:220px;padding-top:30px;padding-right:20px;position:sticky;top:0;height:100vh;align-self:flex-start}.sidebar-section[data-astro-cid-ijnerlr2]{margin-bottom:40px}.search-link[data-astro-cid-ijnerlr2]{display:flex;align-items:center;color:#f0f0f0e6;text-decoration:none;font-family:Georgia Custom,Georgia,serif;font-size:.9rem;transition:all .3s ease;padding:.4rem .9rem;background-color:#2229;backdrop-filter:blur(4px);-webkit-backdrop-filter:blur(4px);border:1px solid rgba(255,255,255,.1);border-radius:1rem}.search-link[data-astro-cid-ijnerlr2]:hover{color:#f0f0f0;background-color:#222222f2;transform:translateY(-1px);box-shadow:0 2px 4px #0003}.search-link[data-astro-cid-ijnerlr2] svg[data-astro-cid-ijnerlr2]{margin-right:10px}.archive-container[data-astro-cid-ijnerlr2]{display:flex;align-items:center}.tags-title[data-astro-cid-ijnerlr2]{font-size:.85rem;color:#b4b4b4b3;margin-bottom:10px;font-family:Georgia Custom,Georgia,serif;display:flex;align-items:center;gap:6px}.tags-title[data-astro-cid-ijnerlr2] svg[data-astro-cid-ijnerlr2]{opacity:.7}.archive-link[data-astro-cid-ijnerlr2]{font-size:.9rem;color:#f0f0f0e6;text-decoration:none;font-family:Georgia Custom,Georgia,serif;transition:all .3s ease;display:flex;align-items:center;gap:8px;padding:.4rem .9rem;background-color:#2229;backdrop-filter:blur(4px);-webkit-backdrop-filter:blur(4px);border:1px solid rgba(255,255,255,.1);border-radius:1rem}.archive-link[data-astro-cid-ijnerlr2]:hover{color:#f0f0f0;background-color:#222222f2;transform:translateY(-1px);box-shadow:0 2px 4px #0003}.tag-link[data-astro-cid-ijnerlr2]{font-size:.85rem;color:#c8c8c8cc;text-decoration:none;font-family:Georgia Custom,Georgia,serif;transition:color .3s ease,transform .3s ease;display:flex;align-items:center;gap:6px}.tags-container[data-astro-cid-ijnerlr2]{margin-top:5px}.tags-toggle[data-astro-cid-ijnerlr2]{display:flex;align-items:center;justify-content:space-between;width:100%;background:none;border:none;padding:0;cursor:pointer;text-align:left;color:#c8c8c8cc;margin-bottom:10px}.tags-toggle[data-astro-cid-ijnerlr2]:hover{color:#f0f0f0}.toggle-icon[data-astro-cid-ijnerlr2]{font-size:1rem;transition:transform .3s ease;display:inline-block;width:16px;height:16px;text-align:center;line-height:16px}.tags-toggle[data-astro-cid-ijnerlr2][aria-expanded=true] .toggle-icon[data-astro-cid-ijnerlr2]{transform:rotate(45deg)}.tags-list[data-astro-cid-ijnerlr2]{display:flex;flex-direction:column;gap:8px;margin-top:10px;max-height:0;overflow:hidden;transition:max-height .3s ease,margin-top .3s ease}.tags-list[data-astro-cid-ijnerlr2]:not([hidden]){max-height:300px;margin-top:10px}.tag-link[data-astro-cid-ijnerlr2]{position:relative;padding-left:15px}.tag-link[data-astro-cid-ijnerlr2]:before{content:"";position:absolute;left:0;top:50%;transform:translateY(-50%);width:4px;height:4px;background-color:#b4b4b466;border-radius:50%}.all-tags-link[data-astro-cid-ijnerlr2]{font-weight:500;color:#dcdcdce6;margin-bottom:5px;border-bottom:1px solid rgba(180,180,180,.2);padding-bottom:5px}.all-tags-link[data-astro-cid-ijnerlr2]:before{background-color:#dcdcdc99;width:5px;height:5px}.tag-link[data-astro-cid-ijnerlr2]:hover{color:#f0f0f0;transform:translate(2px)}.subscribe-link[data-astro-cid-ijnerlr2]{font-family:Georgia Custom,Georgia,serif;font-size:.9rem;color:#f0f0f0e6;text-decoration:none;background-color:#2229;backdrop-filter:blur(4px);-webkit-backdrop-filter:blur(4px);padding:.4rem .9rem;border:1px solid rgba(255,255,255,.1);border-radius:1rem;transition:all .3s ease;display:inline-block}.subscribe-link[data-astro-cid-ijnerlr2]:hover{color:#f0f0f0;background-color:#222222f2;transform:translateY(-1px);box-shadow:0 2px 4px #0003}.subscribe-link[data-astro-cid-ijnerlr2]:active{transform:translateY(0)}.blog-content[data-astro-cid-ijnerlr2]{flex:1;padding:10px 20px 60px 40px;max-width:900px}.recent-post[data-astro-cid-ijnerlr2]{margin-bottom:50px;position:relative}.posts-divider[data-astro-cid-ijnerlr2]{width:100%;height:1px;background:linear-gradient(90deg,rgba(200,200,200,.15),rgba(200,200,200,.05),transparent);margin:40px 0}.recent-post[data-astro-cid-ijnerlr2] .post-title[data-astro-cid-ijnerlr2]{font-size:2rem;font-weight:400;margin-bottom:10px;font-family:Georgia Custom,Georgia,serif;line-height:1.2;letter-spacing:-.02em;position:relative}.recent-post[data-astro-cid-ijnerlr2] .post-title[data-astro-cid-ijnerlr2] a[data-astro-cid-ijnerlr2]{color:#f0f0f0f2;text-decoration:none;transition:color .3s ease}.recent-post[data-astro-cid-ijnerlr2] .post-title[data-astro-cid-ijnerlr2] a[data-astro-cid-ijnerlr2]:hover{color:#fff;text-decoration:underline;text-underline-offset:3px;text-decoration-color:#c8c8c866}.recent-post[data-astro-cid-ijnerlr2] .post-date[data-astro-cid-ijnerlr2]{font-size:.95rem;color:#c8c8c8bf;margin-bottom:25px;font-family:Georgia Custom,Georgia,serif}.post-preview[data-astro-cid-ijnerlr2]{font-family:Georgia Custom,Georgia,serif;color:#e6e6e6e6;line-height:1.6}.post-description[data-astro-cid-ijnerlr2]{font-size:1.05rem;margin-bottom:1.4em;font-style:italic;color:#dcdcdcf2}.post-preview[data-astro-cid-ijnerlr2] p[data-astro-cid-ijnerlr2]{margin-bottom:1.4em}.read-more[data-astro-cid-ijnerlr2]{display:inline-block;font-family:Georgia Custom,Georgia,serif;font-size:.95rem;color:#f0f0f0e6;text-decoration:none;margin-top:15px;transition:all .3s ease;padding:.4rem .9rem;background-color:#2229;backdrop-filter:blur(4px);-webkit-backdrop-filter:blur(4px);border:1px solid rgba(255,255,255,.1);border-radius:1rem}.read-more[data-astro-cid-ijnerlr2]:hover{color:#f0f0f0;background-color:#222222f2;transform:translateY(-1px);box-shadow:0 2px 4px #0003}.read-more-arrow[data-astro-cid-ijnerlr2]{display:inline-block;margin-left:2px;transition:transform .3s ease}.read-more[data-astro-cid-ijnerlr2]:hover .read-more-arrow[data-astro-cid-ijnerlr2]{transform:translate(3px)}.more-posts[data-astro-cid-ijnerlr2]{margin-top:20px}.post-item[data-astro-cid-ijnerlr2]{margin-bottom:30px;padding-bottom:30px;border-bottom:1px solid rgba(200,200,200,.1)}.post-item[data-astro-cid-ijnerlr2]:last-child{border-bottom:none}.post-item-title[data-astro-cid-ijnerlr2]{font-size:1.4rem;font-weight:400;font-family:Georgia Custom,Georgia,serif;margin-bottom:8px;line-height:1.3}.post-item-title[data-astro-cid-ijnerlr2] a[data-astro-cid-ijnerlr2]{color:#e6e6e6e6;text-decoration:none;transition:color .3s ease}.post-item-title[data-astro-cid-ijnerlr2] a[data-astro-cid-ijnerlr2]:hover{color:#fff;text-decoration:underline;text-underline-offset:3px;text-decoration-color:#c8c8c866}.post-item-date[data-astro-cid-ijnerlr2]{font-family:Georgia Custom,Georgia,serif;font-size:.85rem;color:#b4b4b4bf;margin:5px 0 10px}.post-item-description[data-astro-cid-ijnerlr2]{font-family:Georgia Custom,Georgia,serif;font-size:.95rem;color:#c8c8c8d9;line-height:1.5;margin-top:8px;margin-bottom:12px}.post-item-read-more[data-astro-cid-ijnerlr2]{font-family:Georgia Custom,Georgia,serif;font-size:.9rem;color:#f0f0f0e6;text-decoration:none;transition:all .3s ease;display:inline-block;padding:.4rem .9rem;background-color:#2229;backdrop-filter:blur(4px);-webkit-backdrop-filter:blur(4px);border:1px solid rgba(255,255,255,.1);border-radius:1rem;margin-top:5px}.post-item-read-more[data-astro-cid-ijnerlr2]:hover{color:#f0f0f0;background-color:#222222f2;transform:translateY(-1px);box-shadow:0 2px 4px #0003}@media (max-width: 1100px){.page-container[data-astro-cid-ijnerlr2]{flex-direction:column}.blog-sidebar[data-astro-cid-ijnerlr2]{width:100%;padding-top:30px;position:relative;height:auto;display:flex;flex-wrap:wrap;justify-content:flex-start;align-items:center;gap:20px}.sidebar-section[data-astro-cid-ijnerlr2]{margin:0 30px 20px 0}.blog-content[data-astro-cid-ijnerlr2]{padding-top:20px;width:100%;padding-left:0}}@media (max-width: 768px){.blog-title[data-astro-cid-ijnerlr2]{font-size:1.2rem}.blog-header[data-astro-cid-ijnerlr2]{margin:45px 0 15px}.page-container[data-astro-cid-ijnerlr2]{padding:0 15px}.blog-sidebar[data-astro-cid-ijnerlr2]{padding:20px 0;flex-direction:row;justify-content:center;align-items:center;flex-wrap:wrap;gap:15px}.sidebar-section[data-astro-cid-ijnerlr2]{margin:0;padding:8px 12px;background-color:#1e1e1e80;border-radius:1rem;display:flex;align-items:center;justify-content:center}.search-container[data-astro-cid-ijnerlr2],.archive-container[data-astro-cid-ijnerlr2],.subscribe-container[data-astro-cid-ijnerlr2],.tags-container[data-astro-cid-ijnerlr2]{width:auto}.tags-title[data-astro-cid-ijnerlr2],.tags-toggle[data-astro-cid-ijnerlr2]{margin-bottom:0}.tags-list[data-astro-cid-ijnerlr2]:not([hidden]){position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);background-color:#282828f2;padding:20px;border-radius:1rem;box-shadow:0 5px 20px #0000004d;z-index:100;max-height:80vh;overflow-y:auto;width:80%;max-width:300px}.recent-post[data-astro-cid-ijnerlr2] .post-title[data-astro-cid-ijnerlr2]{font-size:1.7rem}.post-item-title[data-astro-cid-ijnerlr2]{font-size:1.3rem}.read-more[data-astro-cid-ijnerlr2],.post-item-read-more[data-astro-cid-ijnerlr2]{display:block;text-align:center;width:100%;padding:8px 0}}
</file>

<file path=".netlify/build/_astro/work.DbR1XWHN.css">
.project-card[data-astro-cid-mspuyifq]{position:relative;transition:transform .5s var(--easing-standard)}.project-card[data-astro-cid-mspuyifq].featured{margin-bottom:3.5rem;padding-bottom:2.5rem;border-bottom:1px solid rgba(255,255,255,.06)}.project-card[data-astro-cid-mspuyifq].standard{position:relative;border-radius:2px;transition:transform .6s var(--easing-standard),opacity .6s var(--easing-standard)}.project-link[data-astro-cid-mspuyifq]{display:block;text-decoration:none;color:inherit;transition:color .4s var(--easing-standard);height:100%}.project-card[data-astro-cid-mspuyifq].standard .project-link[data-astro-cid-mspuyifq]{display:flex;flex-direction:column;height:100%}.project-card[data-astro-cid-mspuyifq]:hover{transform:translateY(-3px)}.project-card[data-astro-cid-mspuyifq].featured:after{content:"";position:absolute;bottom:2.5rem;left:0;width:0;height:1px;background-color:#fff3;transition:width .7s var(--easing-standard)}.project-card[data-astro-cid-mspuyifq].featured:hover:after{width:100%}.project-card[data-astro-cid-mspuyifq].standard:after{content:"";position:absolute;inset:0;background:radial-gradient(circle at 50% 50%,rgba(255,255,255,.02),transparent 70%);opacity:0;transition:opacity .8s var(--easing-standard);pointer-events:none;z-index:-1}.project-card[data-astro-cid-mspuyifq].standard:hover:after{opacity:1}.project-title[data-astro-cid-mspuyifq]{font-size:1.6rem;font-weight:400;color:#fffffff2;margin-bottom:.9rem;line-height:1.3;font-family:Georgia Custom,Georgia,serif;letter-spacing:-.01em}.project-card[data-astro-cid-mspuyifq].standard .project-title[data-astro-cid-mspuyifq]{font-size:1.25rem;margin-bottom:.7rem;letter-spacing:0}.project-description[data-astro-cid-mspuyifq]{font-size:.95rem;line-height:1.6;color:#ffffffbf;margin-bottom:1.5rem;font-family:Georgia Custom,Georgia,serif}.project-card[data-astro-cid-mspuyifq].standard .project-description[data-astro-cid-mspuyifq]{font-size:.85rem;margin-bottom:1.2rem;color:#ffffffa6;flex-grow:1;line-height:1.5}.project-meta[data-astro-cid-mspuyifq]{display:flex;align-items:center;gap:1rem;margin-bottom:1rem}.project-card[data-astro-cid-mspuyifq].standard .project-content[data-astro-cid-mspuyifq]{display:flex;flex-direction:column;height:100%}.project-card[data-astro-cid-mspuyifq].standard .project-footer[data-astro-cid-mspuyifq]{display:flex;justify-content:space-between;align-items:center;margin-top:auto;flex-wrap:wrap;gap:.7rem}time[data-astro-cid-mspuyifq]{font-size:.75rem;color:#ffffff73;font-family:Georgia Custom,Georgia,serif;line-height:1}.project-badges[data-astro-cid-mspuyifq]{display:flex;gap:.7rem;align-items:center}.project-status[data-astro-cid-mspuyifq]{font-size:.65rem;color:#ffd700d9;border:1px solid rgba(255,215,0,.2);padding:0 .4rem;border-radius:2px;text-transform:uppercase;letter-spacing:.05em;line-height:1.5}.project-status[data-astro-cid-mspuyifq].small{font-size:.6rem;padding:0 .3rem}.featured-badge[data-astro-cid-mspuyifq]{font-size:.65rem;color:#ffffffd9;border:1px solid rgba(255,255,255,.15);padding:0 .4rem;border-radius:2px;text-transform:uppercase;letter-spacing:.05em;line-height:1.5}.project-tags[data-astro-cid-mspuyifq]{display:flex;flex-wrap:wrap;gap:.5rem;margin-bottom:1.5rem}.project-tags[data-astro-cid-mspuyifq].small{margin-bottom:0;gap:.4rem}.tag[data-astro-cid-mspuyifq]{font-size:.75rem;color:#fff9;border-bottom:1px solid rgba(255,255,255,.15);padding:0 0 .15rem;letter-spacing:.01em;transition:all .4s var(--easing-standard)}.tag[data-astro-cid-mspuyifq].small{font-size:.65rem;color:#ffffff80;border-bottom:none}.project-card[data-astro-cid-mspuyifq]:hover .tag[data-astro-cid-mspuyifq]{color:#ffffffd9;border-bottom-color:#ffffff40}.project-card[data-astro-cid-mspuyifq]:hover .tag[data-astro-cid-mspuyifq].small{color:#ffffffb3}.more-tags[data-astro-cid-mspuyifq]{font-size:.65rem;color:#fff6;margin-left:.2rem}.project-actions[data-astro-cid-mspuyifq]{display:flex;justify-content:space-between;align-items:center}.external-links[data-astro-cid-mspuyifq]{display:flex;gap:1rem}.view-details[data-astro-cid-mspuyifq]{font-size:.85rem;color:#ffffff80;display:flex;align-items:center;gap:.25rem;transition:all .4s var(--easing-standard)}.project-card[data-astro-cid-mspuyifq]:hover .view-details[data-astro-cid-mspuyifq]{color:#ffffffd9}.view-details[data-astro-cid-mspuyifq] .arrow[data-astro-cid-mspuyifq]{font-size:.85rem;transition:transform .4s var(--easing-standard)}.project-card[data-astro-cid-mspuyifq]:hover .view-details[data-astro-cid-mspuyifq] .arrow[data-astro-cid-mspuyifq]{transform:translate(.2rem)}.external-link[data-astro-cid-mspuyifq]{font-size:.75rem;color:#ffffff73;text-decoration:none;display:flex;align-items:center;gap:.2rem;transition:all .3s var(--easing-standard)}.external-link[data-astro-cid-mspuyifq]:hover{color:#ffffffe6}.ext-arrow[data-astro-cid-mspuyifq]{font-size:.7rem;transition:transform .3s var(--easing-standard)}.external-link[data-astro-cid-mspuyifq]:hover .ext-arrow[data-astro-cid-mspuyifq]{transform:translate(.125rem) translateY(-.125rem)}@media (max-width: 48rem){.project-title[data-astro-cid-mspuyifq]{font-size:1.5rem}.project-card[data-astro-cid-mspuyifq].standard .project-title[data-astro-cid-mspuyifq]{font-size:1.15rem}.project-description[data-astro-cid-mspuyifq]{font-size:.9rem}.project-card[data-astro-cid-mspuyifq].featured{margin-bottom:3rem;padding-bottom:2rem}.project-card[data-astro-cid-mspuyifq].featured:after{bottom:2rem}.project-card[data-astro-cid-mspuyifq].standard .project-footer[data-astro-cid-mspuyifq]{flex-direction:column;align-items:flex-start;gap:.7rem}}@media (max-width: 30rem){.project-actions[data-astro-cid-mspuyifq]{flex-direction:column;align-items:flex-start;gap:.7rem}.external-links[data-astro-cid-mspuyifq]{width:100%;justify-content:flex-start}}.work-header[data-astro-cid-jljc7dey]{width:100%;text-align:center;padding:6rem 0 4rem;position:relative}.work-title[data-astro-cid-jljc7dey]{font-family:Georgia Custom,Georgia,serif;font-size:1.5rem;font-weight:400;color:#f0f0f0f2;position:relative;letter-spacing:-.01em;display:inline-block;margin:0}.work-title[data-astro-cid-jljc7dey]:after{content:"";position:absolute;bottom:-.6rem;left:50%;transform:translate(-50%);width:2.5rem;height:1px;background-color:#f0f0f04d;transition:width .6s var(--easing-standard)}.work-title[data-astro-cid-jljc7dey]:hover:after{width:3.5rem;background-color:#f0f0f066}.content-container[data-astro-cid-jljc7dey]{width:100%;max-width:54rem;margin:0 auto;padding:0 1.5rem 6rem;color:#f0f0f0}body[data-page=work]{overflow-y:auto;overflow-x:hidden;height:auto;min-height:100vh}.featured-projects-section[data-astro-cid-jljc7dey]{margin-bottom:4.5rem}.projects-grid[data-astro-cid-jljc7dey]{display:grid;grid-template-columns:repeat(auto-fill,minmax(21rem,1fr));gap:3rem 2.5rem;margin-bottom:5rem;margin-top:2.5rem}.section-header[data-astro-cid-jljc7dey]{display:flex;align-items:center;gap:1rem;margin:4rem 0 2rem}.section-line[data-astro-cid-jljc7dey]{flex-grow:1;height:1px;background-color:#ffffff14;transition:background-color .4s var(--easing-standard)}.section-header[data-astro-cid-jljc7dey]:hover .section-line[data-astro-cid-jljc7dey]{background-color:#ffffff1f}.section-title[data-astro-cid-jljc7dey]{font-size:1rem;font-weight:400;color:#ffffffb3;white-space:nowrap;letter-spacing:.01em;padding:0 .3rem;transition:color .4s var(--easing-standard);margin:0}.section-header[data-astro-cid-jljc7dey]:hover .section-title[data-astro-cid-jljc7dey]{color:#ffffffd9}.publications-list[data-astro-cid-jljc7dey]{list-style:none;padding:0;margin:2.5rem 0 0}.publication-item[data-astro-cid-jljc7dey]{margin-bottom:2.5rem;padding-bottom:2.5rem;border-bottom:1px solid rgba(255,255,255,.06);transition:transform .5s var(--easing-standard)}.publication-item[data-astro-cid-jljc7dey]:hover{transform:translateY(-3px)}.publication-item[data-astro-cid-jljc7dey]:last-child{border-bottom:none;margin-bottom:0;padding-bottom:0}.pub-title[data-astro-cid-jljc7dey]{font-size:1.15rem;line-height:1.4;margin:0 0 .8rem;font-weight:400;color:#ffffffd9;font-family:Georgia Custom,Georgia,serif}.pub-meta[data-astro-cid-jljc7dey]{display:flex;justify-content:space-between;align-items:center;flex-wrap:wrap;gap:.8rem}.pub-links[data-astro-cid-jljc7dey]{display:flex;gap:.8rem}.pub-details[data-astro-cid-jljc7dey]{font-size:.85rem;color:#ffffff80;margin:0}.pub-link[data-astro-cid-jljc7dey]{font-size:.75rem;color:#ffffff80;text-decoration:none;transition:all .3s var(--easing-standard);display:flex;align-items:center;gap:.2rem;border-bottom:1px solid rgba(255,255,255,.15);padding-bottom:1px}.pub-link[data-astro-cid-jljc7dey] .arrow[data-astro-cid-jljc7dey]{font-size:.7rem;transition:transform .3s var(--easing-standard)}.pub-link[data-astro-cid-jljc7dey]:hover{color:#ffffffe6;border-bottom-color:#ffffff4d}.pub-link[data-astro-cid-jljc7dey]:hover .arrow[data-astro-cid-jljc7dey]{transform:translate(.125rem) translateY(-.125rem)}@media (max-width: 64rem){.content-container[data-astro-cid-jljc7dey]{max-width:90%}.projects-grid[data-astro-cid-jljc7dey]{grid-template-columns:repeat(auto-fill,minmax(18rem,1fr));gap:2.5rem 2rem}}@media (max-width: 48rem){.work-header[data-astro-cid-jljc7dey]{padding:4.5rem 0 3rem}.work-title[data-astro-cid-jljc7dey]{font-size:1.3rem}.content-container[data-astro-cid-jljc7dey]{padding:0 1.25rem 4.5rem}.projects-grid[data-astro-cid-jljc7dey]{grid-template-columns:1fr;gap:2.5rem}.section-header[data-astro-cid-jljc7dey]{margin:3.5rem 0 1.5rem}.publication-item[data-astro-cid-jljc7dey]{margin-bottom:2rem;padding-bottom:2rem}}@media (max-width: 30rem){.work-header[data-astro-cid-jljc7dey]{padding:3.5rem 0 2.5rem}.content-container[data-astro-cid-jljc7dey]{padding:0 1rem 3.5rem}.pub-meta[data-astro-cid-jljc7dey]{flex-direction:column;align-items:flex-start;gap:.6rem}}
</file>

<file path=".netlify/build/_noop-middleware.mjs">
const onRequest = (_, next) => next();

export { onRequest };
</file>

<file path=".netlify/build/chunks/_@astrojs-ssr-adapter_CvSoi7hX.mjs">
import * as ssrFunction_js from '@astrojs/netlify/ssr-function.js';

function _mergeNamespaces(n, m) {
	for (var i = 0; i < m.length; i++) {
		const e = m[i];
		if (typeof e !== 'string' && !Array.isArray(e)) { for (const k in e) {
			if (k !== 'default' && !(k in n)) {
				const d = Object.getOwnPropertyDescriptor(e, k);
				if (d) {
					Object.defineProperty(n, k, d.get ? d : {
						enumerable: true,
						get: () => e[k]
					});
				}
			}
		} }
	}
	return Object.freeze(Object.defineProperty(n, Symbol.toStringTag, { value: 'Module' }));
}

const serverEntrypointModule = /*#__PURE__*/_mergeNamespaces({
	__proto__: null
}, [ssrFunction_js]);

export { serverEntrypointModule as s };
</file>

<file path=".netlify/build/chunks/_astro_asset-imports_D9aVaOQr.mjs">
const _astro_assetImports = new Map();

export { _astro_assetImports as default };
</file>

<file path=".netlify/build/chunks/_astro_content_CcaQj6Wl.mjs">
import { Traverse } from 'neotraverse/modern';
import pLimit from 'p-limit';
import { removeBase, isRemotePath, prependForwardSlash } from '@astrojs/internal-helpers/path';
import { V as VALID_INPUT_FORMATS, A as AstroError, U as UnknownContentCollectionError } from './astro/assets-service_DIMzS0Of.mjs';
import { a as createComponent, g as renderUniqueStylesheet, h as renderScriptElement, i as createHeadAndContent, d as renderComponent, r as renderTemplate, u as unescapeHTML } from './astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import * as devalue from 'devalue';

const CONTENT_IMAGE_FLAG = "astroContentImageFlag";
const IMAGE_IMPORT_PREFIX = "__ASTRO_IMAGE_";

function imageSrcToImportId(imageSrc, filePath) {
  imageSrc = removeBase(imageSrc, IMAGE_IMPORT_PREFIX);
  if (isRemotePath(imageSrc)) {
    return;
  }
  const ext = imageSrc.split(".").at(-1);
  if (!ext || !VALID_INPUT_FORMATS.includes(ext)) {
    return;
  }
  const params = new URLSearchParams(CONTENT_IMAGE_FLAG);
  if (filePath) {
    params.set("importer", filePath);
  }
  return `${imageSrc}?${params.toString()}`;
}

class DataStore {
  _collections = /* @__PURE__ */ new Map();
  constructor() {
    this._collections = /* @__PURE__ */ new Map();
  }
  get(collectionName, key) {
    return this._collections.get(collectionName)?.get(String(key));
  }
  entries(collectionName) {
    const collection = this._collections.get(collectionName) ?? /* @__PURE__ */ new Map();
    return [...collection.entries()];
  }
  values(collectionName) {
    const collection = this._collections.get(collectionName) ?? /* @__PURE__ */ new Map();
    return [...collection.values()];
  }
  keys(collectionName) {
    const collection = this._collections.get(collectionName) ?? /* @__PURE__ */ new Map();
    return [...collection.keys()];
  }
  has(collectionName, key) {
    const collection = this._collections.get(collectionName);
    if (collection) {
      return collection.has(String(key));
    }
    return false;
  }
  hasCollection(collectionName) {
    return this._collections.has(collectionName);
  }
  collections() {
    return this._collections;
  }
  /**
   * Attempts to load a DataStore from the virtual module.
   * This only works in Vite.
   */
  static async fromModule() {
    try {
      const data = await import('./_astro_data-layer-content_BcEe_9wP.mjs');
      if (data.default instanceof Map) {
        return DataStore.fromMap(data.default);
      }
      const map = devalue.unflatten(data.default);
      return DataStore.fromMap(map);
    } catch {
    }
    return new DataStore();
  }
  static async fromMap(data) {
    const store = new DataStore();
    store._collections = data;
    return store;
  }
}
function dataStoreSingleton() {
  let instance = void 0;
  return {
    get: async () => {
      if (!instance) {
        instance = DataStore.fromModule();
      }
      return instance;
    },
    set: (store) => {
      instance = store;
    }
  };
}
const globalDataStore = dataStoreSingleton();

const __vite_import_meta_env__ = {"ASSETS_PREFIX": undefined, "BASE_URL": "/", "DEV": false, "MODE": "production", "PROD": true, "SITE": "https://pvb.com", "SSR": true};
function createCollectionToGlobResultMap({
  globResult,
  contentDir
}) {
  const collectionToGlobResultMap = {};
  for (const key in globResult) {
    const keyRelativeToContentDir = key.replace(new RegExp(`^${contentDir}`), "");
    const segments = keyRelativeToContentDir.split("/");
    if (segments.length <= 1) continue;
    const collection = segments[0];
    collectionToGlobResultMap[collection] ??= {};
    collectionToGlobResultMap[collection][key] = globResult[key];
  }
  return collectionToGlobResultMap;
}
function createGetCollection({
  contentCollectionToEntryMap,
  dataCollectionToEntryMap,
  getRenderEntryImport,
  cacheEntriesByCollection
}) {
  return async function getCollection(collection, filter) {
    const hasFilter = typeof filter === "function";
    const store = await globalDataStore.get();
    let type;
    if (collection in contentCollectionToEntryMap) {
      type = "content";
    } else if (collection in dataCollectionToEntryMap) {
      type = "data";
    } else if (store.hasCollection(collection)) {
      const { default: imageAssetMap } = await import('./_astro_asset-imports_D9aVaOQr.mjs');
      const result = [];
      for (const rawEntry of store.values(collection)) {
        const data = updateImageReferencesInData(rawEntry.data, rawEntry.filePath, imageAssetMap);
        const entry = {
          ...rawEntry,
          data,
          collection
        };
        if (hasFilter && !filter(entry)) {
          continue;
        }
        result.push(entry);
      }
      return result;
    } else {
      console.warn(
        `The collection ${JSON.stringify(
          collection
        )} does not exist or is empty. Ensure a collection directory with this name exists.`
      );
      return [];
    }
    const lazyImports = Object.values(
      type === "content" ? contentCollectionToEntryMap[collection] : dataCollectionToEntryMap[collection]
    );
    let entries = [];
    if (!Object.assign(__vite_import_meta_env__, { Path: process.env.Path })?.DEV && cacheEntriesByCollection.has(collection)) {
      entries = cacheEntriesByCollection.get(collection);
    } else {
      const limit = pLimit(10);
      entries = await Promise.all(
        lazyImports.map(
          (lazyImport) => limit(async () => {
            const entry = await lazyImport();
            return type === "content" ? {
              id: entry.id,
              slug: entry.slug,
              body: entry.body,
              collection: entry.collection,
              data: entry.data,
              async render() {
                return render({
                  collection: entry.collection,
                  id: entry.id,
                  renderEntryImport: await getRenderEntryImport(collection, entry.slug)
                });
              }
            } : {
              id: entry.id,
              collection: entry.collection,
              data: entry.data
            };
          })
        )
      );
      cacheEntriesByCollection.set(collection, entries);
    }
    if (hasFilter) {
      return entries.filter(filter);
    } else {
      return entries.slice();
    }
  };
}
function updateImageReferencesInData(data, fileName, imageAssetMap) {
  return new Traverse(data).map(function(ctx, val) {
    if (typeof val === "string" && val.startsWith(IMAGE_IMPORT_PREFIX)) {
      const src = val.replace(IMAGE_IMPORT_PREFIX, "");
      const id = imageSrcToImportId(src, fileName);
      if (!id) {
        ctx.update(src);
        return;
      }
      const imported = imageAssetMap?.get(id);
      if (imported) {
        ctx.update(imported);
      } else {
        ctx.update(src);
      }
    }
  });
}
async function render({
  collection,
  id,
  renderEntryImport
}) {
  const UnexpectedRenderError = new AstroError({
    ...UnknownContentCollectionError,
    message: `Unexpected error while rendering ${String(collection)} → ${String(id)}.`
  });
  if (typeof renderEntryImport !== "function") throw UnexpectedRenderError;
  const baseMod = await renderEntryImport();
  if (baseMod == null || typeof baseMod !== "object") throw UnexpectedRenderError;
  const { default: defaultMod } = baseMod;
  if (isPropagatedAssetsModule(defaultMod)) {
    const { collectedStyles, collectedLinks, collectedScripts, getMod } = defaultMod;
    if (typeof getMod !== "function") throw UnexpectedRenderError;
    const propagationMod = await getMod();
    if (propagationMod == null || typeof propagationMod !== "object") throw UnexpectedRenderError;
    const Content = createComponent({
      factory(result, baseProps, slots) {
        let styles = "", links = "", scripts = "";
        if (Array.isArray(collectedStyles)) {
          styles = collectedStyles.map((style) => {
            return renderUniqueStylesheet(result, {
              type: "inline",
              content: style
            });
          }).join("");
        }
        if (Array.isArray(collectedLinks)) {
          links = collectedLinks.map((link) => {
            return renderUniqueStylesheet(result, {
              type: "external",
              src: prependForwardSlash(link)
            });
          }).join("");
        }
        if (Array.isArray(collectedScripts)) {
          scripts = collectedScripts.map((script) => renderScriptElement(script)).join("");
        }
        let props = baseProps;
        if (id.endsWith("mdx")) {
          props = {
            components: propagationMod.components ?? {},
            ...baseProps
          };
        }
        return createHeadAndContent(
          unescapeHTML(styles + links + scripts),
          renderTemplate`${renderComponent(
            result,
            "Content",
            propagationMod.Content,
            props,
            slots
          )}`
        );
      },
      propagation: "self"
    });
    return {
      Content,
      headings: propagationMod.getHeadings?.() ?? [],
      remarkPluginFrontmatter: propagationMod.frontmatter ?? {}
    };
  } else if (baseMod.Content && typeof baseMod.Content === "function") {
    return {
      Content: baseMod.Content,
      headings: baseMod.getHeadings?.() ?? [],
      remarkPluginFrontmatter: baseMod.frontmatter ?? {}
    };
  } else {
    throw UnexpectedRenderError;
  }
}
function isPropagatedAssetsModule(module) {
  return typeof module === "object" && module != null && "__astroPropagation" in module;
}

// astro-head-inject

const contentDir = '/src/content/';

const contentEntryGlob = /* #__PURE__ */ Object.assign({"/src/content/blog/age-of-synthesis.md": () => import('./age-of-synthesis_B-VSexue.mjs'),"/src/content/blog/hello-world.md": () => import('./hello-world_BwOf8HZz.mjs'),"/src/content/blog/mastery-and-knowledge.md": () => import('./mastery-and-knowledge_CkJKa8VC.mjs'),"/src/content/quotes/learning.md": () => import('./learning_DcKpeM5O.mjs'),"/src/content/quotes/mindfulness.md": () => import('./mindfulness_DTX3H095.mjs'),"/src/content/quotes/philosophy.md": () => import('./philosophy_fXVfYPzN.mjs'),"/src/content/work/project-five.md": () => import('./project-five_BCHmoapR.mjs'),"/src/content/work/project-four.md": () => import('./project-four_BiijU9ph.mjs'),"/src/content/work/project-one.md": () => import('./project-one_kU0mYB6U.mjs'),"/src/content/work/project-three.md": () => import('./project-three_C8CEKmI8.mjs'),"/src/content/work/project-two.md": () => import('./project-two_BbjtZZaS.mjs')});
const contentCollectionToEntryMap = createCollectionToGlobResultMap({
	globResult: contentEntryGlob,
	contentDir,
});

const dataEntryGlob = /* #__PURE__ */ Object.assign({});
const dataCollectionToEntryMap = createCollectionToGlobResultMap({
	globResult: dataEntryGlob,
	contentDir,
});
createCollectionToGlobResultMap({
	globResult: { ...contentEntryGlob, ...dataEntryGlob },
	contentDir,
});

let lookupMap = {};
lookupMap = {"blog":{"type":"content","entries":{"age-of-synthesis":"/src/content/blog/age-of-synthesis.md","hello-world":"/src/content/blog/hello-world.md","mastery-and-knowledge":"/src/content/blog/mastery-and-knowledge.md"}},"quotes":{"type":"content","entries":{"learning":"/src/content/quotes/learning.md","mindfulness":"/src/content/quotes/mindfulness.md","philosophy":"/src/content/quotes/philosophy.md"}},"work":{"type":"content","entries":{"project-five":"/src/content/work/project-five.md","project-four":"/src/content/work/project-four.md","project-one":"/src/content/work/project-one.md","project-three":"/src/content/work/project-three.md","project-two":"/src/content/work/project-two.md"}}};

new Set(Object.keys(lookupMap));

function createGlobLookup(glob) {
	return async (collection, lookupId) => {
		const filePath = lookupMap[collection]?.entries[lookupId];

		if (!filePath) return undefined;
		return glob[collection][filePath];
	};
}

const renderEntryGlob = /* #__PURE__ */ Object.assign({"/src/content/blog/age-of-synthesis.md": () => import('./age-of-synthesis_DqChI2cq.mjs'),"/src/content/blog/hello-world.md": () => import('./hello-world_BNhj20-t.mjs'),"/src/content/blog/mastery-and-knowledge.md": () => import('./mastery-and-knowledge_B3L9fdmh.mjs'),"/src/content/quotes/learning.md": () => import('./learning_SfTw29v4.mjs'),"/src/content/quotes/mindfulness.md": () => import('./mindfulness_BG5BPaMj.mjs'),"/src/content/quotes/philosophy.md": () => import('./philosophy_D9CDMOq1.mjs'),"/src/content/work/project-five.md": () => import('./project-five_B9I66poG.mjs'),"/src/content/work/project-four.md": () => import('./project-four_DlJCn5JQ.mjs'),"/src/content/work/project-one.md": () => import('./project-one_BLBOXsjf.mjs'),"/src/content/work/project-three.md": () => import('./project-three_CCeq-lTS.mjs'),"/src/content/work/project-two.md": () => import('./project-two_CMkRBYFL.mjs')});
const collectionToRenderEntryMap = createCollectionToGlobResultMap({
	globResult: renderEntryGlob,
	contentDir,
});

const cacheEntriesByCollection = new Map();
const getCollection = createGetCollection({
	contentCollectionToEntryMap,
	dataCollectionToEntryMap,
	getRenderEntryImport: createGlobLookup(collectionToRenderEntryMap),
	cacheEntriesByCollection,
});

export { getCollection as g };
</file>

<file path=".netlify/build/chunks/_astro_data-layer-content_BcEe_9wP.mjs">
const _astro_dataLayerContent = new Map();

export { _astro_dataLayerContent as default };
</file>

<file path=".netlify/build/chunks/age-of-synthesis_B-VSexue.mjs">
const id = "age-of-synthesis.md";
						const collection = "blog";
						const slug = "age-of-synthesis";
						const body = "\n## The Age of Synthesis: Beyond Knowledge Silos\n\nAI marks the end of traditional knowledge silos, shifting focus from retrieval to synthesis. Mastery is no longer about expertise in a singular field but about integrating patterns across domains. Reality isn't boxed into subjects but viewed through dynamic lenses, enabling a new age of strategy and innovation.\n\n## The Shift from Retrieval to Integration\n\nFor centuries, human knowledge has been organized into discrete categories - biology, physics, economics, psychology. We've built educational systems, career paths, and entire identities around these divisions. But as AI systems demonstrate the ability to retrieve and process information across all domains simultaneously, the value is shifting from knowing facts to connecting them in meaningful ways.\n\n## Cross-Domain Pattern Recognition\n\nThe most innovative solutions often come from applying principles from one field to problems in another. The person who can recognize that a biological system's resilience might inform financial market stability, or that linguistic structures might offer insights into protein folding, has an advantage that pure domain expertise cannot match.\n\n## Dynamic Perspectives\n\nIn this new paradigm, we don't just accumulate knowledge - we develop the ability to view problems through multiple lenses simultaneously. A challenge isn't just \"a business problem\" or \"a technical problem\" but a complex system that can be approached from countless angles.\n\n## Implications for Learning and Innovation\n\nThis shift suggests we should:\n\n- Prioritize learning frameworks and mental models over memorizing facts\n- Develop the ability to rapidly switch between different disciplinary perspectives\n- Build diverse teams that can bring multiple knowledge domains to bear on problems\n- Create environments where cross-pollination of ideas is not just permitted but encouraged\n\nThe age of synthesis doesn't diminish the value of deep expertise, but it does change how that expertise is applied - not in isolation, but as part of an integrated approach to understanding and shaping our world.\n";
						const data = {author:"PVB",pubDatetime:new Date(1701777600000),title:"The Age of Synthesis - Beyond Knowledge Silos",featured:false,draft:false,tags:["AI","synthesis","innovation","strategy"],description:"AI marks the end of traditional knowledge silos, shifting focus from retrieval to synthesis."};
						const _internal = {
							type: 'content',
							filePath: "C:/Users/<USER>/Desktop/pvb-astro/src/content/blog/age-of-synthesis.md",
							rawData: undefined,
						};

export { _internal, body, collection, data, id, slug };
</file>

<file path=".netlify/build/chunks/age-of-synthesis_DLAOmwU2.mjs">
import { a as createComponent, m as maybeRenderHead, u as unescapeHTML, r as renderTemplate } from './astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import 'clsx';

const html = "<h2 id=\"the-age-of-synthesis-beyond-knowledge-silos\">The Age of Synthesis: Beyond Knowledge Silos</h2>\n<p>AI marks the end of traditional knowledge silos, shifting focus from retrieval to synthesis. Mastery is no longer about expertise in a singular field but about integrating patterns across domains. Reality isn’t boxed into subjects but viewed through dynamic lenses, enabling a new age of strategy and innovation.</p>\n<h2 id=\"the-shift-from-retrieval-to-integration\">The Shift from Retrieval to Integration</h2>\n<p>For centuries, human knowledge has been organized into discrete categories - biology, physics, economics, psychology. We’ve built educational systems, career paths, and entire identities around these divisions. But as AI systems demonstrate the ability to retrieve and process information across all domains simultaneously, the value is shifting from knowing facts to connecting them in meaningful ways.</p>\n<h2 id=\"cross-domain-pattern-recognition\">Cross-Domain Pattern Recognition</h2>\n<p>The most innovative solutions often come from applying principles from one field to problems in another. The person who can recognize that a biological system’s resilience might inform financial market stability, or that linguistic structures might offer insights into protein folding, has an advantage that pure domain expertise cannot match.</p>\n<h2 id=\"dynamic-perspectives\">Dynamic Perspectives</h2>\n<p>In this new paradigm, we don’t just accumulate knowledge - we develop the ability to view problems through multiple lenses simultaneously. A challenge isn’t just “a business problem” or “a technical problem” but a complex system that can be approached from countless angles.</p>\n<h2 id=\"implications-for-learning-and-innovation\">Implications for Learning and Innovation</h2>\n<p>This shift suggests we should:</p>\n<ul>\n<li>Prioritize learning frameworks and mental models over memorizing facts</li>\n<li>Develop the ability to rapidly switch between different disciplinary perspectives</li>\n<li>Build diverse teams that can bring multiple knowledge domains to bear on problems</li>\n<li>Create environments where cross-pollination of ideas is not just permitted but encouraged</li>\n</ul>\n<p>The age of synthesis doesn’t diminish the value of deep expertise, but it does change how that expertise is applied - not in isolation, but as part of an integrated approach to understanding and shaping our world.</p>";

				const frontmatter = {"title":"The Age of Synthesis - Beyond Knowledge Silos","author":"PVB","pubDatetime":"2023-12-05T12:00:00.000Z","featured":false,"draft":false,"tags":["AI","synthesis","innovation","strategy"],"description":"AI marks the end of traditional knowledge silos, shifting focus from retrieval to synthesis."};
				const file = "C:/Users/<USER>/Desktop/pvb-astro/src/content/blog/age-of-synthesis.md";
				const url = undefined;
				function rawContent() {
					return "\n## The Age of Synthesis: Beyond Knowledge Silos\n\nAI marks the end of traditional knowledge silos, shifting focus from retrieval to synthesis. Mastery is no longer about expertise in a singular field but about integrating patterns across domains. Reality isn't boxed into subjects but viewed through dynamic lenses, enabling a new age of strategy and innovation.\n\n## The Shift from Retrieval to Integration\n\nFor centuries, human knowledge has been organized into discrete categories - biology, physics, economics, psychology. We've built educational systems, career paths, and entire identities around these divisions. But as AI systems demonstrate the ability to retrieve and process information across all domains simultaneously, the value is shifting from knowing facts to connecting them in meaningful ways.\n\n## Cross-Domain Pattern Recognition\n\nThe most innovative solutions often come from applying principles from one field to problems in another. The person who can recognize that a biological system's resilience might inform financial market stability, or that linguistic structures might offer insights into protein folding, has an advantage that pure domain expertise cannot match.\n\n## Dynamic Perspectives\n\nIn this new paradigm, we don't just accumulate knowledge - we develop the ability to view problems through multiple lenses simultaneously. A challenge isn't just \"a business problem\" or \"a technical problem\" but a complex system that can be approached from countless angles.\n\n## Implications for Learning and Innovation\n\nThis shift suggests we should:\n\n- Prioritize learning frameworks and mental models over memorizing facts\n- Develop the ability to rapidly switch between different disciplinary perspectives\n- Build diverse teams that can bring multiple knowledge domains to bear on problems\n- Create environments where cross-pollination of ideas is not just permitted but encouraged\n\nThe age of synthesis doesn't diminish the value of deep expertise, but it does change how that expertise is applied - not in isolation, but as part of an integrated approach to understanding and shaping our world.\n";
				}
				function compiledContent() {
					return html;
				}
				function getHeadings() {
					return [{"depth":2,"slug":"the-age-of-synthesis-beyond-knowledge-silos","text":"The Age of Synthesis: Beyond Knowledge Silos"},{"depth":2,"slug":"the-shift-from-retrieval-to-integration","text":"The Shift from Retrieval to Integration"},{"depth":2,"slug":"cross-domain-pattern-recognition","text":"Cross-Domain Pattern Recognition"},{"depth":2,"slug":"dynamic-perspectives","text":"Dynamic Perspectives"},{"depth":2,"slug":"implications-for-learning-and-innovation","text":"Implications for Learning and Innovation"}];
				}

				const Content = createComponent((result, _props, slots) => {
					const { layout, ...content } = frontmatter;
					content.file = file;
					content.url = url;

					return renderTemplate`${maybeRenderHead()}${unescapeHTML(html)}`;
				});

export { Content, compiledContent, Content as default, file, frontmatter, getHeadings, rawContent, url };
</file>

<file path=".netlify/build/chunks/age-of-synthesis_DqChI2cq.mjs">
async function getMod() {
						return import('./age-of-synthesis_DLAOmwU2.mjs');
					}
					const collectedLinks = [];
					const collectedStyles = [];
					const collectedScripts = [];
					const defaultMod = { __astroPropagation: true, getMod, collectedLinks, collectedStyles, collectedScripts };

export { defaultMod as default };
</file>

<file path=".netlify/build/chunks/astro_YO2Xtdzn.mjs">
import 'kleur/colors';
import './astro/server_Dba0FyIl.mjs';
import 'clsx';
</file>

<file path=".netlify/build/chunks/astro/assets-service_DIMzS0Of.mjs">
import { isRemotePath, joinPaths } from '@astrojs/internal-helpers/path';

const MissingMediaQueryDirective = {
  name: "MissingMediaQueryDirective",
  title: "Missing value for `client:media` directive.",
  message: 'Media query not provided for `client:media` directive. A media query similar to `client:media="(max-width: 600px)"` must be provided'
};
const NoMatchingRenderer = {
  name: "NoMatchingRenderer",
  title: "No matching renderer found.",
  message: (componentName, componentExtension, plural, validRenderersCount) => `Unable to render \`${componentName}\`.

${validRenderersCount > 0 ? `There ${plural ? "are" : "is"} ${validRenderersCount} renderer${plural ? "s" : ""} configured in your \`astro.config.mjs\` file,
but ${plural ? "none were" : "it was not"} able to server-side render \`${componentName}\`.` : `No valid renderer was found ${componentExtension ? `for the \`.${componentExtension}\` file extension.` : `for this file extension.`}`}`,
  hint: (probableRenderers) => `Did you mean to enable the ${probableRenderers} integration?

See https://docs.astro.build/en/guides/framework-components/ for more information on how to install and configure integrations.`
};
const NoClientEntrypoint = {
  name: "NoClientEntrypoint",
  title: "No client entrypoint specified in renderer.",
  message: (componentName, clientDirective, rendererName) => `\`${componentName}\` component has a \`client:${clientDirective}\` directive, but no client entrypoint was provided by \`${rendererName}\`.`,
  hint: "See https://docs.astro.build/en/reference/integrations-reference/#addrenderer-option for more information on how to configure your renderer."
};
const NoClientOnlyHint = {
  name: "NoClientOnlyHint",
  title: "Missing hint on client:only directive.",
  message: (componentName) => `Unable to render \`${componentName}\`. When using the \`client:only\` hydration strategy, Astro needs a hint to use the correct renderer.`,
  hint: (probableRenderers) => `Did you mean to pass \`client:only="${probableRenderers}"\`? See https://docs.astro.build/en/reference/directives-reference/#clientonly for more information on client:only`
};
const NoMatchingImport = {
  name: "NoMatchingImport",
  title: "No import found for component.",
  message: (componentName) => `Could not render \`${componentName}\`. No matching import has been found for \`${componentName}\`.`,
  hint: "Please make sure the component is properly imported."
};
const InvalidComponentArgs = {
  name: "InvalidComponentArgs",
  title: "Invalid component arguments.",
  message: (name) => `Invalid arguments passed to${name ? ` <${name}>` : ""} component.`,
  hint: "Astro components cannot be rendered directly via function call, such as `Component()` or `{items.map(Component)}`."
};
const ImageMissingAlt = {
  name: "ImageMissingAlt",
  title: 'Image missing required "alt" property.',
  message: 'Image missing "alt" property. "alt" text is required to describe important images on the page.',
  hint: 'Use an empty string ("") for decorative images.'
};
const InvalidImageService = {
  name: "InvalidImageService",
  title: "Error while loading image service.",
  message: "There was an error loading the configured image service. Please see the stack trace for more information."
};
const MissingImageDimension = {
  name: "MissingImageDimension",
  title: "Missing image dimensions",
  message: (missingDimension, imageURL) => `Missing ${missingDimension === "both" ? "width and height attributes" : `${missingDimension} attribute`} for ${imageURL}. When using remote images, both dimensions are required in order to avoid CLS.`,
  hint: "If your image is inside your `src` folder, you probably meant to import it instead. See [the Imports guide for more information](https://docs.astro.build/en/guides/imports/#other-assets). You can also use `inferSize={true}` for remote images to get the original dimensions."
};
const FailedToFetchRemoteImageDimensions = {
  name: "FailedToFetchRemoteImageDimensions",
  title: "Failed to retrieve remote image dimensions",
  message: (imageURL) => `Failed to get the dimensions for ${imageURL}.`,
  hint: "Verify your remote image URL is accurate, and that you are not using `inferSize` with a file located in your `public/` folder."
};
const UnsupportedImageFormat = {
  name: "UnsupportedImageFormat",
  title: "Unsupported image format",
  message: (format, imagePath, supportedFormats) => `Received unsupported format \`${format}\` from \`${imagePath}\`. Currently only ${supportedFormats.join(
    ", "
  )} are supported by our image services.`,
  hint: "Using an `img` tag directly instead of the `Image` component might be what you're looking for."
};
const UnsupportedImageConversion = {
  name: "UnsupportedImageConversion",
  title: "Unsupported image conversion",
  message: "Converting between vector (such as SVGs) and raster (such as PNGs and JPEGs) images is not currently supported."
};
const ExpectedImage = {
  name: "ExpectedImage",
  title: "Expected src to be an image.",
  message: (src, typeofOptions, fullOptions) => `Expected \`src\` property for \`getImage\` or \`<Image />\` to be either an ESM imported image or a string with the path of a remote image. Received \`${src}\` (type: \`${typeofOptions}\`).

Full serialized options received: \`${fullOptions}\`.`,
  hint: "This error can often happen because of a wrong path. Make sure the path to your image is correct. If you're passing an async function, make sure to call and await it."
};
const ExpectedImageOptions = {
  name: "ExpectedImageOptions",
  title: "Expected image options.",
  message: (options) => `Expected getImage() parameter to be an object. Received \`${options}\`.`
};
const ExpectedNotESMImage = {
  name: "ExpectedNotESMImage",
  title: "Expected image options, not an ESM-imported image.",
  message: "An ESM-imported image cannot be passed directly to `getImage()`. Instead, pass an object with the image in the `src` property.",
  hint: "Try changing `getImage(myImage)` to `getImage({ src: myImage })`"
};
const IncompatibleDescriptorOptions = {
  name: "IncompatibleDescriptorOptions",
  title: "Cannot set both `densities` and `widths`",
  message: "Only one of `densities` or `widths` can be specified. In most cases, you'll probably want to use only `widths` if you require specific widths.",
  hint: "Those attributes are used to construct a `srcset` attribute, which cannot have both `x` and `w` descriptors."
};
const NoImageMetadata = {
  name: "NoImageMetadata",
  title: "Could not process image metadata.",
  message: (imagePath) => `Could not process image metadata${imagePath ? ` for \`${imagePath}\`` : ""}.`,
  hint: "This is often caused by a corrupted or malformed image. Re-exporting the image from your image editor may fix this issue."
};
const LocalImageUsedWrongly = {
  name: "LocalImageUsedWrongly",
  title: "Local images must be imported.",
  message: (imageFilePath) => `\`Image\`'s and \`getImage\`'s \`src\` parameter must be an imported image or an URL, it cannot be a string filepath. Received \`${imageFilePath}\`.`,
  hint: "If you want to use an image from your `src` folder, you need to either import it or if the image is coming from a content collection, use the [image() schema helper](https://docs.astro.build/en/guides/images/#images-in-content-collections). See https://docs.astro.build/en/guides/images/#src-required for more information on the `src` property."
};
const AstroGlobUsedOutside = {
  name: "AstroGlobUsedOutside",
  title: "Astro.glob() used outside of an Astro file.",
  message: (globStr) => `\`Astro.glob(${globStr})\` can only be used in \`.astro\` files. \`import.meta.glob(${globStr})\` can be used instead to achieve a similar result.`,
  hint: "See Vite's documentation on `import.meta.glob` for more information: https://vite.dev/guide/features.html#glob-import"
};
const AstroGlobNoMatch = {
  name: "AstroGlobNoMatch",
  title: "Astro.glob() did not match any files.",
  message: (globStr) => `\`Astro.glob(${globStr})\` did not return any matching files.`,
  hint: "Check the pattern for typos."
};
const MissingSharp = {
  name: "MissingSharp",
  title: "Could not find Sharp.",
  message: "Could not find Sharp. Please install Sharp (`sharp`) manually into your project or migrate to another image service.",
  hint: "See Sharp's installation instructions for more information: https://sharp.pixelplumbing.com/install. If you are not relying on `astro:assets` to optimize, transform, or process any images, you can configure a passthrough image service instead of installing Sharp. See https://docs.astro.build/en/reference/errors/missing-sharp for more information.\n\nSee https://docs.astro.build/en/guides/images/#default-image-service for more information on how to migrate to another image service."
};
const UnknownContentCollectionError = {
  name: "UnknownContentCollectionError",
  title: "Unknown Content Collection Error."
};

function normalizeLF(code) {
  return code.replace(/\r\n|\r(?!\n)|\n/g, "\n");
}

function codeFrame(src, loc) {
  if (!loc || loc.line === void 0 || loc.column === void 0) {
    return "";
  }
  const lines = normalizeLF(src).split("\n").map((ln) => ln.replace(/\t/g, "  "));
  const visibleLines = [];
  for (let n = -2; n <= 2; n++) {
    if (lines[loc.line + n]) visibleLines.push(loc.line + n);
  }
  let gutterWidth = 0;
  for (const lineNo of visibleLines) {
    let w = `> ${lineNo}`;
    if (w.length > gutterWidth) gutterWidth = w.length;
  }
  let output = "";
  for (const lineNo of visibleLines) {
    const isFocusedLine = lineNo === loc.line - 1;
    output += isFocusedLine ? "> " : "  ";
    output += `${lineNo + 1} | ${lines[lineNo]}
`;
    if (isFocusedLine)
      output += `${Array.from({ length: gutterWidth }).join(" ")}  | ${Array.from({
        length: loc.column
      }).join(" ")}^
`;
  }
  return output;
}

class AstroError extends Error {
  loc;
  title;
  hint;
  frame;
  type = "AstroError";
  constructor(props, options) {
    const { name, title, message, stack, location, hint, frame } = props;
    super(message, options);
    this.title = title;
    this.name = name;
    if (message) this.message = message;
    this.stack = stack ? stack : this.stack;
    this.loc = location;
    this.hint = hint;
    this.frame = frame;
  }
  setLocation(location) {
    this.loc = location;
  }
  setName(name) {
    this.name = name;
  }
  setMessage(message) {
    this.message = message;
  }
  setHint(hint) {
    this.hint = hint;
  }
  setFrame(source, location) {
    this.frame = codeFrame(source, location);
  }
  static is(err) {
    return err.type === "AstroError";
  }
}

const VALID_INPUT_FORMATS = [
  "jpeg",
  "jpg",
  "png",
  "tiff",
  "webp",
  "gif",
  "svg",
  "avif"
];
const VALID_SUPPORTED_FORMATS = [
  "jpeg",
  "jpg",
  "png",
  "tiff",
  "webp",
  "gif",
  "svg",
  "avif"
];
const DEFAULT_OUTPUT_FORMAT = "webp";
const DEFAULT_HASH_PROPS = ["src", "width", "height", "format", "quality"];

function isESMImportedImage(src) {
  return typeof src === "object";
}
function isRemoteImage(src) {
  return typeof src === "string";
}
async function resolveSrc(src) {
  return typeof src === "object" && "then" in src ? (await src).default ?? await src : src;
}

function matchPattern(url, remotePattern) {
  return matchProtocol(url, remotePattern.protocol) && matchHostname(url, remotePattern.hostname, true) && matchPort(url, remotePattern.port) && matchPathname(url, remotePattern.pathname);
}
function matchPort(url, port) {
  return !port || port === url.port;
}
function matchProtocol(url, protocol) {
  return !protocol || protocol === url.protocol.slice(0, -1);
}
function matchHostname(url, hostname, allowWildcard) {
  if (!hostname) {
    return true;
  } else if (!allowWildcard || !hostname.startsWith("*")) {
    return hostname === url.hostname;
  } else if (hostname.startsWith("**.")) {
    const slicedHostname = hostname.slice(2);
    return slicedHostname !== url.hostname && url.hostname.endsWith(slicedHostname);
  } else if (hostname.startsWith("*.")) {
    const slicedHostname = hostname.slice(1);
    const additionalSubdomains = url.hostname.replace(slicedHostname, "").split(".").filter(Boolean);
    return additionalSubdomains.length === 1;
  }
  return false;
}
function matchPathname(url, pathname, allowWildcard) {
  if (!pathname) {
    return true;
  } else if (!pathname.endsWith("*")) {
    return pathname === url.pathname;
  } else if (pathname.endsWith("/**")) {
    const slicedPathname = pathname.slice(0, -2);
    return slicedPathname !== url.pathname && url.pathname.startsWith(slicedPathname);
  } else if (pathname.endsWith("/*")) {
    const slicedPathname = pathname.slice(0, -1);
    const additionalPathChunks = url.pathname.replace(slicedPathname, "").split("/").filter(Boolean);
    return additionalPathChunks.length === 1;
  }
  return false;
}
function isRemoteAllowed(src, {
  domains = [],
  remotePatterns = []
}) {
  if (!isRemotePath(src)) return false;
  const url = new URL(src);
  return domains.some((domain) => matchHostname(url, domain)) || remotePatterns.some((remotePattern) => matchPattern(url, remotePattern));
}

function isLocalService(service) {
  if (!service) {
    return false;
  }
  return "transform" in service;
}
function parseQuality(quality) {
  let result = parseInt(quality);
  if (Number.isNaN(result)) {
    return quality;
  }
  return result;
}
const baseService = {
  validateOptions(options) {
    if (!options.src || typeof options.src !== "string" && typeof options.src !== "object") {
      throw new AstroError({
        ...ExpectedImage,
        message: ExpectedImage.message(
          JSON.stringify(options.src),
          typeof options.src,
          JSON.stringify(options, (_, v) => v === void 0 ? null : v)
        )
      });
    }
    if (!isESMImportedImage(options.src)) {
      if (options.src.startsWith("/@fs/") || !isRemotePath(options.src) && !options.src.startsWith("/")) {
        throw new AstroError({
          ...LocalImageUsedWrongly,
          message: LocalImageUsedWrongly.message(options.src)
        });
      }
      let missingDimension;
      if (!options.width && !options.height) {
        missingDimension = "both";
      } else if (!options.width && options.height) {
        missingDimension = "width";
      } else if (options.width && !options.height) {
        missingDimension = "height";
      }
      if (missingDimension) {
        throw new AstroError({
          ...MissingImageDimension,
          message: MissingImageDimension.message(missingDimension, options.src)
        });
      }
    } else {
      if (!VALID_SUPPORTED_FORMATS.includes(options.src.format)) {
        throw new AstroError({
          ...UnsupportedImageFormat,
          message: UnsupportedImageFormat.message(
            options.src.format,
            options.src.src,
            VALID_SUPPORTED_FORMATS
          )
        });
      }
      if (options.widths && options.densities) {
        throw new AstroError(IncompatibleDescriptorOptions);
      }
      if (options.src.format === "svg") {
        options.format = "svg";
      }
      if (options.src.format === "svg" && options.format !== "svg" || options.src.format !== "svg" && options.format === "svg") {
        throw new AstroError(UnsupportedImageConversion);
      }
    }
    if (!options.format) {
      options.format = DEFAULT_OUTPUT_FORMAT;
    }
    if (options.width) options.width = Math.round(options.width);
    if (options.height) options.height = Math.round(options.height);
    return options;
  },
  getHTMLAttributes(options) {
    const { targetWidth, targetHeight } = getTargetDimensions(options);
    const { src, width, height, format, quality, densities, widths, formats, ...attributes } = options;
    return {
      ...attributes,
      width: targetWidth,
      height: targetHeight,
      loading: attributes.loading ?? "lazy",
      decoding: attributes.decoding ?? "async"
    };
  },
  getSrcSet(options) {
    const srcSet = [];
    const { targetWidth } = getTargetDimensions(options);
    const { widths, densities } = options;
    const targetFormat = options.format ?? DEFAULT_OUTPUT_FORMAT;
    let imageWidth = options.width;
    let maxWidth = Infinity;
    if (isESMImportedImage(options.src)) {
      imageWidth = options.src.width;
      maxWidth = imageWidth;
    }
    const {
      width: transformWidth,
      height: transformHeight,
      ...transformWithoutDimensions
    } = options;
    const allWidths = [];
    if (densities) {
      const densityValues = densities.map((density) => {
        if (typeof density === "number") {
          return density;
        } else {
          return parseFloat(density);
        }
      });
      const densityWidths = densityValues.sort().map((density) => Math.round(targetWidth * density));
      allWidths.push(
        ...densityWidths.map((width, index) => ({
          maxTargetWidth: Math.min(width, maxWidth),
          descriptor: `${densityValues[index]}x`
        }))
      );
    } else if (widths) {
      allWidths.push(
        ...widths.map((width) => ({
          maxTargetWidth: Math.min(width, maxWidth),
          descriptor: `${width}w`
        }))
      );
    }
    for (const { maxTargetWidth, descriptor } of allWidths) {
      const srcSetTransform = { ...transformWithoutDimensions };
      if (maxTargetWidth !== imageWidth) {
        srcSetTransform.width = maxTargetWidth;
      } else {
        if (options.width && options.height) {
          srcSetTransform.width = options.width;
          srcSetTransform.height = options.height;
        }
      }
      srcSet.push({
        transform: srcSetTransform,
        descriptor,
        attributes: {
          type: `image/${targetFormat}`
        }
      });
    }
    return srcSet;
  },
  getURL(options, imageConfig) {
    const searchParams = new URLSearchParams();
    if (isESMImportedImage(options.src)) {
      searchParams.append("href", options.src.src);
    } else if (isRemoteAllowed(options.src, imageConfig)) {
      searchParams.append("href", options.src);
    } else {
      return options.src;
    }
    const params = {
      w: "width",
      h: "height",
      q: "quality",
      f: "format"
    };
    Object.entries(params).forEach(([param, key]) => {
      options[key] && searchParams.append(param, options[key].toString());
    });
    const imageEndpoint = joinPaths("/", "/_image");
    return `${imageEndpoint}?${searchParams}`;
  },
  parseURL(url) {
    const params = url.searchParams;
    if (!params.has("href")) {
      return void 0;
    }
    const transform = {
      src: params.get("href"),
      width: params.has("w") ? parseInt(params.get("w")) : void 0,
      height: params.has("h") ? parseInt(params.get("h")) : void 0,
      format: params.get("f"),
      quality: params.get("q")
    };
    return transform;
  }
};
function getTargetDimensions(options) {
  let targetWidth = options.width;
  let targetHeight = options.height;
  if (isESMImportedImage(options.src)) {
    const aspectRatio = options.src.width / options.src.height;
    if (targetHeight && !targetWidth) {
      targetWidth = Math.round(targetHeight * aspectRatio);
    } else if (targetWidth && !targetHeight) {
      targetHeight = Math.round(targetWidth / aspectRatio);
    } else if (!targetWidth && !targetHeight) {
      targetWidth = options.src.width;
      targetHeight = options.src.height;
    }
  }
  return {
    targetWidth,
    targetHeight
  };
}

let sharp;
const qualityTable = {
  low: 25,
  mid: 50,
  high: 80,
  max: 100
};
async function loadSharp() {
  let sharpImport;
  try {
    sharpImport = (await import('sharp')).default;
  } catch {
    throw new AstroError(MissingSharp);
  }
  sharpImport.cache(false);
  return sharpImport;
}
const sharpService = {
  validateOptions: baseService.validateOptions,
  getURL: baseService.getURL,
  parseURL: baseService.parseURL,
  getHTMLAttributes: baseService.getHTMLAttributes,
  getSrcSet: baseService.getSrcSet,
  async transform(inputBuffer, transformOptions, config) {
    if (!sharp) sharp = await loadSharp();
    const transform = transformOptions;
    if (transform.format === "svg") return { data: inputBuffer, format: "svg" };
    const result = sharp(inputBuffer, {
      failOnError: false,
      pages: -1,
      limitInputPixels: config.service.config.limitInputPixels
    });
    result.rotate();

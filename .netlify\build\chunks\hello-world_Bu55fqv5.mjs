import { a as createComponent, m as maybeRenderHead, u as unescapeHTML, r as renderTemplate } from './astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import 'clsx';

const html = "<h2 id=\"welcome-to-my-blog\">Welcome to my blog</h2>\n<p>This is the first post on my new blog. I’ve set up this blog using Astro to create a fast, minimal, and personal blogging experience.</p>\n<h2 id=\"why-i-started-this-blog\">Why I started this blog</h2>\n<p>I wanted a place to share my thoughts, projects, and experiences. This blog will be a collection of my work, ideas, and learnings.</p>\n<h2 id=\"what-to-expect\">What to expect</h2>\n<p>I’ll be posting about:</p>\n<ul>\n<li>Personal projects</li>\n<li>Things I’m learning</li>\n<li>Thoughts and reflections</li>\n<li>Tutorials and guides</li>\n</ul>\n<p>Stay tuned for more content coming soon!</p>\n<h2 id=\"table-of-contents\">Table of contents</h2>\n<h2 id=\"code-examples\">Code examples</h2>\n<p>Here’s a simple code example:</p>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto; white-space: pre-wrap; word-wrap: break-word;\" tabindex=\"0\" data-language=\"javascript\"><code><span class=\"line\"><span style=\"color:#F97583\">function</span><span style=\"color:#B392F0\"> greet</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#FFAB70\">name</span><span style=\"color:#E1E4E8\">) {</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">  return</span><span style=\"color:#9ECBFF\"> `Hello, ${</span><span style=\"color:#E1E4E8\">name</span><span style=\"color:#9ECBFF\">}!`</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">console.</span><span style=\"color:#B392F0\">log</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#B392F0\">greet</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#9ECBFF\">\"World\"</span><span style=\"color:#E1E4E8\">));</span></span>\n<span class=\"line\"></span></code></pre>\n<h2 id=\"conclusion\">Conclusion</h2>\n<p>Thanks for reading my first blog post. I’m excited to share more content with you in the future!</p>";

				const frontmatter = {"title":"Hello World - My First Blog Post","author":"PVB","pubDatetime":"2023-04-01T12:00:00.000Z","featured":true,"draft":false,"tags":["introduction","blogging"],"description":"This is my first blog post using the new Astro-powered blog system."};
				const file = "C:/Users/<USER>/Desktop/pvb-astro/src/content/blog/hello-world.md";
				const url = undefined;
				function rawContent() {
					return "\n## Welcome to my blog\n\nThis is the first post on my new blog. I've set up this blog using Astro to create a fast, minimal, and personal blogging experience.\n\n## Why I started this blog\n\nI wanted a place to share my thoughts, projects, and experiences. This blog will be a collection of my work, ideas, and learnings.\n\n## What to expect\n\nI'll be posting about:\n\n- Personal projects\n- Things I'm learning\n- Thoughts and reflections\n- Tutorials and guides\n\nStay tuned for more content coming soon!\n\n## Table of contents\n\n## Code examples\n\nHere's a simple code example:\n\n```javascript\nfunction greet(name) {\n  return `Hello, ${name}!`;\n}\n\nconsole.log(greet(\"World\"));\n```\n\n## Conclusion\n\nThanks for reading my first blog post. I'm excited to share more content with you in the future!\n";
				}
				function compiledContent() {
					return html;
				}
				function getHeadings() {
					return [{"depth":2,"slug":"welcome-to-my-blog","text":"Welcome to my blog"},{"depth":2,"slug":"why-i-started-this-blog","text":"Why I started this blog"},{"depth":2,"slug":"what-to-expect","text":"What to expect"},{"depth":2,"slug":"table-of-contents","text":"Table of contents"},{"depth":2,"slug":"code-examples","text":"Code examples"},{"depth":2,"slug":"conclusion","text":"Conclusion"}];
				}

				const Content = createComponent((result, _props, slots) => {
					const { layout, ...content } = frontmatter;
					content.file = file;
					content.url = url;

					return renderTemplate`${maybeRenderHead()}${unescapeHTML(html)}`;
				});

export { Content, compiledContent, Content as default, file, frontmatter, getHeadings, rawContent, url };

import { a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../chunks/astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import { g as getCollection } from '../chunks/_astro_content_CcaQj6Wl.mjs';
import { $ as $$Layout } from '../chunks/Layout_rXbp99fE.mjs';
import { $ as $$Tag } from '../chunks/Tag_DSxhj6Zu.mjs';
import { g as getUniqueTags } from '../chunks/getUniqueTags_3cIFKCqm.mjs';
/* empty css                                 */
export { renderers } from '../renderers.mjs';

const $$Index = createComponent(async ($$result, $$props, $$slots) => {
  const posts = await getCollection("blog");
  const tags = getUniqueTags(posts);
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "pageTitle": "Tags | PVB", "isHomePage": false, "accentColor": "#f0f0f0", "bgColor": "rgba(0, 0, 0, 0.9)", "backgroundImageUrl": "none", "style": "background: linear-gradient(to bottom right, rgba(0, 0, 0, 0.95), rgba(50, 50, 50, 0.9))", "bodyDataPage": "tags", "data-astro-cid-os4i7owy": true }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="quote-container" style="max-width: 800px;" data-astro-cid-os4i7owy> <h1 class="text-3xl font-bold mb-4 text-accent" data-astro-cid-os4i7owy>Tags</h1> <p class="mb-8 text-muted" data-astro-cid-os4i7owy>Browse posts by topic</p> <div class="tags-container" data-astro-cid-os4i7owy> ${tags.map(
    ({ tag, tagName }) => renderTemplate`${renderComponent($$result2, "Tag", $$Tag, { "tag": tag, "tagName": tagName, "size": "lg", "data-astro-cid-os4i7owy": true })}`
  )} </div> <div class="mt-10" data-astro-cid-os4i7owy> <a href="/blog" class="back-link" data-astro-cid-os4i7owy> <span class="back-arrow" data-astro-cid-os4i7owy>←</span> <span data-astro-cid-os4i7owy>Back to blog</span> </a> </div> </div> ` })} `;
}, "C:/Users/<USER>/Desktop/pvb-astro/src/pages/tags/index.astro", void 0);

const $$file = "C:/Users/<USER>/Desktop/pvb-astro/src/pages/tags/index.astro";
const $$url = "/tags";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Index,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

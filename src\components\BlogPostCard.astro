---
import { slugifyStr } from "../utils/slugify";
import { getContentType } from "../utils/unifiedContent.js";
import Tag from "./Tag.astro";

export interface Props {
  post: any; // Using 'any' for Ghost content
}

const { post } = Astro.props;

// Handle both traditional blog entries and Ghost JSON content
const title = post.title || post.data?.title;
const description = post.excerpt || post.meta_description || post.data?.description;
const pubDatetime = post.published_at ? new Date(post.published_at) : post.data?.pubDatetime;
// Process tags for display and create objects with name and slug properties
const tags = post.tags ? post.tags.map((tag: any) => {
  if (typeof tag === 'object' && tag !== null) {
    // Ghost format - tag is an object with name and slug properties
    return {
      name: tag.name,
      slug: tag.slug || slugifyStr(tag.name)
    };
  } else if (typeof tag === 'string') {
    // String format - create an object with name and slugified name
    return {
      name: tag,
      slug: slugifyStr(tag)
    };
  }
  return null;
}).filter(Boolean) : post.data?.tags?.map((tag: string) => ({
  name: tag,
  slug: slugifyStr(tag)
})) || [];

// Filter out internal tags
const internalTagSlugs = ['blog','work','archive'];
const filteredTags = tags.filter(tag => !internalTagSlugs.includes(tag.slug.toLowerCase()));

const type = getContentType(post);
const postUrl = type === 'work' ? `/work/${post.slug}/` : `/blog/${post.slug}/`;
const datetime = pubDatetime instanceof Date ? pubDatetime.toISOString() : new Date().toISOString();
const postDate = pubDatetime instanceof Date ? pubDatetime.toLocaleDateString("en-US", {
  year: "numeric",
  month: "short",
  day: "numeric",
}) : "";
---

<li class="blog-post-card">
  <a
    href={postUrl}
    class="post-link"
  >
    <div class="post-content">
      <time datetime={datetime} class="post-date">{postDate}</time>
      <h3 class="post-title" transition:name={slugifyStr(title)}>
        {title}
      </h3>
      <p class="post-description">{description}</p>
      <div class="post-tags">
        {filteredTags.map((tag: {slug: string, name: string}) => <Tag tag={tag.slug} tagName={tag.name} />)}
      </div>
    </div>
  </a>
</li>

<style>
  .blog-post-card {
    opacity: 0;
    animation: fade-in-card 0.5s ease-out forwards;
    margin: 1.5rem 0;
    background-color: rgba(20, 20, 20, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.05); /* Subtle border */
    border-radius: 0.75rem;
    padding: 1.5rem;
    list-style: none;
    box-shadow: var(--shadow-card);
    transition: transform var(--transition-duration) var(--easing-standard), box-shadow var(--transition-duration) var(--easing-standard), border-color var(--transition-duration) var(--easing-standard);
  }
  .blog-post-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-card-hover-light);
    border-color: rgba(255, 255, 255, 0.1);
  }
  .blog-post-card:last-child {
    margin-bottom: 1.5rem;
  }

  .post-link {
    display: block;
    transition: transform var(--transition-duration) var(--easing-standard);
    text-decoration: none;
    color: var(--blog-text);
  }

  .post-link:hover {
    transform: translateY(-2px);
  }

  .post-date {
    display: block;
    font-size: 0.8rem;
    color: rgba(240, 240, 240, 0.6);
    margin-bottom: 0.5rem;
    font-family: 'Georgia Custom', Georgia, serif;
  }

  .post-title {
    font-size: 1.4rem; /* Adjusted for hierarchy */
    font-weight: 600;
    margin: 0 0 0.75rem 0;
    color: var(--blog-text);
    font-family: 'Georgia Custom', Georgia, serif;
    line-height: 1.3;
  }

  .post-description {
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 1rem;
    color: rgba(240, 240, 240, 0.8);
    font-family: 'Georgia Custom', Georgia, serif;
  }

  .post-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
  }

  @keyframes fade-in-card {
    from {
      opacity: 0;
      transform: translateY(4px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
</style>
import { c as createAstro, a as createComponent, m as maybeR<PERSON>Head, b as addAttribute, e as renderTransition, d as renderComponent, r as renderTemplate } from '../../chunks/astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import { g as getCollection } from '../../chunks/_astro_content_CcaQj6Wl.mjs';
import { $ as $$Layout } from '../../chunks/Layout_rXbp99fE.mjs';
import { s as slugifyStr } from '../../chunks/slugify_CHvHojPC.mjs';
import { $ as $$Tag } from '../../chunks/Tag_DSxhj6Zu.mjs';
/* empty css                                    */
/* empty css                                    */
import { g as getUniqueTags } from '../../chunks/getUniqueTags_3cIFKCqm.mjs';
export { renderers } from '../../renderers.mjs';

const $$Astro$1 = createAstro("https://pvb.com");
const $$BlogPostCard = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$1, $$props, $$slots);
  Astro2.self = $$BlogPostCard;
  const { post } = Astro2.props;
  const { title, description, pubDatetime, tags } = post.data;
  const datetime = pubDatetime.toISOString();
  const postDate = pubDatetime.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric"
  });
  return renderTemplate`${maybeRenderHead()}<li class="blog-post-card" data-astro-cid-f45vxlzk> <a${addAttribute(`/blog/${post.slug}`, "href")} class="post-link" data-astro-cid-f45vxlzk> <div class="post-content" data-astro-cid-f45vxlzk> <time${addAttribute(datetime, "datetime")} class="post-date" data-astro-cid-f45vxlzk>${postDate}</time> <h3 class="post-title" data-astro-cid-f45vxlzk${addAttribute(renderTransition($$result, "w2v4zgte", "", slugifyStr(title)), "data-astro-transition-scope")}> ${title} </h3> <p class="post-description" data-astro-cid-f45vxlzk>${description}</p> <div class="post-tags" data-astro-cid-f45vxlzk> ${tags.map((tag) => renderTemplate`${renderComponent($$result, "Tag", $$Tag, { "tag": slugifyStr(tag), "tagName": tag, "data-astro-cid-f45vxlzk": true })}`)} </div> </div> </a> </li> `;
}, "C:/Users/<USER>/Desktop/pvb-astro/src/components/BlogPostCard.astro", "self");

function getPostsByTag(posts, tag) {
  return posts.filter(
    (post) => post.data.tags?.some((postTag) => slugifyStr(postTag) === tag)
  );
}

const $$Astro = createAstro("https://pvb.com");
async function getStaticPaths() {
  const posts = await getCollection("blog");
  const tags = getUniqueTags(posts);
  return tags.map(({ tag, tagName }) => ({
    params: { tag },
    props: {
      posts: getPostsByTag(posts, tag),
      tag,
      tagName
    }
  }));
}
const $$Index = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Index;
  const { posts, tag, tagName } = Astro2.props;
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "pageTitle": `Tag: ${tagName} | PVB`, "isHomePage": false, "accentColor": "#f0f0f0", "bgColor": "rgba(0, 0, 0, 0.9)", "backgroundImageUrl": "none", "style": "background: linear-gradient(to bottom right, rgba(0, 0, 0, 0.95), rgba(50, 50, 50, 0.9))", "bodyDataPage": "tag", "data-astro-cid-hqxj4ft6": true }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="quote-container" style="max-width: 800px;" data-astro-cid-hqxj4ft6> <h1 class="text-3xl font-bold mb-4 text-accent" data-astro-cid-hqxj4ft6>
Tag: <span data-astro-cid-hqxj4ft6${addAttribute(renderTransition($$result2, "6rhg7idu", "", tag), "data-astro-transition-scope")}>#${tagName}</span> </h1> <p class="mb-8 text-muted" data-astro-cid-hqxj4ft6> ${posts.length} post${posts.length > 1 ? "s" : ""} with this tag
</p> <div class="posts-container" data-astro-cid-hqxj4ft6> <ul data-astro-cid-hqxj4ft6> ${posts.map((post) => renderTemplate`${renderComponent($$result2, "BlogPostCard", $$BlogPostCard, { "post": post, "data-astro-cid-hqxj4ft6": true })}`)} </ul> </div> <div class="mt-10" data-astro-cid-hqxj4ft6> <a href="/tags" class="back-link" data-astro-cid-hqxj4ft6> <span class="back-arrow" data-astro-cid-hqxj4ft6>←</span> <span data-astro-cid-hqxj4ft6>All tags</span> </a> </div> </div> ` })} `;
}, "C:/Users/<USER>/Desktop/pvb-astro/src/pages/tags/[tag]/index.astro", "self");

const $$file = "C:/Users/<USER>/Desktop/pvb-astro/src/pages/tags/[tag]/index.astro";
const $$url = "/tags/[tag]";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Index,
  file: $$file,
  getStaticPaths,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

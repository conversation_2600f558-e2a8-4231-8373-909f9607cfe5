import { c as createAstro, a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead, b as addAttribute } from '../../chunks/astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import { g as getCollection } from '../../chunks/_astro_content_CcaQj6Wl.mjs';
import { $ as $$BlogPost } from '../../chunks/BlogPost_DYNjvq7J.mjs';
/* empty css                                     */
export { renderers } from '../../renderers.mjs';

const SITE = {
  author: "PVB"};

const $$Astro = createAstro("https://pvb.com");
async function getStaticPaths() {
  const workEntries = await getCollection("work");
  return workEntries.map((entry) => ({
    params: { slug: entry.slug },
    props: { entry }
  }));
}
const $$ = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$;
  const { entry } = Astro2.props;
  const { Content } = await entry.render();
  const postData = {
    title: entry.data.title,
    author: SITE.author,
    description: entry.data.description,
    ogImage: entry.data.ogImage,
    pubDatetime: entry.data.projectDate,
    modDatetime: null,
    tags: entry.data.tags,
    featured: entry.data.featured,
    draft: false,
    canonicalURL: entry.data.canonicalURL
  };
  const { repoUrl, liveUrl, status } = entry.data;
  function formatDate(date) {
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long"
    });
  }
  return renderTemplate`${renderComponent($$result, "BlogPostLayout", $$BlogPost, { "post": { ...entry, data: postData }, "data-astro-cid-qwekciqp": true }, { "default": async ($$result2) => renderTemplate`  ${maybeRenderHead()}<div class="project-meta" data-astro-cid-qwekciqp> <div class="meta-grid" data-astro-cid-qwekciqp> <div class="meta-item" data-astro-cid-qwekciqp> <div class="meta-label" data-astro-cid-qwekciqp>Timeline</div> <div class="meta-value" data-astro-cid-qwekciqp>${formatDate(postData.pubDatetime)}</div> </div> ${status && status !== "Completed" && renderTemplate`<div class="meta-item" data-astro-cid-qwekciqp> <div class="meta-label" data-astro-cid-qwekciqp>Status</div> <div class="meta-value" data-astro-cid-qwekciqp> <span class="status-pill" data-astro-cid-qwekciqp>${status}</span> </div> </div>`} </div> <div class="project-links" data-astro-cid-qwekciqp> ${repoUrl && renderTemplate`<a${addAttribute(repoUrl, "href")} target="_blank" rel="noopener noreferrer" class="project-link" data-astro-cid-qwekciqp>
Source <span class="link-arrow" data-astro-cid-qwekciqp>↗</span> </a>`} ${liveUrl && renderTemplate`<a${addAttribute(liveUrl, "href")} target="_blank" rel="noopener noreferrer" class="project-link" data-astro-cid-qwekciqp>
Demo <span class="link-arrow" data-astro-cid-qwekciqp>↗</span> </a>`} </div> </div>  <div class="project-content" data-astro-cid-qwekciqp> ${renderComponent($$result2, "Content", Content, { "data-astro-cid-qwekciqp": true })} </div>  <div class="project-technologies" data-astro-cid-qwekciqp> <div class="tech-label" data-astro-cid-qwekciqp>Technologies</div> <div class="tech-tags" data-astro-cid-qwekciqp> ${postData.tags.map((tag) => renderTemplate`<span class="tech-tag" data-astro-cid-qwekciqp>${tag}</span>`)} </div> </div> ` })}  `;
}, "C:/Users/<USER>/Desktop/pvb-astro/src/pages/work/[...slug].astro", void 0);

const $$file = "C:/Users/<USER>/Desktop/pvb-astro/src/pages/work/[...slug].astro";
const $$url = "/work/[...slug]";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$,
  file: $$file,
  getStaticPaths,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

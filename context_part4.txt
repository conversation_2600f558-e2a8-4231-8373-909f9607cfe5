    const ispeBox = ipcoBox && findBox(buffer, "ispe", ipcoBox.offset + 8);
    if (ispeBox) {
      return {
        height: readUInt32BE(buffer, ispeBox.offset + 16),
        width: readUInt32BE(buffer, ispeBox.offset + 12),
        type: detectBrands(buffer, 8, metaBox.offset)
      };
    }
    throw new TypeError("Invalid HEIF, no size found");
  }
};

const SIZE_HEADER = 4 + 4;
const FILE_LENGTH_OFFSET = 4;
const ENTRY_LENGTH_OFFSET = 4;
const ICON_TYPE_SIZE = {
  ICON: 32,
  "ICN#": 32,
  // m => 16 x 16
  "icm#": 16,
  icm4: 16,
  icm8: 16,
  // s => 16 x 16
  "ics#": 16,
  ics4: 16,
  ics8: 16,
  is32: 16,
  s8mk: 16,
  icp4: 16,
  // l => 32 x 32
  icl4: 32,
  icl8: 32,
  il32: 32,
  l8mk: 32,
  icp5: 32,
  ic11: 32,
  // h => 48 x 48
  ich4: 48,
  ich8: 48,
  ih32: 48,
  h8mk: 48,
  // . => 64 x 64
  icp6: 64,
  ic12: 32,
  // t => 128 x 128
  it32: 128,
  t8mk: 128,
  ic07: 128,
  // . => 256 x 256
  ic08: 256,
  ic13: 256,
  // . => 512 x 512
  ic09: 512,
  ic14: 512,
  // . => 1024 x 1024
  ic10: 1024
};
function readImageHeader(input, imageOffset) {
  const imageLengthOffset = imageOffset + ENTRY_LENGTH_OFFSET;
  return [
    toUTF8String(input, imageOffset, imageLengthOffset),
    readUInt32BE(input, imageLengthOffset)
  ];
}
function getImageSize(type) {
  const size = ICON_TYPE_SIZE[type];
  return { width: size, height: size, type };
}
const ICNS = {
  validate: (input) => toUTF8String(input, 0, 4) === "icns",
  calculate(input) {
    const inputLength = input.length;
    const fileLength = readUInt32BE(input, FILE_LENGTH_OFFSET);
    let imageOffset = SIZE_HEADER;
    let imageHeader = readImageHeader(input, imageOffset);
    let imageSize = getImageSize(imageHeader[0]);
    imageOffset += imageHeader[1];
    if (imageOffset === fileLength) return imageSize;
    const result = {
      height: imageSize.height,
      images: [imageSize],
      width: imageSize.width
    };
    while (imageOffset < fileLength && imageOffset < inputLength) {
      imageHeader = readImageHeader(input, imageOffset);
      imageSize = getImageSize(imageHeader[0]);
      imageOffset += imageHeader[1];
      result.images.push(imageSize);
    }
    return result;
  }
};

const J2C = {
  // TODO: this doesn't seem right. SIZ marker doesn't have to be right after the SOC
  validate: (input) => toHexString(input, 0, 4) === "ff4fff51",
  calculate: (input) => ({
    height: readUInt32BE(input, 12),
    width: readUInt32BE(input, 8)
  })
};

const JP2 = {
  validate(input) {
    if (readUInt32BE(input, 4) !== 1783636e3 || readUInt32BE(input, 0) < 1) return false;
    const ftypBox = findBox(input, "ftyp", 0);
    if (!ftypBox) return false;
    return readUInt32BE(input, ftypBox.offset + 4) === 1718909296;
  },
  calculate(input) {
    const jp2hBox = findBox(input, "jp2h", 0);
    const ihdrBox = jp2hBox && findBox(input, "ihdr", jp2hBox.offset + 8);
    if (ihdrBox) {
      return {
        height: readUInt32BE(input, ihdrBox.offset + 8),
        width: readUInt32BE(input, ihdrBox.offset + 12)
      };
    }
    throw new TypeError("Unsupported JPEG 2000 format");
  }
};

const EXIF_MARKER = "45786966";
const APP1_DATA_SIZE_BYTES = 2;
const EXIF_HEADER_BYTES = 6;
const TIFF_BYTE_ALIGN_BYTES = 2;
const BIG_ENDIAN_BYTE_ALIGN = "4d4d";
const LITTLE_ENDIAN_BYTE_ALIGN = "4949";
const IDF_ENTRY_BYTES = 12;
const NUM_DIRECTORY_ENTRIES_BYTES = 2;
function isEXIF(input) {
  return toHexString(input, 2, 6) === EXIF_MARKER;
}
function extractSize(input, index) {
  return {
    height: readUInt16BE(input, index),
    width: readUInt16BE(input, index + 2)
  };
}
function extractOrientation(exifBlock, isBigEndian) {
  const idfOffset = 8;
  const offset = EXIF_HEADER_BYTES + idfOffset;
  const idfDirectoryEntries = readUInt(exifBlock, 16, offset, isBigEndian);
  for (let directoryEntryNumber = 0; directoryEntryNumber < idfDirectoryEntries; directoryEntryNumber++) {
    const start = offset + NUM_DIRECTORY_ENTRIES_BYTES + directoryEntryNumber * IDF_ENTRY_BYTES;
    const end = start + IDF_ENTRY_BYTES;
    if (start > exifBlock.length) {
      return;
    }
    const block = exifBlock.slice(start, end);
    const tagNumber = readUInt(block, 16, 0, isBigEndian);
    if (tagNumber === 274) {
      const dataFormat = readUInt(block, 16, 2, isBigEndian);
      if (dataFormat !== 3) {
        return;
      }
      const numberOfComponents = readUInt(block, 32, 4, isBigEndian);
      if (numberOfComponents !== 1) {
        return;
      }
      return readUInt(block, 16, 8, isBigEndian);
    }
  }
}
function validateExifBlock(input, index) {
  const exifBlock = input.slice(APP1_DATA_SIZE_BYTES, index);
  const byteAlign = toHexString(
    exifBlock,
    EXIF_HEADER_BYTES,
    EXIF_HEADER_BYTES + TIFF_BYTE_ALIGN_BYTES
  );
  const isBigEndian = byteAlign === BIG_ENDIAN_BYTE_ALIGN;
  const isLittleEndian = byteAlign === LITTLE_ENDIAN_BYTE_ALIGN;
  if (isBigEndian || isLittleEndian) {
    return extractOrientation(exifBlock, isBigEndian);
  }
}
function validateInput(input, index) {
  if (index > input.length) {
    throw new TypeError("Corrupt JPG, exceeded buffer limits");
  }
}
const JPG = {
  validate: (input) => toHexString(input, 0, 2) === "ffd8",
  calculate(input) {
    input = input.slice(4);
    let orientation;
    let next;
    while (input.length) {
      const i = readUInt16BE(input, 0);
      if (input[i] !== 255) {
        input = input.slice(i);
        continue;
      }
      if (isEXIF(input)) {
        orientation = validateExifBlock(input, i);
      }
      validateInput(input, i);
      next = input[i + 1];
      if (next === 192 || next === 193 || next === 194) {
        const size = extractSize(input, i + 5);
        if (!orientation) {
          return size;
        }
        return {
          height: size.height,
          orientation,
          width: size.width
        };
      }
      input = input.slice(i + 2);
    }
    throw new TypeError("Invalid JPG, no size found");
  }
};

const KTX = {
  validate: (input) => {
    const signature = toUTF8String(input, 1, 7);
    return ["KTX 11", "KTX 20"].includes(signature);
  },
  calculate: (input) => {
    const type = input[5] === 49 ? "ktx" : "ktx2";
    const offset = type === "ktx" ? 36 : 20;
    return {
      height: readUInt32LE(input, offset + 4),
      width: readUInt32LE(input, offset),
      type
    };
  }
};

const pngSignature = "PNG\r\n\n";
const pngImageHeaderChunkName = "IHDR";
const pngFriedChunkName = "CgBI";
const PNG = {
  validate(input) {
    if (pngSignature === toUTF8String(input, 1, 8)) {
      let chunkName = toUTF8String(input, 12, 16);
      if (chunkName === pngFriedChunkName) {
        chunkName = toUTF8String(input, 28, 32);
      }
      if (chunkName !== pngImageHeaderChunkName) {
        throw new TypeError("Invalid PNG");
      }
      return true;
    }
    return false;
  },
  calculate(input) {
    if (toUTF8String(input, 12, 16) === pngFriedChunkName) {
      return {
        height: readUInt32BE(input, 36),
        width: readUInt32BE(input, 32)
      };
    }
    return {
      height: readUInt32BE(input, 20),
      width: readUInt32BE(input, 16)
    };
  }
};

const PNMTypes = {
  P1: "pbm/ascii",
  P2: "pgm/ascii",
  P3: "ppm/ascii",
  P4: "pbm",
  P5: "pgm",
  P6: "ppm",
  P7: "pam",
  PF: "pfm"
};
const handlers = {
  default: (lines) => {
    let dimensions = [];
    while (lines.length > 0) {
      const line = lines.shift();
      if (line[0] === "#") {
        continue;
      }
      dimensions = line.split(" ");
      break;
    }
    if (dimensions.length === 2) {
      return {
        height: parseInt(dimensions[1], 10),
        width: parseInt(dimensions[0], 10)
      };
    } else {
      throw new TypeError("Invalid PNM");
    }
  },
  pam: (lines) => {
    const size = {};
    while (lines.length > 0) {
      const line = lines.shift();
      if (line.length > 16 || line.charCodeAt(0) > 128) {
        continue;
      }
      const [key, value] = line.split(" ");
      if (key && value) {
        size[key.toLowerCase()] = parseInt(value, 10);
      }
      if (size.height && size.width) {
        break;
      }
    }
    if (size.height && size.width) {
      return {
        height: size.height,
        width: size.width
      };
    } else {
      throw new TypeError("Invalid PAM");
    }
  }
};
const PNM = {
  validate: (input) => toUTF8String(input, 0, 2) in PNMTypes,
  calculate(input) {
    const signature = toUTF8String(input, 0, 2);
    const type = PNMTypes[signature];
    const lines = toUTF8String(input, 3).split(/[\r\n]+/);
    const handler = handlers[type] || handlers.default;
    return handler(lines);
  }
};

const PSD = {
  validate: (input) => toUTF8String(input, 0, 4) === "8BPS",
  calculate: (input) => ({
    height: readUInt32BE(input, 14),
    width: readUInt32BE(input, 18)
  })
};

const svgReg = /<svg\s([^>"']|"[^"]*"|'[^']*')*>/;
const extractorRegExps = {
  height: /\sheight=(['"])([^%]+?)\1/,
  root: svgReg,
  viewbox: /\sviewBox=(['"])(.+?)\1/i,
  width: /\swidth=(['"])([^%]+?)\1/
};
const INCH_CM = 2.54;
const units = {
  in: 96,
  cm: 96 / INCH_CM,
  em: 16,
  ex: 8,
  m: 96 / INCH_CM * 100,
  mm: 96 / INCH_CM / 10,
  pc: 96 / 72 / 12,
  pt: 96 / 72,
  px: 1
};
const unitsReg = new RegExp(
  `^([0-9.]+(?:e\\d+)?)(${Object.keys(units).join("|")})?$`
);
function parseLength(len) {
  const m = unitsReg.exec(len);
  if (!m) {
    return void 0;
  }
  return Math.round(Number(m[1]) * (units[m[2]] || 1));
}
function parseViewbox(viewbox) {
  const bounds = viewbox.split(" ");
  return {
    height: parseLength(bounds[3]),
    width: parseLength(bounds[2])
  };
}
function parseAttributes(root) {
  const width = extractorRegExps.width.exec(root);
  const height = extractorRegExps.height.exec(root);
  const viewbox = extractorRegExps.viewbox.exec(root);
  return {
    height: height && parseLength(height[2]),
    viewbox: viewbox && parseViewbox(viewbox[2]),
    width: width && parseLength(width[2])
  };
}
function calculateByDimensions(attrs) {
  return {
    height: attrs.height,
    width: attrs.width
  };
}
function calculateByViewbox(attrs, viewbox) {
  const ratio = viewbox.width / viewbox.height;
  if (attrs.width) {
    return {
      height: Math.floor(attrs.width / ratio),
      width: attrs.width
    };
  }
  if (attrs.height) {
    return {
      height: attrs.height,
      width: Math.floor(attrs.height * ratio)
    };
  }
  return {
    height: viewbox.height,
    width: viewbox.width
  };
}
const SVG = {
  // Scan only the first kilo-byte to speed up the check on larger files
  validate: (input) => svgReg.test(toUTF8String(input, 0, 1e3)),
  calculate(input) {
    const root = extractorRegExps.root.exec(toUTF8String(input));
    if (root) {
      const attrs = parseAttributes(root[0]);
      if (attrs.width && attrs.height) {
        return calculateByDimensions(attrs);
      }
      if (attrs.viewbox) {
        return calculateByViewbox(attrs, attrs.viewbox);
      }
    }
    throw new TypeError("Invalid SVG");
  }
};

const TGA = {
  validate(input) {
    return readUInt16LE(input, 0) === 0 && readUInt16LE(input, 4) === 0;
  },
  calculate(input) {
    return {
      height: readUInt16LE(input, 14),
      width: readUInt16LE(input, 12)
    };
  }
};

function readIFD(input, isBigEndian) {
  const ifdOffset = readUInt(input, 32, 4, isBigEndian);
  return input.slice(ifdOffset + 2);
}
function readValue(input, isBigEndian) {
  const low = readUInt(input, 16, 8, isBigEndian);
  const high = readUInt(input, 16, 10, isBigEndian);
  return (high << 16) + low;
}
function nextTag(input) {
  if (input.length > 24) {
    return input.slice(12);
  }
}
function extractTags(input, isBigEndian) {
  const tags = {};
  let temp = input;
  while (temp && temp.length) {
    const code = readUInt(temp, 16, 0, isBigEndian);
    const type = readUInt(temp, 16, 2, isBigEndian);
    const length = readUInt(temp, 32, 4, isBigEndian);
    if (code === 0) {
      break;
    } else {
      if (length === 1 && (type === 3 || type === 4)) {
        tags[code] = readValue(temp, isBigEndian);
      }
      temp = nextTag(temp);
    }
  }
  return tags;
}
function determineEndianness(input) {
  const signature = toUTF8String(input, 0, 2);
  if ("II" === signature) {
    return "LE";
  } else if ("MM" === signature) {
    return "BE";
  }
}
const signatures = [
  // '492049', // currently not supported
  "49492a00",
  // Little endian
  "4d4d002a"
  // Big Endian
  // '4d4d002a', // BigTIFF > 4GB. currently not supported
];
const TIFF = {
  validate: (input) => signatures.includes(toHexString(input, 0, 4)),
  calculate(input) {
    const isBigEndian = determineEndianness(input) === "BE";
    const ifdBuffer = readIFD(input, isBigEndian);
    const tags = extractTags(ifdBuffer, isBigEndian);
    const width = tags[256];
    const height = tags[257];
    if (!width || !height) {
      throw new TypeError("Invalid Tiff. Missing tags");
    }
    return { height, width };
  }
};

function calculateExtended(input) {
  return {
    height: 1 + readUInt24LE(input, 7),
    width: 1 + readUInt24LE(input, 4)
  };
}
function calculateLossless(input) {
  return {
    height: 1 + ((input[4] & 15) << 10 | input[3] << 2 | (input[2] & 192) >> 6),
    width: 1 + ((input[2] & 63) << 8 | input[1])
  };
}
function calculateLossy(input) {
  return {
    height: readInt16LE(input, 8) & 16383,
    width: readInt16LE(input, 6) & 16383
  };
}
const WEBP = {
  validate(input) {
    const riffHeader = "RIFF" === toUTF8String(input, 0, 4);
    const webpHeader = "WEBP" === toUTF8String(input, 8, 12);
    const vp8Header = "VP8" === toUTF8String(input, 12, 15);
    return riffHeader && webpHeader && vp8Header;
  },
  calculate(input) {
    const chunkHeader = toUTF8String(input, 12, 16);
    input = input.slice(20, 30);
    if (chunkHeader === "VP8X") {
      const extendedHeader = input[0];
      const validStart = (extendedHeader & 192) === 0;
      const validEnd = (extendedHeader & 1) === 0;
      if (validStart && validEnd) {
        return calculateExtended(input);
      } else {
        throw new TypeError("Invalid WebP");
      }
    }
    if (chunkHeader === "VP8 " && input[0] !== 47) {
      return calculateLossy(input);
    }
    const signature = toHexString(input, 3, 6);
    if (chunkHeader === "VP8L" && signature !== "9d012a") {
      return calculateLossless(input);
    }
    throw new TypeError("Invalid WebP");
  }
};

const typeHandlers = /* @__PURE__ */ new Map([
  ["bmp", BMP],
  ["cur", CUR],
  ["dds", DDS],
  ["gif", GIF],
  ["heif", HEIF],
  ["icns", ICNS],
  ["ico", ICO],
  ["j2c", J2C],
  ["jp2", JP2],
  ["jpg", JPG],
  ["ktx", KTX],
  ["png", PNG],
  ["pnm", PNM],
  ["psd", PSD],
  ["svg", SVG],
  ["tga", TGA],
  ["tiff", TIFF],
  ["webp", WEBP]
]);
const types = Array.from(typeHandlers.keys());

const firstBytes = /* @__PURE__ */ new Map([
  [56, "psd"],
  [66, "bmp"],
  [68, "dds"],
  [71, "gif"],
  [73, "tiff"],
  [77, "tiff"],
  [82, "webp"],
  [105, "icns"],
  [137, "png"],
  [255, "jpg"]
]);
function detector(input) {
  const byte = input[0];
  const type = firstBytes.get(byte);
  if (type && typeHandlers.get(type).validate(input)) {
    return type;
  }
  return types.find((fileType) => typeHandlers.get(fileType).validate(input));
}

const globalOptions = {
  disabledTypes: []
};
function lookup(input) {
  const type = detector(input);
  if (typeof type !== "undefined") {
    if (globalOptions.disabledTypes.includes(type)) {
      throw new TypeError("disabled file type: " + type);
    }
    const size = typeHandlers.get(type).calculate(input);
    if (size !== void 0) {
      size.type = size.type ?? type;
      return size;
    }
  }
  throw new TypeError("unsupported file type: " + type);
}

async function imageMetadata(data, src) {
  try {
    const result = lookup(data);
    if (!result.height || !result.width || !result.type) {
      throw new AstroError({
        ...NoImageMetadata,
        message: NoImageMetadata.message(src)
      });
    }
    const { width, height, type, orientation } = result;
    const isPortrait = (orientation || 0) >= 5;
    return {
      width: isPortrait ? height : width,
      height: isPortrait ? width : height,
      format: type,
      orientation
    };
  } catch {
    throw new AstroError({
      ...NoImageMetadata,
      message: NoImageMetadata.message(src)
    });
  }
}

async function inferRemoteSize(url) {
  const response = await fetch(url);
  if (!response.body || !response.ok) {
    throw new AstroError({
      ...FailedToFetchRemoteImageDimensions,
      message: FailedToFetchRemoteImageDimensions.message(url)
    });
  }
  const reader = response.body.getReader();
  let done, value;
  let accumulatedChunks = new Uint8Array();
  while (!done) {
    const readResult = await reader.read();
    done = readResult.done;
    if (done) break;
    if (readResult.value) {
      value = readResult.value;
      let tmp = new Uint8Array(accumulatedChunks.length + value.length);
      tmp.set(accumulatedChunks, 0);
      tmp.set(value, accumulatedChunks.length);
      accumulatedChunks = tmp;
      try {
        const dimensions = await imageMetadata(accumulatedChunks, url);
        if (dimensions) {
          await reader.cancel();
          return dimensions;
        }
      } catch {
      }
    }
  }
  throw new AstroError({
    ...NoImageMetadata,
    message: NoImageMetadata.message(url)
  });
}

async function getConfiguredImageService() {
  if (!globalThis?.astroAsset?.imageService) {
    const { default: service } = await import(
      // @ts-expect-error
      '../chunks/astro/assets-service_DIMzS0Of.mjs'
    ).then(n => n.s).catch((e) => {
      const error = new AstroError(InvalidImageService);
      error.cause = e;
      throw error;
    });
    if (!globalThis.astroAsset) globalThis.astroAsset = {};
    globalThis.astroAsset.imageService = service;
    return service;
  }
  return globalThis.astroAsset.imageService;
}
async function getImage$1(options, imageConfig) {
  if (!options || typeof options !== "object") {
    throw new AstroError({
      ...ExpectedImageOptions,
      message: ExpectedImageOptions.message(JSON.stringify(options))
    });
  }
  if (typeof options.src === "undefined") {
    throw new AstroError({
      ...ExpectedImage,
      message: ExpectedImage.message(
        options.src,
        "undefined",
        JSON.stringify(options)
      )
    });
  }
  if (isImageMetadata(options)) {
    throw new AstroError(ExpectedNotESMImage);
  }
  const service = await getConfiguredImageService();
  const resolvedOptions = {
    ...options,
    src: await resolveSrc(options.src)
  };
  if (options.inferSize && isRemoteImage(resolvedOptions.src) && isRemotePath(resolvedOptions.src)) {
    const result = await inferRemoteSize(resolvedOptions.src);
    resolvedOptions.width ??= result.width;
    resolvedOptions.height ??= result.height;
    delete resolvedOptions.inferSize;
  }
  const originalFilePath = isESMImportedImage(resolvedOptions.src) ? resolvedOptions.src.fsPath : void 0;
  const clonedSrc = isESMImportedImage(resolvedOptions.src) ? (
    // @ts-expect-error - clone is a private, hidden prop
    resolvedOptions.src.clone ?? resolvedOptions.src
  ) : resolvedOptions.src;
  resolvedOptions.src = clonedSrc;
  const validatedOptions = service.validateOptions ? await service.validateOptions(resolvedOptions, imageConfig) : resolvedOptions;
  const srcSetTransforms = service.getSrcSet ? await service.getSrcSet(validatedOptions, imageConfig) : [];
  let imageURL = await service.getURL(validatedOptions, imageConfig);
  let srcSets = await Promise.all(
    srcSetTransforms.map(async (srcSet) => ({
      transform: srcSet.transform,
      url: await service.getURL(srcSet.transform, imageConfig),
      descriptor: srcSet.descriptor,
      attributes: srcSet.attributes
    }))
  );
  if (isLocalService(service) && globalThis.astroAsset.addStaticImage && !(isRemoteImage(validatedOptions.src) && imageURL === validatedOptions.src)) {
    const propsToHash = service.propertiesToHash ?? DEFAULT_HASH_PROPS;
    imageURL = globalThis.astroAsset.addStaticImage(
      validatedOptions,
      propsToHash,
      originalFilePath
    );
    srcSets = srcSetTransforms.map((srcSet) => ({
      transform: srcSet.transform,
      url: globalThis.astroAsset.addStaticImage(srcSet.transform, propsToHash, originalFilePath),
      descriptor: srcSet.descriptor,
      attributes: srcSet.attributes
    }));
  }
  return {
    rawOptions: resolvedOptions,
    options: validatedOptions,
    src: imageURL,
    srcSet: {
      values: srcSets,
      attribute: srcSets.map((srcSet) => `${srcSet.url} ${srcSet.descriptor}`).join(", ")
    },
    attributes: service.getHTMLAttributes !== void 0 ? await service.getHTMLAttributes(validatedOptions, imageConfig) : {}
  };
}

const $$Astro$1 = createAstro("https://pvb.com");
const $$Image = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$1, $$props, $$slots);
  Astro2.self = $$Image;
  const props = Astro2.props;
  if (props.alt === void 0 || props.alt === null) {
    throw new AstroError(ImageMissingAlt);
  }
  if (typeof props.width === "string") {
    props.width = parseInt(props.width);
  }
  if (typeof props.height === "string") {
    props.height = parseInt(props.height);
  }
  const image = await getImage(props);
  const additionalAttributes = {};
  if (image.srcSet.values.length > 0) {
    additionalAttributes.srcset = image.srcSet.attribute;
  }
  return renderTemplate`${maybeRenderHead()}<img${addAttribute(image.src, "src")}${spreadAttributes(additionalAttributes)}${spreadAttributes(image.attributes)}>`;
}, "C:/Users/<USER>/Desktop/pvb-astro/node_modules/astro/components/Image.astro", void 0);

const $$Astro = createAstro("https://pvb.com");
const $$Picture = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Picture;
  const defaultFormats = ["webp"];
  const defaultFallbackFormat = "png";
  const specialFormatsFallback = ["gif", "svg", "jpg", "jpeg"];
  const { formats = defaultFormats, pictureAttributes = {}, fallbackFormat, ...props } = Astro2.props;
  if (props.alt === void 0 || props.alt === null) {
    throw new AstroError(ImageMissingAlt);
  }
  const scopedStyleClass = props.class?.match(/\bastro-\w{8}\b/)?.[0];
  if (scopedStyleClass) {
    if (pictureAttributes.class) {
      pictureAttributes.class = `${pictureAttributes.class} ${scopedStyleClass}`;
    } else {
      pictureAttributes.class = scopedStyleClass;
    }
  }
  for (const key in props) {
    if (key.startsWith("data-astro-cid")) {
      pictureAttributes[key] = props[key];
    }
  }
  const originalSrc = await resolveSrc(props.src);
  const optimizedImages = await Promise.all(
    formats.map(
      async (format) => await getImage({
        ...props,
        src: originalSrc,
        format,
        widths: props.widths,
        densities: props.densities
      })
    )
  );
  let resultFallbackFormat = fallbackFormat ?? defaultFallbackFormat;
  if (!fallbackFormat && isESMImportedImage(originalSrc) && specialFormatsFallback.includes(originalSrc.format)) {
    resultFallbackFormat = originalSrc.format;
  }
  const fallbackImage = await getImage({
    ...props,
    format: resultFallbackFormat,
    widths: props.widths,
    densities: props.densities
  });
  const imgAdditionalAttributes = {};
  const sourceAdditionalAttributes = {};
  if (props.sizes) {
    sourceAdditionalAttributes.sizes = props.sizes;
  }
  if (fallbackImage.srcSet.values.length > 0) {
    imgAdditionalAttributes.srcset = fallbackImage.srcSet.attribute;
  }
  return renderTemplate`${maybeRenderHead()}<picture${spreadAttributes(pictureAttributes)}> ${Object.entries(optimizedImages).map(([_, image]) => {
    const srcsetAttribute = props.densities || !props.densities && !props.widths ? `${image.src}${image.srcSet.values.length > 0 ? ", " + image.srcSet.attribute : ""}` : image.srcSet.attribute;
    return renderTemplate`<source${addAttribute(srcsetAttribute, "srcset")}${addAttribute(mime.lookup(image.options.format ?? image.src) ?? `image/${image.options.format}`, "type")}${spreadAttributes(sourceAdditionalAttributes)}>`;
  })} <img${addAttribute(fallbackImage.src, "src")}${spreadAttributes(imgAdditionalAttributes)}${spreadAttributes(fallbackImage.attributes)}> </picture>`;
}, "C:/Users/<USER>/Desktop/pvb-astro/node_modules/astro/components/Picture.astro", void 0);

const imageConfig = {"service":{"entrypoint":"astro/assets/services/sharp","config":{}},"domains":[],"remotePatterns":[]};
					const getImage = async (options) => await getImage$1(options, imageConfig);

const fnv1a52 = (str) => {
  const len = str.length;
  let i = 0, t0 = 0, v0 = 8997, t1 = 0, v1 = 33826, t2 = 0, v2 = 40164, t3 = 0, v3 = 52210;
  while (i < len) {
    v0 ^= str.charCodeAt(i++);
    t0 = v0 * 435;
    t1 = v1 * 435;
    t2 = v2 * 435;
    t3 = v3 * 435;
    t2 += v0 << 8;
    t3 += v1 << 8;
    t1 += t0 >>> 16;
    v0 = t0 & 65535;
    t2 += t1 >>> 16;
    v1 = t1 & 65535;
    v3 = t3 + (t2 >>> 16) & 65535;
    v2 = t2 & 65535;
  }
  return (v3 & 15) * 281474976710656 + v2 * 4294967296 + v1 * 65536 + (v0 ^ v3 >> 4);
};
const etag = (payload, weak = false) => {
  const prefix = weak ? 'W/"' : '"';
  return prefix + fnv1a52(payload).toString(36) + payload.length.toString(36) + '"';
};

async function loadRemoteImage(src, headers) {
  try {
    const res = await fetch(src, {
      // Forward all headers from the original request
      headers
    });
    if (!res.ok) {
      return void 0;
    }
    return await res.arrayBuffer();
  } catch {
    return void 0;
  }
}
const GET = async ({ request }) => {
  try {
    const imageService = await getConfiguredImageService();
    if (!("transform" in imageService)) {
      throw new Error("Configured image service is not a local service");
    }
    const url = new URL(request.url);
    const transform = await imageService.parseURL(url, imageConfig);
    if (!transform?.src) {
      throw new Error("Incorrect transform returned by `parseURL`");
    }
    let inputBuffer = void 0;
    const isRemoteImage = isRemotePath(transform.src);
    const sourceUrl = isRemoteImage ? new URL(transform.src) : new URL(transform.src, url.origin);
    if (isRemoteImage && isRemoteAllowed(transform.src, imageConfig) === false) {
      return new Response("Forbidden", { status: 403 });
    }
    inputBuffer = await loadRemoteImage(sourceUrl, isRemoteImage ? new Headers() : request.headers);
    if (!inputBuffer) {
      return new Response("Not Found", { status: 404 });
    }
    const { data, format } = await imageService.transform(
      new Uint8Array(inputBuffer),
      transform,
      imageConfig
    );
    return new Response(data, {
      status: 200,
      headers: {
        "Content-Type": mime.lookup(format) ?? `image/${format}`,
        "Cache-Control": "public, max-age=31536000",
        ETag: etag(data.toString()),
        Date: (/* @__PURE__ */ new Date()).toUTCString()
      }
    });
  } catch (err) {
    console.error("Could not process image request:", err);
    return new Response(`Server Error: ${err}`, { status: 500 });
  }
};

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  GET
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
</file>

<file path=".netlify/build/pages/about.astro.mjs">
import { a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../chunks/astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import { $ as $$Layout } from '../chunks/Layout_rXbp99fE.mjs';
/* empty css                                 */
export { renderers } from '../renderers.mjs';

var __freeze = Object.freeze;
var __defProp = Object.defineProperty;
var __template = (cooked, raw) => __freeze(__defProp(cooked, "raw", { value: __freeze(cooked.slice()) }));
var _a;
const $$About = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "pageTitle": "About | PVB", "isHomePage": false, "accentColor": "#3a2c23", "bgColor": "#3a2c23", "backgroundImageUrl": "/images/about.png", "bodyDataPage": "about", "data-astro-cid-kh7btl4r": true }, { "default": ($$result2) => renderTemplate(_a || (_a = __template([" ", `<div class="about-header" data-astro-cid-kh7btl4r> <div class="about-title" data-astro-cid-kh7btl4r>about</div> </div> <div class="content-container" data-astro-cid-kh7btl4r> <section class="about-section intro-section" data-astro-cid-kh7btl4r> <p data-astro-cid-kh7btl4r>This is the main introductory text about me. Keep it concise and engaging. Briefly touch upon what drives you or the purpose of this site.</p> <p data-astro-cid-kh7btl4r>This demonstrates how the components can be reused with different properties to change the appearance and behavior.</p> </section> <section class="about-section detail-section" data-astro-cid-kh7btl4r> <h2 data-astro-cid-kh7btl4r>My Journey</h2> <p data-astro-cid-kh7btl4r>Expand on your background, key experiences, and the path that led you here. Use storytelling elements if appropriate.</p> </section> <section class="about-section detail-section" data-astro-cid-kh7btl4r> <h2 data-astro-cid-kh7btl4r>Skills & Expertise</h2> <p data-astro-cid-kh7btl4r>List or describe your core skills, tools you master, and areas you specialize in.</p> <ul data-astro-cid-kh7btl4r> <li data-astro-cid-kh7btl4r>Skill/Area 1: Brief description.</li> <li data-astro-cid-kh7btl4r>Skill/Area 2: Brief description.</li> <li data-astro-cid-kh7btl4r>Skill/Area 3: Brief description.</li> </ul> </section> <section class="about-section detail-section" data-astro-cid-kh7btl4r> <h2 data-astro-cid-kh7btl4r>Philosophy</h2> <p data-astro-cid-kh7btl4r>Discuss your approach to work, design principles, or core values that guide you.</p> </section> </div> <script>
      // Set up back button event
      document.addEventListener('DOMContentLoaded', function() {
        const backButton = document.querySelector('.nav-circle.top-left');
        if (backButton) {
          backButton.addEventListener('click', () => {
            window.history.back();
          });
        }
      });
    <\/script> `])), maybeRenderHead()) })} `;
}, "C:/Users/<USER>/Desktop/pvb-astro/src/pages/about.astro", void 0);

const $$file = "C:/Users/<USER>/Desktop/pvb-astro/src/pages/about.astro";
const $$url = "/about";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
    __proto__: null,
    default: $$About,
    file: $$file,
    url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
</file>

<file path=".netlify/build/pages/api/random-quote.json.astro.mjs">
import { g as getCollection } from '../../chunks/_astro_content_CcaQj6Wl.mjs';
export { renderers } from '../../renderers.mjs';

// Fallback quotes in case the collection fails
const fallbackQuotes = [
  {
    text: "Many mistake stability for safety, but only the dead remain still.",
    author: "Pruthvi Bhat",
    linkedPage: "/blog/stability-vs-safety",
    cardTitle: "Core Philosophy",
    cardSubtitle: "Exploring the need for change"
  },
  {
    text: "The purpose of knowledge is action, not more knowledge.",
    author: "Pruthvi Bhat",
    linkedPage: "/blog/knowledge-and-action",
    cardTitle: "Applied Learning",
    cardSubtitle: "Insights into meaningful action"
  },
  {
    text: "Silence is not empty, it's full of answers.",
    author: "Unknown",
    linkedPage: "/blog/power-of-silence",
    cardTitle: "Mindfulness",
    cardSubtitle: "Finding clarity in quiet"
  }
];

const prerender = false;

async function GET({ request }) {
  try {
    // Get all quotes from the collection
    const allQuotes = await getCollection('quotes');

    // If we have quotes, select a random one
    if (allQuotes && allQuotes.length > 0) {
      const randomIndex = Math.floor(Math.random() * allQuotes.length);
      const randomQuote = allQuotes[randomIndex];

      return new Response(
        JSON.stringify({
          text: randomQuote.data.text,
          author: randomQuote.data.author,
          linkedPage: randomQuote.data.linkedPage || null,
          cardTitle: randomQuote.data.cardTitle,
          cardSubtitle: randomQuote.data.cardSubtitle
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
    }

    // If no quotes found, use fallback
    console.warn('No quotes found in collection, using fallback');
    const randomIndex = Math.floor(Math.random() * fallbackQuotes.length);
    return new Response(
      JSON.stringify(fallbackQuotes[randomIndex]),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  } catch (error) {
    console.error('Error fetching quotes:', error);

    // Return a fallback quote
    const randomIndex = Math.floor(Math.random() * fallbackQuotes.length);
    return new Response(
      JSON.stringify(fallbackQuotes[randomIndex]),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  GET,
  prerender
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
</file>

<file path=".netlify/build/pages/blog.astro.mjs">
import { a as createComponent, d as renderComponent, r as renderTemplate, b as addAttribute, m as maybeRenderHead } from '../chunks/astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import { g as getCollection } from '../chunks/_astro_content_CcaQj6Wl.mjs';
import { $ as $$Layout } from '../chunks/Layout_rXbp99fE.mjs';
/* empty css                                */
export { renderers } from '../renderers.mjs';

var __freeze = Object.freeze;
var __defProp = Object.defineProperty;
var __template = (cooked, raw) => __freeze(__defProp(cooked, "raw", { value: __freeze(cooked.slice()) }));
var _a;
const $$Blog = createComponent(async ($$result, $$props, $$slots) => {
  let sortedPosts = [];
  let mostRecentPost;
  let allTags = [];
  try {
    const posts = await getCollection("blog");
    const nonDraftPosts = posts.filter(({ data }) => !data.draft);
    sortedPosts = [...nonDraftPosts].sort(
      (a, b) => Math.floor(new Date(b.data.pubDatetime).getTime() / 1e3) - Math.floor(new Date(a.data.pubDatetime).getTime() / 1e3)
    );
    mostRecentPost = sortedPosts[0];
    allTags = [...new Set(nonDraftPosts.flatMap((post) => post.data.tags || []))].sort();
  } catch (e) {
    console.error("Error loading blog posts:", e);
    allTags = ["philosophy", "wisdom", "mastery", "AI", "synthesis", "innovation"];
  }
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "pageTitle": "Blog | PVB", "isHomePage": false, "accentColor": "#f0f0f0", "bgColor": "rgba(10, 10, 10, 0.94)", "backgroundImageUrl": "/images/blackgranite.png", "bodyDataPage": "blog", "data-astro-cid-ijnerlr2": true }, { "default": async ($$result2) => renderTemplate(_a || (_a = __template(["  ", '<div class="blog-header" data-astro-cid-ijnerlr2> <div class="blog-title" data-astro-cid-ijnerlr2>blog</div> </div>  <div class="page-container" data-astro-cid-ijnerlr2> <!-- Left Sidebar --> <div class="blog-sidebar" data-astro-cid-ijnerlr2> <!-- Search Bar --> <div class="search-container sidebar-section" data-astro-cid-ijnerlr2> <a href="/search" class="search-link" data-astro-cid-ijnerlr2> <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-astro-cid-ijnerlr2> <circle cx="11" cy="11" r="8" data-astro-cid-ijnerlr2></circle> <line x1="21" y1="21" x2="16.65" y2="16.65" data-astro-cid-ijnerlr2></line> </svg> <span data-astro-cid-ijnerlr2>search</span> </a> </div> <!-- Archive Button --> <div class="archive-container sidebar-section" data-astro-cid-ijnerlr2> <a href="/blog/timeline" class="archive-link" data-astro-cid-ijnerlr2> <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-astro-cid-ijnerlr2> <path d="M3 3v18h18" data-astro-cid-ijnerlr2></path> <path d="M7 17l4-4 4 4 4-4" data-astro-cid-ijnerlr2></path> <path d="M7 11l4-4 4 4 4-4" data-astro-cid-ijnerlr2></path> </svg> <span data-astro-cid-ijnerlr2>archive</span> </a> </div> <!-- Subscribe --> <div class="subscribe-container sidebar-section" data-astro-cid-ijnerlr2> <a href="#" class="subscribe-link" data-astro-cid-ijnerlr2>subscribe by email</a> </div> <!-- Tags Filter (Collapsible) --> <div class="tags-container sidebar-section" data-astro-cid-ijnerlr2> <button class="tags-toggle" id="tags-toggle" aria-expanded="false" aria-controls="tags-list" data-astro-cid-ijnerlr2> <span class="tags-title" data-astro-cid-ijnerlr2> <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-astro-cid-ijnerlr2> <path d="M9 5H2v7l6.29 6.29c.94.94 2.48.94 3.42 0l7.58-7.58c.94-.94.94-2.48 0-3.42L13 2c-.94-.94-2.48-.94-3.42 0L9 5Z" data-astro-cid-ijnerlr2></path> <path d="M6 9.01V9" data-astro-cid-ijnerlr2></path> </svg> <span data-astro-cid-ijnerlr2>tags</span> </span> <span class="toggle-icon" data-astro-cid-ijnerlr2>+</span> </button> <div class="tags-list" id="tags-list" hidden data-astro-cid-ijnerlr2> <a href="/tags" class="tag-link all-tags-link" data-astro-cid-ijnerlr2>all tags</a> ', ' </div> </div> </div> <!-- Main Content --> <div class="blog-content" data-astro-cid-ijnerlr2> <!-- Recent Post Preview --> ', ' <!-- Dividing Line --> <div class="posts-divider" data-astro-cid-ijnerlr2></div> <!-- More Posts (without header) --> <div class="more-posts" data-astro-cid-ijnerlr2> ', ` </div> </div> </div> <script>
    // Make the bottom button slightly transparent on scroll
    document.addEventListener('DOMContentLoaded', function() {
      const bottomButton = document.querySelector('.nav-circle.bottom-center');

      window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
          bottomButton.style.opacity = "0.7";
        } else {
          bottomButton.style.opacity = "1";
        }
      });

      // Fix for navigation button - Attach event to the navigation button
      const backButton = document.querySelector('.nav-circle.top-left');
      if (backButton) {
        backButton.addEventListener('click', () => {
          window.history.back();
        });
      }

      // Toggle tags list
      const tagsToggle = document.getElementById('tags-toggle');
      const tagsList = document.getElementById('tags-list');

      if (tagsToggle && tagsList) {
        // Function to close tags list
        const closeTagsList = () => {
          tagsToggle.setAttribute('aria-expanded', 'false');
          tagsList.setAttribute('hidden', '');
        };

        // Toggle tags list when clicking the toggle button
        tagsToggle.addEventListener('click', () => {
          const expanded = tagsToggle.getAttribute('aria-expanded') === 'true';
          tagsToggle.setAttribute('aria-expanded', !expanded);

          if (expanded) {
            tagsList.setAttribute('hidden', '');
          } else {
            tagsList.removeAttribute('hidden');
          }
        });

        // Close tags list when clicking outside on mobile
        if (window.innerWidth <= 768) {
          document.addEventListener('click', (e) => {
            if (!tagsList.hasAttribute('hidden') &&
                !tagsToggle.contains(e.target) &&
                !tagsList.contains(e.target)) {
              closeTagsList();
            }
          });

          // Close tags list when pressing escape
          document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && !tagsList.hasAttribute('hidden')) {
              closeTagsList();
            }
          });
        }
      }
    });
  <\/script> `])), maybeRenderHead(), allTags.map((tag) => renderTemplate`<a${addAttribute(`/blog/tag/${tag}`, "href")} class="tag-link" data-astro-cid-ijnerlr2>${tag}</a>`), mostRecentPost && renderTemplate`<div class="recent-post" data-astro-cid-ijnerlr2> <h2 class="post-title" data-astro-cid-ijnerlr2> <a${addAttribute(`/blog/${mostRecentPost.slug}`, "href")} data-astro-cid-ijnerlr2>${mostRecentPost.data.title}</a> </h2> <div class="post-date" data-astro-cid-ijnerlr2>${new Date(mostRecentPost.data.pubDatetime).toLocaleDateString("en-US", { day: "numeric", month: "long", year: "numeric" })}</div> <div class="post-preview" data-astro-cid-ijnerlr2> <p class="post-description" data-astro-cid-ijnerlr2>${mostRecentPost.data.description}</p> <a${addAttribute(`/blog/${mostRecentPost.slug}`, "href")} class="read-more" data-astro-cid-ijnerlr2>
read <span class="read-more-arrow" data-astro-cid-ijnerlr2>→</span> </a> </div> </div>`, sortedPosts.slice(1).map((post) => renderTemplate`<div class="post-item" data-astro-cid-ijnerlr2> <h3 class="post-item-title" data-astro-cid-ijnerlr2> <a${addAttribute(`/blog/${post.slug}`, "href")} data-astro-cid-ijnerlr2>${post.data.title}</a> </h3> <div class="post-item-date" data-astro-cid-ijnerlr2> ${new Date(post.data.pubDatetime).toLocaleDateString("en-US", { day: "numeric", month: "long", year: "numeric" })} </div> <p class="post-item-description" data-astro-cid-ijnerlr2>${post.data.description}</p> <a${addAttribute(`/blog/${post.slug}`, "href")} class="post-item-read-more" data-astro-cid-ijnerlr2>read</a> </div>`)) })} `;
}, "C:/Users/<USER>/Desktop/pvb-astro/src/pages/blog.astro", void 0);

const $$file = "C:/Users/<USER>/Desktop/pvb-astro/src/pages/blog.astro";
const $$url = "/blog";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Blog,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
</file>

<file path=".netlify/build/pages/blog/_slug_.astro.mjs">
import { c as createAstro, a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../../chunks/astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import { g as getCollection } from '../../chunks/_astro_content_CcaQj6Wl.mjs';
import { $ as $$BlogPost } from '../../chunks/BlogPost_DYNjvq7J.mjs';
import { $ as $$Layout } from '../../chunks/Layout_rXbp99fE.mjs';
export { renderers } from '../../renderers.mjs';

const $$Astro = createAstro("https://pvb.com");
async function getStaticPaths() {
  try {
    const posts = await getCollection("blog", ({ data }) => !data.draft);
    if (posts && posts.length > 0) {
      return posts.map((post) => ({
        params: { slug: post.slug },
        props: { post }
      }));
    }
    console.warn("No blog posts found, using fallback");
    return [
      {
        params: { slug: "hello-world" },
        props: { error: "No blog posts found" }
      }
    ];
  } catch (error) {
    console.error("Error getting blog posts for static paths:", error);
    return [
      {
        params: { slug: "hello-world" },
        props: { error: "Failed to load blog posts" }
      }
    ];
  }
}
const $$slug = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$slug;
  const { post, error } = Astro2.props;
  let Content;
  let errorState = !!error;
  if (!errorState && post) {
    try {
      const renderResult = await post.render();
      Content = renderResult.Content;
    } catch (e) {
      console.error("Error rendering post:", e);
      errorState = true;
    }
  }
  return renderTemplate`${errorState ? renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "pageTitle": "Blog Post Not Found", "isHomePage": false, "accentColor": "#f0f0f0", "bgColor": "rgba(20, 20, 20, 0.9)", "backgroundImageUrl": "/images/blackgranite.png", "bodyDataPage": "blog-post" }, { "default": async ($$result2) => renderTemplate`${maybeRenderHead()}<div class="error-container"><h1>Blog Post Not Found</h1><p>Sorry, the blog post you're looking for is not available.</p><a href="/blog" class="return-link">Return to Blog</a></div><style>
      .error-container {
        max-width: 650px;
        margin: 130px auto 100px;
        padding: 0 20px;
        text-align: center;
      }

      h1 {
        font-size: 2rem;
        margin-bottom: 1rem;
        font-family: 'Georgia Custom', Georgia, serif;
        color: rgba(240, 240, 240, 0.9);
      }

      p {
        font-size: 1.1rem;
        margin-bottom: 2rem;
        font-family: 'Georgia Custom', Georgia, serif;
        color: rgba(240, 240, 240, 0.8);
      }

      .return-link {
        display: inline-block;
        padding: 0.5rem 1rem;
        background-color: #e0e0e0;
        color: #222222;
        text-decoration: none;
        border-radius: 4px;
        font-family: 'Georgia Custom', Georgia, serif;
        transition: background-color 0.3s ease;
      }

      .return-link:hover {
        background-color: #cccccc;
      }
    </style>` })}` : renderTemplate`${renderComponent($$result, "BlogPost", $$BlogPost, { "post": post }, { "default": async ($$result2) => renderTemplate`${renderComponent($$result2, "Content", Content, {})}` })}`}`;
}, "C:/Users/<USER>/Desktop/pvb-astro/src/pages/blog/[slug].astro", void 0);

const $$file = "C:/Users/<USER>/Desktop/pvb-astro/src/pages/blog/[slug].astro";
const $$url = "/blog/[slug]";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$slug,
  file: $$file,
  getStaticPaths,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
</file>

<file path=".netlify/build/pages/blog/tag/_tag_.astro.mjs">
import { c as createAstro, a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead, b as addAttribute } from '../../../chunks/astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import { g as getCollection } from '../../../chunks/_astro_content_CcaQj6Wl.mjs';
import { $ as $$Layout } from '../../../chunks/Layout_rXbp99fE.mjs';
/* empty css                                       */
export { renderers } from '../../../renderers.mjs';

const $$Astro = createAstro("https://pvb.com");
async function getStaticPaths() {
  try {
    const allPosts = await getCollection("blog", ({ data }) => !data.draft);
    const allTags = [...new Set(allPosts.flatMap((post) => post.data.tags || []))];
    const tags = allTags.length > 0 ? allTags : [
      "philosophy",
      "wisdom",
      "mastery",
      "AI",
      "synthesis",
      "innovation",
      "strategy",
      "introduction",
      "blogging"
    ];
    return tags.map((tag) => ({
      params: { tag },
      props: { tag }
    }));
  } catch (error) {
    console.error("Error in getStaticPaths:", error);
    const fallbackTags = [
      "philosophy",
      "wisdom",
      "mastery",
      "AI",
      "synthesis",
      "innovation",
      "strategy",
      "introduction",
      "blogging"
    ];
    return fallbackTags.map((tag) => ({
      params: { tag },
      props: { tag, errorInStaticPaths: true }
    }));
  }
}
const $$tag = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$tag;
  const { tag } = Astro2.props;
  let posts = [];
  let allTags = [];
  let error = false;
  try {
    const allPosts = await getCollection("blog");
    const nonDraftPosts = allPosts.filter((post) => !post.data.draft);
    allTags = [...new Set(nonDraftPosts.flatMap((post) => post.data.tags))].sort();
    posts = nonDraftPosts.filter((post) => post.data.tags.includes(tag));
    posts = posts.sort((a, b) => {
      const dateA = new Date(a.data.pubDatetime).getTime();
      const dateB = new Date(b.data.pubDatetime).getTime();
      return dateB - dateA;
    });
  } catch (e) {
    console.error("Error fetching posts:", e);
    error = true;
  }
  return renderTemplate`${error ? renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "pageTitle": "Tag Error | Blog | PVB", "isHomePage": false, "accentColor": "#f0f0f0", "bgColor": "rgba(10, 10, 10, 0.94)", "backgroundImageUrl": "/images/blackgranite.png", "bodyDataPage": "blog", "data-astro-cid-trjsnkp3": true }, { "default": async ($$result2) => renderTemplate`${maybeRenderHead()}<div class="error-container" data-astro-cid-trjsnkp3><h1 data-astro-cid-trjsnkp3>Error Loading Tag</h1><p data-astro-cid-trjsnkp3>Sorry, there was an error loading posts for this tag.</p><a href="/blog" class="back-to-blog" data-astro-cid-trjsnkp3>&larr; back to blog</a></div><style>
      .error-container {
        max-width: 600px;
        margin: 100px auto;
        text-align: center;
        padding: 0 20px;
      }

      h1 {
        font-size: 1.8rem;
        color: rgba(240, 240, 240, 0.9);
        margin-bottom: 20px;
        font-family: 'Georgia Custom', Georgia, serif;
      }

      p {
        font-size: 1.1rem;
        color: rgba(220, 220, 220, 0.8);
        margin-bottom: 30px;
        font-family: 'Georgia Custom', Georgia, serif;
      }

      .back-to-blog {
        font-family: 'Georgia Custom', Georgia, serif;
        font-size: 1rem;
        color: rgba(200, 200, 200, 0.8);
        text-decoration: none;
        transition: color 0.3s ease, transform 0.3s ease;
        display: inline-block;
      }

      .back-to-blog:hover {
        color: rgba(240, 240, 240, 1);
        transform: translateX(-3px);
      }
    </style>` })}` : renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "pageTitle": `${tag} | Blog | PVB`, "isHomePage": false, "accentColor": "#f0f0f0", "bgColor": "rgba(10, 10, 10, 0.94)", "backgroundImageUrl": "/images/blackgranite.png", "bodyDataPage": "blog", "data-astro-cid-trjsnkp3": true }, { "default": async ($$result2) => renderTemplate`<div class="blog-header" data-astro-cid-trjsnkp3><div class="blog-title" data-astro-cid-trjsnkp3>#${tag}</div></div><div class="page-container" data-astro-cid-trjsnkp3><!-- Left Sidebar --><div class="blog-sidebar" data-astro-cid-trjsnkp3><!-- Back to Blog --><div class="sidebar-section" data-astro-cid-trjsnkp3><a href="/blog" class="back-to-blog" data-astro-cid-trjsnkp3>&larr; back to blog</a></div><!-- Tags Filter --><div class="tags-container sidebar-section" data-astro-cid-trjsnkp3><div class="tags-title" data-astro-cid-trjsnkp3>other tags:</div><div class="tags-list" data-astro-cid-trjsnkp3>${allTags.filter((t) => t !== tag).map((otherTag) => renderTemplate`<a${addAttribute(`/blog/tag/${otherTag}`, "href")} class="tag-link" data-astro-cid-trjsnkp3>${otherTag}</a>`)}</div></div></div><!-- Main Content --><div class="blog-content" data-astro-cid-trjsnkp3><div class="tag-header" data-astro-cid-trjsnkp3><h2 class="tag-heading" data-astro-cid-trjsnkp3>Posts tagged with "${tag}"</h2><div class="post-count" data-astro-cid-trjsnkp3>${posts.length} post${posts.length !== 1 ? "s" : ""}</div></div><!-- Posts List --><div class="tag-posts" data-astro-cid-trjsnkp3>${posts.length > 0 ? posts.map((post) => renderTemplate`<div class="post-item" data-astro-cid-trjsnkp3><h3 class="post-item-title" data-astro-cid-trjsnkp3><a${addAttribute(`/blog/${post.slug}`, "href")} data-astro-cid-trjsnkp3>${post.data.title}</a></h3><div class="post-item-date" data-astro-cid-trjsnkp3>${new Date(post.data.pubDatetime).toLocaleDateString("en-US", { day: "numeric", month: "long", year: "numeric" })}</div><p class="post-item-description" data-astro-cid-trjsnkp3>${post.data.description}</p><a${addAttribute(`/blog/${post.slug}`, "href")} class="post-item-read-more" data-astro-cid-trjsnkp3>read</a></div>`) : renderTemplate`<div class="no-posts" data-astro-cid-trjsnkp3><p data-astro-cid-trjsnkp3>No posts found with this tag.</p></div>`}</div></div></div>` })}`}`;
}, "C:/Users/<USER>/Desktop/pvb-astro/src/pages/blog/tag/[tag].astro", void 0);

const $$file = "C:/Users/<USER>/Desktop/pvb-astro/src/pages/blog/tag/[tag].astro";
const $$url = "/blog/tag/[tag]";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$tag,
  file: $$file,
  getStaticPaths,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
</file>

<file path=".netlify/build/pages/blog/timeline.astro.mjs">
import { a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead, b as addAttribute } from '../../chunks/astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import { g as getCollection } from '../../chunks/_astro_content_CcaQj6Wl.mjs';
import { $ as $$Layout } from '../../chunks/Layout_rXbp99fE.mjs';
export { renderers } from '../../renderers.mjs';

const $$Timeline = createComponent(async ($$result, $$props, $$slots) => {
  let postsByYear = {};
  let sortedYears = [];
  let error = false;
  try {
    const posts = await getCollection("blog");
    const nonDraftPosts = posts.filter(({ data }) => !data.draft);
    postsByYear = nonDraftPosts.reduce((acc, post) => {
      const year = new Date(post.data.pubDatetime).getFullYear().toString();
      if (!acc[year]) {
        acc[year] = [];
      }
      acc[year].push(post);
      return acc;
    }, {});
    sortedYears = Object.keys(postsByYear).sort((a, b) => parseInt(b) - parseInt(a));
    for (const year of sortedYears) {
      postsByYear[year].sort((a, b) => {
        const dateA = new Date(a.data.pubDatetime).getTime();
        const dateB = new Date(b.data.pubDatetime).getTime();
        return dateB - dateA;
      });
    }
  } catch (e) {
    console.error("Error fetching posts:", e);
    error = true;
  }
  return renderTemplate`${error ? renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "pageTitle": "Archive Error | Blog | PVB", "isHomePage": false, "accentColor": "#f0f0f0", "bgColor": "rgba(10, 10, 10, 0.94)", "backgroundImageUrl": "/images/blackgranite.png", "bodyDataPage": "blog" }, { "default": async ($$result2) => renderTemplate`${maybeRenderHead()}<div class="error-container"><h1>Error Loading Archive</h1><p>Sorry, there was an error loading the blog archive.</p><a href="/blog" class="back-to-blog">&larr; back to blog</a></div><style>
      .error-container {
        max-width: 600px;
        margin: 100px auto;
        text-align: center;
        padding: 0 20px;
      }

      h1 {
        font-size: 1.8rem;
        color: rgba(240, 240, 240, 0.9);
        margin-bottom: 20px;
        font-family: 'Georgia Custom', Georgia, serif;
      }

      p {
        font-size: 1.1rem;
        color: rgba(220, 220, 220, 0.8);
        margin-bottom: 30px;
        font-family: 'Georgia Custom', Georgia, serif;
      }

      .back-to-blog {
        font-family: 'Georgia Custom', Georgia, serif;
        font-size: 0.95rem;
        color: rgba(200, 200, 200, 0.8);
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-block;
        padding: 0.5rem 1rem;
        border: 1px solid rgba(200, 200, 200, 0.3);
        border-radius: 3px;
      }

      .back-to-blog:hover {
        color: rgba(240, 240, 240, 1);
        border-color: rgba(240, 240, 240, 0.5);
        background-color: rgba(255, 255, 255, 0.05);
        transform: translateX(-3px);
      }
    </style>` })}` : renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "pageTitle": "Archive | PVB", "isHomePage": false, "accentColor": "#f0f0f0", "bgColor": "rgba(10, 10, 10, 0.94)", "backgroundImageUrl": "/images/blackgranite.png", "bodyDataPage": "blog" }, { "default": async ($$result2) => renderTemplate`<div class="blog-header"><div class="blog-title">archive</div></div><div class="timeline-container"><!-- Back to Blog --><div class="back-section"><a href="/blog" class="back-to-blog"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19 12H5"></path><path d="M12 19l-7-7 7-7"></path></svg><span>back to blog</span></a></div><!-- Timeline Content --><div class="timeline-content">${sortedYears.map((year) => renderTemplate`<div class="year-section"><h2 class="year-heading">${year}</h2><div class="year-posts">${postsByYear[year].map((post) => renderTemplate`<div class="timeline-post"><div class="post-date">${new Date(post.data.pubDatetime).toLocaleDateString("en-US", { month: "short", day: "numeric" })}</div><h3 class="post-title"><a${addAttribute(`/blog/${post.slug}`, "href")}>${post.data.title}</a></h3></div>`)}</div></div>`)}</div></div><style>
      /* Global Scrollbar Styling */
      :global(html) {
        scrollbar-width: thin;
        scrollbar-color: rgba(0, 0, 0, 0.4) transparent;
      }

      :global(::-webkit-scrollbar) {
        width: 8px;
        height: 8px;
      }

      :global(::-webkit-scrollbar-track) {
        background: transparent;
      }

      :global(::-webkit-scrollbar-thumb) {
        background-color: rgba(100, 100, 100, 0.4);
        border-radius: 4px;
      }

      :global(::-webkit-scrollbar-thumb:hover) {
        background-color: rgba(120, 120, 120, 0.6);
      }

      /* Blog Header - Static (not fixed) */
      .blog-header {
        width: 100%;
        display: flex;
        justify-content: center;
        margin: 55px 0 30px;
      }

      .blog-title {
        font-family: 'Georgia Custom', Georgia, serif;
        font-size: 1.5rem;
        color: rgba(240, 240, 240, 0.9);
        letter-spacing: -0.01em;
        position: relative;
      }

      /* Add subtle underline to blog title */
      .blog-title::after {
        content: '';
        position: absolute;
        bottom: -8px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 1px;
        background-color: rgba(240, 240, 240, 0.4);
      }

      /* Timeline Container */
      .timeline-container {
        max-width: 800px;
        margin: 0 auto 60px;
        padding: 0 30px;
      }

      /* Back to Blog Link */
      .back-section {
        margin-bottom: 40px;
      }

      .back-to-blog {
        font-family: 'Georgia Custom', Georgia, serif;
        font-size: 0.95rem;
        color: rgba(200, 200, 200, 0.8);
        text-decoration: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 6px;
        width: fit-content;
        padding: 0.5rem 1rem;
        border: 1px solid rgba(200, 200, 200, 0.3);
        border-radius: 3px;
      }

      .back-to-blog:hover {
        color: rgba(240, 240, 240, 1);
        border-color: rgba(240, 240, 240, 0.5);
        background-color: rgba(255, 255, 255, 0.05);
        transform: translateX(-3px);
      }

      /* Year Sections */
      .year-section {
        margin-bottom: 50px;
      }

      .year-heading {
        font-family: 'Georgia Custom', Georgia, serif;
        font-size: 1.8rem;
        color: rgba(240, 240, 240, 0.95);
        margin-bottom: 20px;
        font-weight: normal;
        position: relative;
        padding-bottom: 10px;
      }

      .year-heading::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 60px;
        height: 1px;
        background-color: rgba(200, 200, 200, 0.3);
      }

      /* Post Items */
      .timeline-post {
        margin-bottom: 25px;
        display: flex;
        flex-direction: column;
      }

      .post-date {
        font-family: 'Georgia Custom', Georgia, serif;
        font-size: 0.9rem;
        color: rgba(180, 180, 180, 0.75);
        margin-bottom: 5px;
      }

      .post-title {
        font-family: 'Georgia Custom', Georgia, serif;
        font-size: 1.2rem;
        font-weight: normal;
        margin: 0;
      }

      .post-title a {
        color: rgba(220, 220, 220, 0.9);
        text-decoration: none;
        transition: color 0.3s ease;
      }

      .post-title a:hover {
        color: rgba(255, 255, 255, 1);
        text-decoration: underline;
        text-underline-offset: 3px;
        text-decoration-color: rgba(200, 200, 200, 0.4);
      }

      /* Responsive Styles */
      @media (max-width: 768px) {
        .blog-title {
          font-size: 1.3rem;
        }

        .timeline-container {
          padding: 0 20px;
        }

        .year-heading {
          font-size: 1.6rem;
        }

        .post-title {
          font-size: 1.1rem;
        }
      }
    </style>` })}`}`;
}, "C:/Users/<USER>/Desktop/pvb-astro/src/pages/blog/timeline.astro", void 0);

const $$file = "C:/Users/<USER>/Desktop/pvb-astro/src/pages/blog/timeline.astro";
const $$url = "/blog/timeline";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Timeline,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
</file>

<file path=".netlify/build/pages/index.astro.mjs">
import { a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../chunks/astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import { $ as $$Layout } from '../chunks/Layout_rXbp99fE.mjs';
/* empty css                                 */
export { renderers } from '../renderers.mjs';

const $$Index = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "pageTitle": "PVB Home", "isHomePage": true, "accentColor": "#3a2c23", "bgColor": "rgba(250, 246, 242, 0)", "backgroundImageUrl": "/images/whitemarble.png", "bodyDataPage": "home", "data-astro-cid-j7pv25f6": true }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="quote-container" data-astro-cid-j7pv25f6> <div class="quote-indicator-wrapper" id="quote-indicator-wrapper" title="Quote Info" aria-label="Show Quote Information" data-astro-cid-j7pv25f6> <div class="quote-indicator" id="quote-indicator" data-astro-cid-j7pv25f6></div> </div> <p class="quote-text" data-astro-cid-j7pv25f6></p> <p class="quote-attribution" data-astro-cid-j7pv25f6></p> </div> ` })} `;
}, "C:/Users/<USER>/Desktop/pvb-astro/src/pages/index.astro", void 0);

const $$file = "C:/Users/<USER>/Desktop/pvb-astro/src/pages/index.astro";
const $$url = "";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Index,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
</file>

<file path=".netlify/build/pages/search.astro.mjs">
import { a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../chunks/astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import { $ as $$Layout } from '../chunks/Layout_rXbp99fE.mjs';
/* empty css                                  */
export { renderers } from '../renderers.mjs';

const $$Search = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "pageTitle": "Search | PVB", "isHomePage": false, "accentColor": "#2a2a2a", "bgColor": "rgba(245, 245, 245, 0.9)", "backgroundImageUrl": "/images/whitemarble.png", "bodyDataPage": "search", "data-astro-cid-ipsxrsrh": true }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="quote-container" style="max-width: 800px;" data-astro-cid-ipsxrsrh> <h1 class="text-3xl font-bold mb-4 text-accent" data-astro-cid-ipsxrsrh>Search</h1> <p class="mb-8 text-muted" data-astro-cid-ipsxrsrh>Find posts by title, content, or tags</p> <div class="search-container" data-astro-cid-ipsxrsrh> <div id="search-box" class="mb-8" data-astro-cid-ipsxrsrh> <!-- This div will be replaced with the search UI --> <div class="search-placeholder" data-astro-cid-ipsxrsrh> <p class="text-muted" data-astro-cid-ipsxrsrh>Search functionality will be available after building the site.</p> <p class="search-note" data-astro-cid-ipsxrsrh>For local development, you need to build the site first with <code data-astro-cid-ipsxrsrh>npm run build</code></p> </div> </div> <div id="search-results" data-astro-cid-ipsxrsrh> <!-- Results will appear here --> </div> </div> <div class="mt-10" data-astro-cid-ipsxrsrh> <a href="/blog" class="back-link" data-astro-cid-ipsxrsrh> <span class="back-arrow" data-astro-cid-ipsxrsrh>←</span> <span data-astro-cid-ipsxrsrh>Back to blog</span> </a> </div> </div> ` })}  `;
}, "C:/Users/<USER>/Desktop/pvb-astro/src/pages/search.astro", void 0);

const $$file = "C:/Users/<USER>/Desktop/pvb-astro/src/pages/search.astro";
const $$url = "/search";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Search,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
</file>

<file path=".netlify/build/pages/tags.astro.mjs">
import { a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../chunks/astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import { g as getCollection } from '../chunks/_astro_content_CcaQj6Wl.mjs';
import { $ as $$Layout } from '../chunks/Layout_rXbp99fE.mjs';
import { $ as $$Tag } from '../chunks/Tag_DSxhj6Zu.mjs';
import { g as getUniqueTags } from '../chunks/getUniqueTags_3cIFKCqm.mjs';
/* empty css                                 */
export { renderers } from '../renderers.mjs';

const $$Index = createComponent(async ($$result, $$props, $$slots) => {
  const posts = await getCollection("blog");
  const tags = getUniqueTags(posts);
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "pageTitle": "Tags | PVB", "isHomePage": false, "accentColor": "#f0f0f0", "bgColor": "rgba(0, 0, 0, 0.9)", "backgroundImageUrl": "none", "style": "background: linear-gradient(to bottom right, rgba(0, 0, 0, 0.95), rgba(50, 50, 50, 0.9))", "bodyDataPage": "tags", "data-astro-cid-os4i7owy": true }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="quote-container" style="max-width: 800px;" data-astro-cid-os4i7owy> <h1 class="text-3xl font-bold mb-4 text-accent" data-astro-cid-os4i7owy>Tags</h1> <p class="mb-8 text-muted" data-astro-cid-os4i7owy>Browse posts by topic</p> <div class="tags-container" data-astro-cid-os4i7owy> ${tags.map(
    ({ tag, tagName }) => renderTemplate`${renderComponent($$result2, "Tag", $$Tag, { "tag": tag, "tagName": tagName, "size": "lg", "data-astro-cid-os4i7owy": true })}`
  )} </div> <div class="mt-10" data-astro-cid-os4i7owy> <a href="/blog" class="back-link" data-astro-cid-os4i7owy> <span class="back-arrow" data-astro-cid-os4i7owy>←</span> <span data-astro-cid-os4i7owy>Back to blog</span> </a> </div> </div> ` })} `;
}, "C:/Users/<USER>/Desktop/pvb-astro/src/pages/tags/index.astro", void 0);

const $$file = "C:/Users/<USER>/Desktop/pvb-astro/src/pages/tags/index.astro";
const $$url = "/tags";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Index,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
</file>

<file path=".netlify/build/pages/tags/_tag_.astro.mjs">
import { c as createAstro, a as createComponent, m as maybeRenderHead, b as addAttribute, e as renderTransition, d as renderComponent, r as renderTemplate } from '../../chunks/astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import { g as getCollection } from '../../chunks/_astro_content_CcaQj6Wl.mjs';
import { $ as $$Layout } from '../../chunks/Layout_rXbp99fE.mjs';
import { s as slugifyStr } from '../../chunks/slugify_CHvHojPC.mjs';
import { $ as $$Tag } from '../../chunks/Tag_DSxhj6Zu.mjs';
/* empty css                                    */
/* empty css                                    */
import { g as getUniqueTags } from '../../chunks/getUniqueTags_3cIFKCqm.mjs';
export { renderers } from '../../renderers.mjs';

const $$Astro$1 = createAstro("https://pvb.com");
const $$BlogPostCard = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$1, $$props, $$slots);
  Astro2.self = $$BlogPostCard;
  const { post } = Astro2.props;
  const { title, description, pubDatetime, tags } = post.data;
  const datetime = pubDatetime.toISOString();
  const postDate = pubDatetime.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric"
  });
  return renderTemplate`${maybeRenderHead()}<li class="blog-post-card" data-astro-cid-f45vxlzk> <a${addAttribute(`/blog/${post.slug}`, "href")} class="post-link" data-astro-cid-f45vxlzk> <div class="post-content" data-astro-cid-f45vxlzk> <time${addAttribute(datetime, "datetime")} class="post-date" data-astro-cid-f45vxlzk>${postDate}</time> <h3 class="post-title" data-astro-cid-f45vxlzk${addAttribute(renderTransition($$result, "w2v4zgte", "", slugifyStr(title)), "data-astro-transition-scope")}> ${title} </h3> <p class="post-description" data-astro-cid-f45vxlzk>${description}</p> <div class="post-tags" data-astro-cid-f45vxlzk> ${tags.map((tag) => renderTemplate`${renderComponent($$result, "Tag", $$Tag, { "tag": slugifyStr(tag), "tagName": tag, "data-astro-cid-f45vxlzk": true })}`)} </div> </div> </a> </li> `;
}, "C:/Users/<USER>/Desktop/pvb-astro/src/components/BlogPostCard.astro", "self");

function getPostsByTag(posts, tag) {
  return posts.filter(
    (post) => post.data.tags?.some((postTag) => slugifyStr(postTag) === tag)
  );
}

const $$Astro = createAstro("https://pvb.com");
async function getStaticPaths() {
  const posts = await getCollection("blog");
  const tags = getUniqueTags(posts);
  return tags.map(({ tag, tagName }) => ({
    params: { tag },
    props: {
      posts: getPostsByTag(posts, tag),
      tag,
      tagName
    }
  }));
}
const $$Index = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Index;
  const { posts, tag, tagName } = Astro2.props;
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "pageTitle": `Tag: ${tagName} | PVB`, "isHomePage": false, "accentColor": "#f0f0f0", "bgColor": "rgba(0, 0, 0, 0.9)", "backgroundImageUrl": "none", "style": "background: linear-gradient(to bottom right, rgba(0, 0, 0, 0.95), rgba(50, 50, 50, 0.9))", "bodyDataPage": "tag", "data-astro-cid-hqxj4ft6": true }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="quote-container" style="max-width: 800px;" data-astro-cid-hqxj4ft6> <h1 class="text-3xl font-bold mb-4 text-accent" data-astro-cid-hqxj4ft6>
Tag: <span data-astro-cid-hqxj4ft6${addAttribute(renderTransition($$result2, "6rhg7idu", "", tag), "data-astro-transition-scope")}>#${tagName}</span> </h1> <p class="mb-8 text-muted" data-astro-cid-hqxj4ft6> ${posts.length} post${posts.length > 1 ? "s" : ""} with this tag
</p> <div class="posts-container" data-astro-cid-hqxj4ft6> <ul data-astro-cid-hqxj4ft6> ${posts.map((post) => renderTemplate`${renderComponent($$result2, "BlogPostCard", $$BlogPostCard, { "post": post, "data-astro-cid-hqxj4ft6": true })}`)} </ul> </div> <div class="mt-10" data-astro-cid-hqxj4ft6> <a href="/tags" class="back-link" data-astro-cid-hqxj4ft6> <span class="back-arrow" data-astro-cid-hqxj4ft6>←</span> <span data-astro-cid-hqxj4ft6>All tags</span> </a> </div> </div> ` })} `;
}, "C:/Users/<USER>/Desktop/pvb-astro/src/pages/tags/[tag]/index.astro", "self");

const $$file = "C:/Users/<USER>/Desktop/pvb-astro/src/pages/tags/[tag]/index.astro";
const $$url = "/tags/[tag]";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Index,
  file: $$file,
  getStaticPaths,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
</file>

<file path=".netlify/build/pages/work.astro.mjs">
import { c as createAstro, a as createComponent, m as maybeRenderHead, b as addAttribute, r as renderTemplate, e as renderTransition, d as renderComponent } from '../chunks/astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import { g as getCollection } from '../chunks/_astro_content_CcaQj6Wl.mjs';
import { $ as $$Layout } from '../chunks/Layout_rXbp99fE.mjs';
import 'clsx';
import { s as slugifyStr } from '../chunks/slugify_CHvHojPC.mjs';
/* empty css                                */
/* empty css                                 */
export { renderers } from '../renderers.mjs';

const $$Astro = createAstro("https://pvb.com");
const $$ProjectCard = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$ProjectCard;
  const { project, variant = "standard" } = Astro2.props;
  const { title, description, projectDate, tags, status, featured, repoUrl, liveUrl } = project.data;
  function formatDate(date) {
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short"
    });
  }
  const isFeatured = variant === "featured";
  return renderTemplate`${isFeatured ? renderTemplate`${maybeRenderHead()}<article class="project-card featured" data-astro-cid-mspuyifq><a${addAttribute(`/work/${project.slug}`, "href")} class="project-link" data-astro-cid-mspuyifq><div class="project-meta" data-astro-cid-mspuyifq><time${addAttribute(projectDate.toISOString(), "datetime")} data-astro-cid-mspuyifq>${formatDate(projectDate)}</time><div class="project-badges" data-astro-cid-mspuyifq>${status && status !== "Completed" && renderTemplate`<span class="project-status" data-astro-cid-mspuyifq>${status}</span>`}<span class="featured-badge" data-astro-cid-mspuyifq>Featured</span></div></div><h2 class="project-title" data-astro-cid-mspuyifq${addAttribute(renderTransition($$result, "xatmo7nz", "", slugifyStr(title)), "data-astro-transition-scope")}>${title}</h2><p class="project-description" data-astro-cid-mspuyifq>${description}</p>${tags && renderTemplate`<div class="project-tags" data-astro-cid-mspuyifq>${tags.map((tag) => renderTemplate`<span class="tag" data-astro-cid-mspuyifq>${tag}</span>`)}</div>`}<div class="project-actions" data-astro-cid-mspuyifq><span class="view-details" data-astro-cid-mspuyifq>
Read more <span class="arrow" data-astro-cid-mspuyifq>→</span></span><div class="external-links" data-astro-cid-mspuyifq>${repoUrl && renderTemplate`<a${addAttribute(repoUrl, "href")} target="_blank" rel="noopener noreferrer" class="external-link" onclick="event.stopPropagation()" data-astro-cid-mspuyifq>
Source <span class="ext-arrow" data-astro-cid-mspuyifq>↗</span></a>`}${liveUrl && renderTemplate`<a${addAttribute(liveUrl, "href")} target="_blank" rel="noopener noreferrer" class="external-link" onclick="event.stopPropagation()" data-astro-cid-mspuyifq>
Demo <span class="ext-arrow" data-astro-cid-mspuyifq>↗</span></a>`}</div></div></a></article>` : renderTemplate`<article class="project-card standard" data-astro-cid-mspuyifq><a${addAttribute(`/work/${project.slug}`, "href")} class="project-link" data-astro-cid-mspuyifq><div class="project-content" data-astro-cid-mspuyifq><h2 class="project-title" data-astro-cid-mspuyifq${addAttribute(renderTransition($$result, "pixvamr3", "", slugifyStr(title)), "data-astro-transition-scope")}>${title}</h2><p class="project-description" data-astro-cid-mspuyifq>${description}</p><div class="project-footer" data-astro-cid-mspuyifq><div class="project-meta" data-astro-cid-mspuyifq><time${addAttribute(projectDate.toISOString(), "datetime")} data-astro-cid-mspuyifq>${formatDate(projectDate)}</time>${status && status !== "Completed" && renderTemplate`<span class="project-status small" data-astro-cid-mspuyifq>${status}</span>`}</div><div class="project-tags-container" data-astro-cid-mspuyifq>${tags && tags.length > 0 && renderTemplate`<div class="project-tags small" data-astro-cid-mspuyifq>${tags.slice(0, 3).map((tag) => renderTemplate`<span class="tag small" data-astro-cid-mspuyifq>${tag}</span>`)}${tags.length > 3 && renderTemplate`<span class="more-tags" data-astro-cid-mspuyifq>+${tags.length - 3}</span>`}</div>`}</div></div></div></a></article>`}`;
}, "C:/Users/<USER>/Desktop/pvb-astro/src/components/ProjectCard.astro", "self");

const $$Work = createComponent(async ($$result, $$props, $$slots) => {
  const allProjects = await getCollection(
    "work",
    ({ data }) => data.status !== "Archived"
  );
  const sortedProjects = allProjects.sort(
    (a, b) => b.data.projectDate.valueOf() - a.data.projectDate.valueOf()
  );
  const featuredProjects = sortedProjects.filter((project) => project.data.featured);
  const regularProjects = sortedProjects.filter((project) => !project.data.featured);
  const publications = [
    {
      title: "Recursive Self-Referential Compression (RSRC): AI's Survival Map in the Post-Scaling Era",
      journal: "MURST Research Initiative",
      year: 2025,
      url: "#",
      pdfUrl: "/pdfs/RSRC_Paper.pdf"
    },
    {
      title: "Cordyceps militaris: Culturing, Optimization and Bioactive Compound Analysis",
      journal: "Research Paper (IEEE format)",
      year: 2023,
      url: "#",
      pdfUrl: "/pdfs/Cordyceps_Paper.pdf"
    }
  ];
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "pageTitle": "Work & Research | PVB", "isHomePage": false, "accentColor": "#f0f0f0", "bgColor": "rgba(0, 0, 0, 0.88)", "backgroundImageUrl": "/images/blackgranite.png", "bodyDataPage": "work", "data-astro-cid-jljc7dey": true }, { "default": async ($$result2) => renderTemplate`  ${maybeRenderHead()}<div class="work-header" data-astro-cid-jljc7dey> <h1 class="work-title" data-astro-cid-jljc7dey>work</h1> </div> <div class="content-container" data-astro-cid-jljc7dey> <!-- Featured Projects Section --> ${featuredProjects.length > 0 && renderTemplate`<section class="featured-projects-section" data-astro-cid-jljc7dey> ${featuredProjects.map((project) => renderTemplate`${renderComponent($$result2, "ProjectCard", $$ProjectCard, { "project": project, "variant": "featured", "data-astro-cid-jljc7dey": true })}`)} </section>`} <!-- Projects Section with refined, elegant styling --> ${regularProjects.length > 0 && renderTemplate`<section class="projects-section" data-astro-cid-jljc7dey> <div class="section-header" data-astro-cid-jljc7dey> <div class="section-line" data-astro-cid-jljc7dey></div> <h2 class="section-title" data-astro-cid-jljc7dey>Projects</h2> <div class="section-line" data-astro-cid-jljc7dey></div> </div> <div class="projects-grid" data-astro-cid-jljc7dey> ${regularProjects.map((project) => renderTemplate`${renderComponent($$result2, "ProjectCard", $$ProjectCard, { "project": project, "variant": "standard", "data-astro-cid-jljc7dey": true })}`)} </div> </section>`} <!-- Publications Section with refined styling --> <section class="publications-section" data-astro-cid-jljc7dey> <div class="section-header" data-astro-cid-jljc7dey> <div class="section-line" data-astro-cid-jljc7dey></div> <h2 class="section-title" data-astro-cid-jljc7dey>Research & Publications</h2> <div class="section-line" data-astro-cid-jljc7dey></div> </div> <ul class="publications-list" data-astro-cid-jljc7dey> ${publications.map((pub) => renderTemplate`<li class="publication-item" data-astro-cid-jljc7dey> <h3 class="pub-title" data-astro-cid-jljc7dey>${pub.title}</h3> <div class="pub-meta" data-astro-cid-jljc7dey> <p class="pub-details" data-astro-cid-jljc7dey>${pub.journal}, ${pub.year}</p> <div class="pub-links" data-astro-cid-jljc7dey> ${pub.pdfUrl && renderTemplate`<a${addAttribute(pub.pdfUrl, "href")} target="_blank" rel="noopener noreferrer" class="pub-link" data-astro-cid-jljc7dey>
PDF <span class="arrow" data-astro-cid-jljc7dey>↗</span> </a>`} ${pub.url && pub.url !== "#" && renderTemplate`<a${addAttribute(pub.url, "href")} target="_blank" rel="noopener noreferrer" class="pub-link" data-astro-cid-jljc7dey>
Link <span class="arrow" data-astro-cid-jljc7dey>↗</span> </a>`} </div> </div> </li>`)} </ul> </section> </div> ` })} `;
}, "C:/Users/<USER>/Desktop/pvb-astro/src/pages/work.astro", void 0);

const $$file = "C:/Users/<USER>/Desktop/pvb-astro/src/pages/work.astro";
const $$url = "/work";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Work,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
</file>

<file path=".netlify/build/pages/work/_---slug_.astro.mjs">
import { c as createAstro, a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead, b as addAttribute } from '../../chunks/astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import { g as getCollection } from '../../chunks/_astro_content_CcaQj6Wl.mjs';
import { $ as $$BlogPost } from '../../chunks/BlogPost_DYNjvq7J.mjs';
/* empty css                                     */
export { renderers } from '../../renderers.mjs';

const SITE = {
  author: "PVB"};

const $$Astro = createAstro("https://pvb.com");
async function getStaticPaths() {
  const workEntries = await getCollection("work");
  return workEntries.map((entry) => ({
    params: { slug: entry.slug },
    props: { entry }
  }));
}
const $$ = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$;
  const { entry } = Astro2.props;
  const { Content } = await entry.render();
  const postData = {
    title: entry.data.title,
    author: SITE.author,
    description: entry.data.description,
    ogImage: entry.data.ogImage,
    pubDatetime: entry.data.projectDate,
    modDatetime: null,
    tags: entry.data.tags,
    featured: entry.data.featured,
    draft: false,
    canonicalURL: entry.data.canonicalURL
  };
  const { repoUrl, liveUrl, status } = entry.data;
  function formatDate(date) {
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long"
    });
  }
  return renderTemplate`${renderComponent($$result, "BlogPostLayout", $$BlogPost, { "post": { ...entry, data: postData }, "data-astro-cid-qwekciqp": true }, { "default": async ($$result2) => renderTemplate`  ${maybeRenderHead()}<div class="project-meta" data-astro-cid-qwekciqp> <div class="meta-grid" data-astro-cid-qwekciqp> <div class="meta-item" data-astro-cid-qwekciqp> <div class="meta-label" data-astro-cid-qwekciqp>Timeline</div> <div class="meta-value" data-astro-cid-qwekciqp>${formatDate(postData.pubDatetime)}</div> </div> ${status && status !== "Completed" && renderTemplate`<div class="meta-item" data-astro-cid-qwekciqp> <div class="meta-label" data-astro-cid-qwekciqp>Status</div> <div class="meta-value" data-astro-cid-qwekciqp> <span class="status-pill" data-astro-cid-qwekciqp>${status}</span> </div> </div>`} </div> <div class="project-links" data-astro-cid-qwekciqp> ${repoUrl && renderTemplate`<a${addAttribute(repoUrl, "href")} target="_blank" rel="noopener noreferrer" class="project-link" data-astro-cid-qwekciqp>
Source <span class="link-arrow" data-astro-cid-qwekciqp>↗</span> </a>`} ${liveUrl && renderTemplate`<a${addAttribute(liveUrl, "href")} target="_blank" rel="noopener noreferrer" class="project-link" data-astro-cid-qwekciqp>
Demo <span class="link-arrow" data-astro-cid-qwekciqp>↗</span> </a>`} </div> </div>  <div class="project-content" data-astro-cid-qwekciqp> ${renderComponent($$result2, "Content", Content, { "data-astro-cid-qwekciqp": true })} </div>  <div class="project-technologies" data-astro-cid-qwekciqp> <div class="tech-label" data-astro-cid-qwekciqp>Technologies</div> <div class="tech-tags" data-astro-cid-qwekciqp> ${postData.tags.map((tag) => renderTemplate`<span class="tech-tag" data-astro-cid-qwekciqp>${tag}</span>`)} </div> </div> ` })}  `;
}, "C:/Users/<USER>/Desktop/pvb-astro/src/pages/work/[...slug].astro", void 0);

const $$file = "C:/Users/<USER>/Desktop/pvb-astro/src/pages/work/[...slug].astro";
const $$url = "/work/[...slug]";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$,
  file: $$file,
  getStaticPaths,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
</file>

<file path=".netlify/build/renderers.mjs">
const renderers = [];

export { renderers };
</file>

<file path=".netlify/v1/config.json">
{"images":{"remote_images":[]},"headers":[{"for":"/_astro/*","values":{"Cache-Control":"public, max-age=31536000, immutable"}}]}
</file>

<file path=".roo/mcp.json">
{
  "mcpServers": {}
}
</file>

<file path=".roomodes">
{
  "customModes": []
}
</file>

<file path="convert-md-to-json.js">
/**
 * Convert markdown content in src/content into JSON for Astro loader
 * Run with: node convert-md-to-json.js
 */
import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';
import { marked } from 'marked';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Directories
const projectRoot = path.resolve(__dirname);
const contentDir = path.join(projectRoot, 'src', 'content');
const ghostDataDir = path.join(projectRoot, 'src', 'data', 'ghost');

// Ensure ghost data dir exists
if (!fs.existsSync(ghostDataDir)) {
  fs.mkdirSync(ghostDataDir, { recursive: true });
}

// Convert markdown posts (blog and work)
const mdDirs = [
  { subdir: 'blog', type: 'blog' },
  { subdir: 'work', type: 'work' }
];
let posts = [];
mdDirs.forEach(({ subdir, type }) => {
  const dirPath = path.join(contentDir, subdir);
  if (fs.existsSync(dirPath)) {
    fs.readdirSync(dirPath)
      .filter(file => file.endsWith('.md'))
      .forEach(file => {
        const filepath = path.join(dirPath, file);
        const slug = path.basename(file, '.md');
        const rawContent = fs.readFileSync(filepath, 'utf8');
        const { data, content } = matter(rawContent);
        const html = marked(content);
        posts.push({ slug, type, ...data, content, html });
      });
  }
});
// Write posts.json
fs.writeFileSync(
  path.join(ghostDataDir, 'posts.json'),
  JSON.stringify(posts, null, 2)
);
console.log(`Converted ${posts.length} markdown posts to JSON at src/data/ghost/posts.json`);

// Convert quotes
const quotesDir = path.join(contentDir, 'quotes');
let quotes = [];
if (fs.existsSync(quotesDir)) {
  fs.readdirSync(quotesDir)
    .filter(file => file.endsWith('.md'))
    .forEach(file => {
      const filepath = path.join(quotesDir, file);
      const raw = fs.readFileSync(filepath, 'utf8');
      const { data } = matter(raw);
      quotes.push(data);
    });
}
// Write quotes.json
fs.writeFileSync(
  path.join(ghostDataDir, 'quotes.json'),
  JSON.stringify(quotes, null, 2)
);
console.log(`Converted ${quotes.length} quotes to JSON at src/data/ghost/quotes.json`);
</file>

<file path="public/api/quotes.json">
[
  {
    "text": "Many mistake stability for safety, but only the dead remain still.",
    "author": "Pruthvi Bhat",
    "linkedPage": "/blog/stability-vs-safety",
    "cardTitle": "Core Philosophy",
    "cardSubtitle": "Exploring the need for change"
  },
  {
    "text": "The purpose of knowledge is action, not more knowledge.",
    "author": "Pruthvi Bhat",
    "linkedPage": "/blog/knowledge-and-action",
    "cardTitle": "Applied Learning",
    "cardSubtitle": "Insights into meaningful action"
  },
  {
    "text": "Silence is not empty, it's full of answers.",
    "author": "Unknown",
    "linkedPage": "/blog/power-of-silence",
    "cardTitle": "Mindfulness",
    "cardSubtitle": "Finding clarity in quiet"
  }
]
</file>

<file path="public/images/logos/bluesky.svg">
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64">
  <path fill="#0085ff" d="M13.873 3.805C21.21 9.332 29.103 20.537 32 26.55v15.882c0-.338-.13.044-.41.867-1.512 4.456-7.418 21.847-20.923 7.944-7.111-7.32-3.819-14.64 9.125-16.85-7.405 1.264-15.73-.825-18.014-9.015C1.12 23.022 0 8.51 0 6.55 0-3.268 8.579-.182 13.873 3.805ZM50.127 3.805C42.79 9.332 34.897 20.537 32 26.55v15.882c0-.338.13.044.41.867 1.512 4.456 7.418 21.847 20.923 7.944 7.111-7.32 3.819-14.64-9.125-16.85 7.405 1.264 15.73-.825 18.014-9.015C62.88 23.022 64 8.51 64 6.55c0-9.818-8.578-6.732-13.873-2.745Z"/>
</svg>
</file>

<file path="public/images/logos/lesswrong.svg">
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
  <path d="M29.1,29.2l6.4,11.6l4.3-0.8l0.8-4.3L29.1,29.2z M40.7,64.5l-0.8-4.3l-4.3-0.8L29.2,71L40.7,64.5z M70.9,70.9l-6.4-11.6l-4.3,0.8l-0.8,4.3L70.9,70.9z M64.4,40.8l6.4-11.6l-11.6,6.4l0.8,4.3L64.4,40.8z M67.4,58.8l10.8,19.4L58.8,67.4L50,98.8l-8.8-31.4L21.9,78.2l10.8-19.4L1.2,50.1l31.4-8.8L21.9,21.9l19.4,10.8L50,1.3l8.8,31.4l19.4-10.8L67.4,41.3L98.8,50L67.4,58.8zM57.7,57.8L83.5,50L50,50.1l7.7-7.7L50,16.6v33.5l-7.7-7.7l-25.8,7.7H50l-7.7,7.7L50,83.5V50.1L57.7,57.8z" fill="#5f9b65"/>
</svg>
</file>

<file path="public/images/logos/orcid.svg">
<svg viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <path d="M256,128c0,70.7-57.3,128-128,128C57.3,256,0,198.7,0,128C0,57.3,57.3,0,128,0C198.7,0,256,57.3,256,128z" fill="#A6CE39"/>
  <path d="M86.3,186.2H70.9V79.1h15.4v107.1z M108.9,79.1h41.6c39.6,0,57,28.3,57,53.6c0,27.5-21.5,53.6-56.8,53.6h-41.8V79.1z M124.3,172.4h24.5c34.9,0,42.9-26.5,42.9-39.7c0-21.5-13.7-39.7-43.7-39.7h-23.7V172.4z M88.7,56.8c0,5.5-4.5,10.1-10.1,10.1c-5.6,0-10.1-4.6-10.1-10.1c0-5.6,4.5-10.1,10.1-10.1C84.2,46.7,88.7,51.3,88.7,56.8z" fill="#fff"/>
</svg>
</file>

<file path="public/images/logos/x.svg">
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
  <path fill="#ffffff" d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
</svg>
</file>

<file path="public/images/logos/zenodo.svg">
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 50 50">
  <path fill="#2f6fa7" d="m 28.324,20.044 c -0.043,-0.106 -0.084,-0.214 -0.131,-0.32 -0.707,-1.602 -1.656,-2.997 -2.848,-4.19 -1.188,-1.187 -2.582,-2.125 -4.184,-2.805 -1.605,-0.678 -3.309,-1.02 -5.104,-1.02 -1.85,0 -3.564,0.342 -5.137,1.02 -1.467,0.628 -2.764,1.488 -3.91,2.552 V 14.84 c 0,-1.557 -1.262,-2.822 -2.82,-2.822 h -19.775 c -1.557,0 -2.82,1.265 -2.82,2.822 0,1.559 1.264,2.82 2.82,2.82 h 15.541 l -18.23,24.546 c -0.362,0.487 -0.557,1.077 -0.557,1.682 v 1.841 c 0,1.558 1.264,2.822 2.822,2.822 H 5.038 c 1.488,0 2.705,-1.153 2.812,-2.614 0.932,0.743 1.967,1.364 3.109,1.848 1.605,0.684 3.299,1.021 5.102,1.021 2.723,0 5.15,-0.726 7.287,-2.187 1.727,-1.176 3.092,-2.639 4.084,-4.389 0.832799,-1.472094 1.418284,-2.633352 1.221889,-3.729182 -0.173003,-0.965318 -0.694914,-1.946419 -2.326865,-2.378358 -0.58,0 -1.376024,0.17454 -1.833024,0.49254 -0.463,0.316 -0.793,0.744 -0.982,1.275 l -0.453,0.93 c -0.631,1.365 -1.566,2.443 -2.809,3.244 -1.238,0.803 -2.633,1.201 -4.188,1.201 -1.023,0 -2.004,-0.191 -2.955,-0.579 -0.941,-0.39 -1.758,-0.935 -2.439,-1.64 C 9.986,40.343 9.441,39.526 9.027,38.603 8.617,37.679 8.41,36.71 8.41,35.687 v -2.476 h 17.715 c 0,0 1.517774,-0.15466 2.183375,-0.770672 0.958496,-0.887085 0.864622,-2.15038 0.864622,-2.15038 0,0 -0.04354,-5.066834 -0.338376,-7.578154 C 28.729048,21.812563 28.324,20.044 28.324,20.044 Z M -11.767,42.91 2.991,23.036 C 2.913,23.623 2.87,24.22 2.87,24.827 v 10.86 c 0,1.799 0.35,3.498 1.059,5.104 0.328,0.752 0.719,1.458 1.156,2.119 -0.016,0 -0.031,-10e-4 -0.047,-10e-4 H -11.767 Z M 23.71,27.667 H 8.409 v -2.841 c 0,-1.015 0.189,-1.99 0.58,-2.912 0.391,-0.922 0.936,-1.74 1.645,-2.444 0.697,-0.703 1.516,-1.249 2.438,-1.641 0.922,-0.388 1.92,-0.581 2.99,-0.581 1.02,0 2.002,0.193 2.949,0.581 0.949,0.393 1.764,0.938 2.441,1.641 0.682,0.704 1.225,1.521 1.641,2.444 0.414,0.922 0.617,1.896 0.617,2.912 z" transform="translate(20.35 -4.735)"/>
</svg>
</file>

<file path="README.md">
# PVB Astro Website

A clean, minimalist personal website built with Astro that features a unique 3-dot navigation system and elegant transitions.

## Features

- Minimalist design with focus on content
- Unique 3-dot navigation system
- Menu overlay for site navigation
- Quote display system with info card
- Smooth transitions and animations
- Responsive design for all device sizes

## Project Structure

```
/
├── public/              # Static assets
│   ├── fonts/           # Custom fonts
│   └── images/          # Background images and other static images
├── src/
│   ├── components/      # Reusable UI components
│   │   ├── Navigation.astro    # 3-dot navigation system
│   │   └── MainMenu.astro      # Menu overlay
│   ├── layouts/
│   │   └── Layout.astro        # Main layout template
│   ├── pages/           # Page components
│   │   ├── index.astro         # Home page
│   │   └── about.astro         # About page
│   ├── scripts/
│   │   └── main.js             # Main JavaScript functionality
│   └── styles/
│       └── global.css          # Global styles
├── astro.config.mjs     # Astro configuration
├── package.json         # Project dependencies
└── tsconfig.json        # TypeScript configuration
```

## Getting Started

1. **Install dependencies:**

```bash
npm install
```

2. **Start the development server:**

```bash
npm run dev
```

3. **Build for production:**

```bash
npm run build
```

## Adding New Pages

To add a new page:

1. Create a new `.astro` file in the `src/pages` directory
2. Use the main Layout component with appropriate props
3. Add your page-specific content

Example:

```astro
---
import Layout from '../layouts/Layout.astro';
---

<Layout
    pageTitle="Your Page Title"
    isHomePage={false}
    accentColor="#your-color"
    bgColor="rgba(your-bg-color)"
    backgroundImageUrl="/images/your-background.png"
    bodyDataPage="your-page-name"
>
    <div class="your-content-container">
        <!-- Your content here -->
    </div>
</Layout>
```

## Customization

- Edit global styles in `src/styles/global.css`
- Modify component behavior in the respective component files
- Update the menu items in `src/components/MainMenu.astro`

## License

This project is licensed under the MIT License. See the LICENSE file for details.
</file>

<file path="src/components/UtopiaProjectGrid.astro">
---
import { slugifyStr } from "../utils/slugify";

export interface Props {
  projects: any[]; // Array of projects
}

const { projects } = Astro.props;
---

<div class="utopia-container">
  <h2 class="utopia-world-title">UTOPIA WORLD</h2>
  
  <div class="utopia-grid">
    {projects.map((project) => (
      <div class="grid-item">
        <div class="project-image-container">
          <img 
            src={project.feature_image || project.cover_image || '/images/placeholder.jpg'} 
            alt={project.title}
            class="project-image"
          />
          <a href={`/work/${project.slug}`} class="explore-link">Explore</a>
        </div>
      </div>
    ))}
  </div>
</div>

<style>
  /* Utopia World styling */
  .utopia-container {
    width: 100%;
    max-width: 90rem;
    margin: 0 auto;
    padding: 2rem 1rem;
  }

  .utopia-world-title {
    font-size: 1.5rem;
    text-align: center;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 0.05em;
    margin-bottom: 3rem;
    color: #fff;
  }

  .utopia-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2px;
    width: 100%;
  }

  .grid-item {
    aspect-ratio: 1;
    overflow: hidden;
    position: relative;
  }

  .project-image-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .project-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
  }

  .explore-link {
    position: absolute;
    bottom: 1.5rem;
    left: 50%;
    transform: translateX(-50%);
    background: transparent;
    color: white;
    padding: 0.5rem 1rem;
    text-decoration: none;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 0.1em;
    opacity: 0;
    transition: opacity 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.3);
    z-index: 2;
  }

  .project-image-container:hover .project-image {
    transform: scale(1.05);
  }

  .project-image-container:hover .explore-link {
    opacity: 1;
  }

  .project-image-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.2);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .project-image-container:hover::after {
    opacity: 1;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .utopia-grid {
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
  }

  @media (max-width: 480px) {
    .utopia-grid {
      grid-template-columns: 1fr;
    }
    
    .grid-item {
      aspect-ratio: 1 / 1.2;
    }
  }
</style>
</file>

<file path="src/content/.gitkeep">
# This directory is kept empty intentionally
# All content is now sourced from src/data/ghost/ JSON files
</file>

<file path="src/data/ghost/.keep">

</file>

<file path="src/data/ghost/authors.json">
{
  "authors": [
    {
      "id": "1",
      "name": "Approx",
      "slug": "approx",
      "profile_image": null,
      "cover_image": null,
      "bio": null,
      "website": null,
      "location": null,
      "facebook": null,
      "twitter": null,
      "meta_title": null,
      "meta_description": null,
      "url": "/author/approx/"
    }
  ],
  "meta": {
    "pagination": {
      "page": 1,
      "limit": "all",
      "pages": 1,
      "total": 1,
      "next": null,
      "prev": null
    }
  }
}
</file>

<file path="src/data/ghost/pages.json">
{
  "pages": [
    {
      "id": "67ffa02e4ea0d83c60c038c8",
      "uuid": "91a3c080-3f70-4f8d-b125-e055ad8c91bf",
      "title": "About this site",
      "slug": "about",
      "html": "<p>pvb is an independent publication launched in April 2025 by Approx. If you subscribe today, you'll get full access to the website as well as email newsletters about new content when it's available. Your subscription makes this site possible, and allows pvb to continue to exist. Thank you!</p><h3 id=\"access-all-areas\">Access all areas</h3><p>By signing up, you'll get access to the full archive of everything that's been published before and everything that's still to come. Your very own private library.</p><h3 id=\"fresh-content-delivered\">Fresh content, delivered</h3><p>Stay up to date with new content sent straight to your inbox! No more worrying about whether you missed something because of a pesky algorithm or news feed.</p><h3 id=\"meet-people-like-you\">Meet people like you</h3><p>Join a community of other subscribers who share the same interests.</p><hr><h3 id=\"start-your-own-thing\">Start your own thing</h3><p>Enjoying the experience? Get started for free and set up your very own subscription business using <a href=\"https://ghost.org\">Ghost</a>, the same platform that powers this website.</p>",
      "comment_id": "67ffa02e4ea0d83c60c038c8",
      "feature_image": null,
      "featured": false,
      "visibility": "public",
      "created_at": "2025-04-16 12:18:54",
      "updated_at": "2025-04-18 12:52:27",
      "published_at": "2025-04-16 12:18:53",
      "custom_excerpt": null,
      "codeinjection_head": null,
      "codeinjection_foot": null,
      "custom_template": null,
      "canonical_url": null,
      "url": "/about/",
      "excerpt": "pvb is an independent publication launched in April 2025 by Approx. If you subscribe today, you'll get full access to the website as well as email newsletters about new content when it's available. Your subscription makes this site possible, and allows pvb to continue to exist. Thank you!Access all areasBy signing up, you'll get access to the full archive of everything that's been published before and everything that's still to come. Your very own private library.Fresh content, deliveredStay up ",
      "reading_time": 1,
      "access": "public",
      "authors": [
        {
          "id": "1",
          "name": "Approx",
          "slug": "approx",
          "profile_image": null,
          "cover_image": null,
          "bio": null,
          "website": null,
          "location": null,
          "facebook": null,
          "twitter": null,
          "meta_title": null,
          "meta_description": null,
          "url": "/author/approx/"
        }
      ],
      "primary_author": {
        "id": "1",
        "name": "Approx",
        "slug": "approx",
        "profile_image": null,
        "cover_image": null,
        "bio": null,
        "website": null,
        "location": null,
        "facebook": null,
        "twitter": null,
        "meta_title": null,
        "meta_description": null,
        "url": "/author/approx/"
      },
      "tags": [],
      "primary_tag": null
    }
  ],
  "meta": {
    "pagination": {
      "page": 1,
      "limit": "all",
      "pages": 1,
      "total": 1,
      "next": null,
      "prev": null
    }
  }
}
</file>

<file path="src/data/ghost/posts.json">
[
  {
    "id": "68024d93a9db3b2a9cc5d050",
    "uuid": "8e1c6cc7-5790-4d0d-90a7-178f787bb5ff",
    "title": "Synapse Drive – Centralized Resource Platform",
    "slug": "synapse-drive-centralized-resource-platform",
    "html": "<p>Inspired by the chaos of scattered files and outdated links, Synapse Drive unified SOPs, playbooks, and live docs into a version-controlled layer with structured JSON backends. Local-first. Fully exportable. Unbreakable hierarchy.</p>",
    "comment_id": "68024d93a9db3b2a9cc5d050",
    "feature_image": null,
    "featured": false,
    "visibility": "public",
    "created_at": "2025-04-18 13:03:15",
    "updated_at": "2025-04-18 13:03:40",
    "published_at": "2025-04-18 13:03:40",
    "custom_excerpt": "Built a Notion-meets-GitHub hub for knowledge sovereignty.\n",
    "codeinjection_head": null,
    "codeinjection_foot": null,
    "custom_template": null,
    "canonical_url": null,
    "url": "/synapse-drive-centralized-resource-platform/",
    "excerpt": "Built a Notion-meets-GitHub hub for knowledge sovereignty.\n",
    "reading_time": 1,
    "access": "public",
    "authors": [
      {
        "id": "1",
        "name": "Approx",
        "slug": "approx",
        "profile_image": null,
        "cover_image": null,
        "bio": null,
        "website": null,
        "location": null,
        "facebook": null,
        "twitter": null,
        "meta_title": null,
        "meta_description": null,
        "url": "/author/approx/"
      }
    ],
    "primary_author": {
      "id": "1",
      "name": "Approx",
      "slug": "approx",
      "profile_image": null,
      "cover_image": null,
      "bio": null,
      "website": null,
      "location": null,
      "facebook": null,
      "twitter": null,
      "meta_title": null,
      "meta_description": null,
      "url": "/author/approx/"
    },
    "tags": [
      {
        "id": "68024c27a9db3b2a9cc5d004",
        "name": "work",
        "slug": "work",
        "description": null,
        "feature_image": null,
        "visibility": "public",
        "meta_title": null,
        "meta_description": null,
        "og_image": null,
        "og_title": null,
        "og_description": null,
        "twitter_image": null,
        "twitter_title": null,
        "twitter_description": null,
        "codeinjection_head": null,
        "codeinjection_foot": null,
        "canonical_url": null,
        "accent_color": "#ff0f0f",
        "url": "/tag/work/"
      }
    ],
    "primary_tag": {
      "id": "68024c27a9db3b2a9cc5d004",
      "name": "work",
      "slug": "work",
      "description": null,
      "feature_image": null,
      "visibility": "public",
      "meta_title": null,
      "meta_description": null,
      "og_image": null,
      "og_title": null,
      "og_description": null,
      "twitter_image": null,
      "twitter_title": null,
      "twitter_description": null,
      "codeinjection_head": null,
      "codeinjection_foot": null,
      "canonical_url": null,
      "accent_color": "#ff0f0f",
      "url": "/tag/work/"
    }
  },
  {
    "id": "68024d75a9db3b2a9cc5d043",
    "uuid": "e86279e7-ebb1-4cd3-a4c8-d1513209fb4a",
    "title": "Leads Wizard – AI‑Driven Lead Qualification",
    "slug": "leads-wizard-ai-driven-lead-qualification",
    "html": "<p>Developed a workflow that leverages OpenRouter-based orchestration with metacognitive filtering (via VibeCheck) to pre-qualify cold leads. Accuracy improved by 43% over rule-based sorting pipelines.</p>",
    "comment_id": "68024d75a9db3b2a9cc5d043",
    "feature_image": null,
    "featured": false,
    "visibility": "public",
    "created_at": "2025-04-18 13:02:45",
    "updated_at": "2025-04-18 13:03:04",
    "published_at": "2025-04-18 13:03:04",
    "custom_excerpt": "Used LLM orchestration to rank and qualify sales leads with dynamic input vectors.\n",
    "codeinjection_head": null,
    "codeinjection_foot": null,
    "custom_template": null,
    "canonical_url": null,
    "url": "/leads-wizard-ai-driven-lead-qualification/",
    "excerpt": "Used LLM orchestration to rank and qualify sales leads with dynamic input vectors.\n",
    "reading_time": 1,
    "access": "public",
    "authors": [
      {
        "id": "1",
        "name": "Approx",
        "slug": "approx",
        "profile_image": null,
        "cover_image": null,
        "bio": null,
        "website": null,
        "location": null,
        "facebook": null,
        "twitter": null,
        "meta_title": null,
        "meta_description": null,
        "url": "/author/approx/"
      }
    ],
    "primary_author": {
      "id": "1",
      "name": "Approx",
      "slug": "approx",
      "profile_image": null,
      "cover_image": null,
      "bio": null,
      "website": null,
      "location": null,
      "facebook": null,
      "twitter": null,
      "meta_title": null,
      "meta_description": null,
      "url": "/author/approx/"
    },
    "tags": [
      {
        "id": "68024d59a9db3b2a9cc5d038",
        "name": "AI",
        "slug": "ai",
        "description": null,
        "feature_image": null,
        "visibility": "public",
        "meta_title": null,
        "meta_description": null,
        "og_image": null,
        "og_title": null,
        "og_description": null,
        "twitter_image": null,
        "twitter_title": null,
        "twitter_description": null,
        "codeinjection_head": null,
        "codeinjection_foot": null,
        "canonical_url": null,
        "accent_color": null,
        "url": "/tag/ai/"
      },
      {
        "id": "68024c27a9db3b2a9cc5d004",
        "name": "work",
        "slug": "work",
        "description": null,
        "feature_image": null,
        "visibility": "public",
        "meta_title": null,
        "meta_description": null,
        "og_image": null,
        "og_title": null,
        "og_description": null,
        "twitter_image": null,
        "twitter_title": null,
        "twitter_description": null,
        "codeinjection_head": null,
        "codeinjection_foot": null,
        "canonical_url": null,
        "accent_color": "#ff0f0f",
        "url": "/tag/work/"
      }
    ],
    "primary_tag": {
      "id": "68024d59a9db3b2a9cc5d038",
      "name": "AI",
      "slug": "ai",
      "description": null,
      "feature_image": null,
      "visibility": "public",
      "meta_title": null,
      "meta_description": null,
      "og_image": null,
      "og_title": null,
      "og_description": null,
      "twitter_image": null,
      "twitter_title": null,
      "twitter_description": null,
      "codeinjection_head": null,
      "codeinjection_foot": null,
      "canonical_url": null,
      "accent_color": null,
      "url": "/tag/ai/"
    }
  },
  {
    "id": "68024d47a9db3b2a9cc5d031",
    "uuid": "7c5640c8-1d70-4a75-a6a5-e1978eb13a0f",
    "title": "Recursive Self-Referential Compression (RSRC): AI’s Survival Map",
    "slug": "recursive-self-referential-compression-rsrc-ais-survival-map",
    "html": "<h2 id=\"abstract\">Abstract</h2><p>RSRC introduces two key metrics: RSRCt (training efficiency) and RSRCi (inference efficiency). These offer a principled way to evaluate and optimize AI systems beyond just performance scores.</p><ul><li><strong>RSRCt:</strong> Captures cost, compression, and age-weighted training throughput.</li><li><strong>RSRCi:</strong> Measures real-time inference cost across tasks.</li></ul><p>Published under the MURST Initiative, this framework enables a shift toward sustainability in foundation models.</p>",
    "comment_id": "68024d47a9db3b2a9cc5d031",
    "feature_image": null,
    "featured": false,
    "visibility": "public",
    "created_at": "2025-04-18 13:01:59",
    "updated_at": "2025-04-18 13:02:37",
    "published_at": "2025-04-18 13:02:37",
    "custom_excerpt": "A dual-metric framework for sustainable AI that prioritizes efficiency over brute-force scaling.",
    "codeinjection_head": null,
    "codeinjection_foot": null,
    "custom_template": null,
    "canonical_url": null,
    "url": "/recursive-self-referential-compression-rsrc-ais-survival-map/",
    "excerpt": "A dual-metric framework for sustainable AI that prioritizes efficiency over brute-force scaling.",
    "reading_time": 1,
    "access": "public",
    "authors": [
      {
        "id": "1",
        "name": "Approx",
        "slug": "approx",
        "profile_image": null,
        "cover_image": null,
        "bio": null,
        "website": null,
        "location": null,
        "facebook": null,
        "twitter": null,
        "meta_title": null,
        "meta_description": null,
        "url": "/author/approx/"
      }
    ],
    "primary_author": {
      "id": "1",
      "name": "Approx",
      "slug": "approx",
      "profile_image": null,
      "cover_image": null,
      "bio": null,
      "website": null,
      "location": null,
      "facebook": null,
      "twitter": null,
      "meta_title": null,
      "meta_description": null,
      "url": "/author/approx/"
    },
    "tags": [
      {
        "id": "68024d59a9db3b2a9cc5d038",
        "name": "AI",
        "slug": "ai",
        "description": null,
        "feature_image": null,
        "visibility": "public",
        "meta_title": null,
        "meta_description": null,
        "og_image": null,
        "og_title": null,
        "og_description": null,
        "twitter_image": null,
        "twitter_title": null,
        "twitter_description": null,
        "codeinjection_head": null,
        "codeinjection_foot": null,
        "canonical_url": null,
        "accent_color": null,
        "url": "/tag/ai/"
      },
      {
        "id": "68024d59a9db3b2a9cc5d039",
        "name": "RSRC",
        "slug": "rsrc",
        "description": null,
        "feature_image": null,
        "visibility": "public",
        "meta_title": null,
        "meta_description": null,
        "og_image": null,
        "og_title": null,
        "og_description": null,
        "twitter_image": null,
        "twitter_title": null,
        "twitter_description": null,
        "codeinjection_head": null,
        "codeinjection_foot": null,
        "canonical_url": null,
        "accent_color": null,
        "url": "/tag/rsrc/"
      },
      {
        "id": "68024c27a9db3b2a9cc5d004",
        "name": "work",
        "slug": "work",
        "description": null,
        "feature_image": null,
        "visibility": "public",
        "meta_title": null,
        "meta_description": null,
        "og_image": null,
        "og_title": null,
        "og_description": null,
        "twitter_image": null,
        "twitter_title": null,
        "twitter_description": null,
        "codeinjection_head": null,
        "codeinjection_foot": null,
        "canonical_url": null,
        "accent_color": "#ff0f0f",
        "url": "/tag/work/"
      }
    ],
    "primary_tag": {
      "id": "68024d59a9db3b2a9cc5d038",
      "name": "AI",
      "slug": "ai",
      "description": null,
      "feature_image": null,
      "visibility": "public",
      "meta_title": null,
      "meta_description": null,
      "og_image": null,
      "og_title": null,
      "og_description": null,
      "twitter_image": null,
      "twitter_title": null,
      "twitter_description": null,
      "codeinjection_head": null,
      "codeinjection_foot": null,
      "canonical_url": null,
      "accent_color": null,
      "url": "/tag/ai/"
    }
  },
  {
    "id": "68024d2ea9db3b2a9cc5d026",
    "uuid": "ad757d58-7a6b-4070-8bd1-217ac38dda03",
    "title": "Dream Log – Synesthetic Ocean",
    "slug": "dream-log-synesthetic-ocean",
    "html": "<p>Was floating above a phosphorescent ocean, but each wave had a frequency. I heard sound as color, felt motion as thought. Might represent how I subconsciously bind domains.</p>",
    "comment_id": "68024d2ea9db3b2a9cc5d026",
    "feature_image": null,
    "featured": false,
    "visibility": "public",
    "created_at": "2025-04-18 13:01:34",
    "updated_at": "2025-04-18 13:01:47",
    "published_at": "2025-04-18 13:01:47",
    "custom_excerpt": "Vivid, symbolic dream with layered sensory patterns.",
    "codeinjection_head": null,
    "codeinjection_foot": null,
    "custom_template": null,
    "canonical_url": null,
    "url": "/dream-log-synesthetic-ocean/",
    "excerpt": "Vivid, symbolic dream with layered sensory patterns.",
    "reading_time": 1,
    "access": "public",
    "authors": [
      {
        "id": "1",
        "name": "Approx",
        "slug": "approx",
        "profile_image": null,
        "cover_image": null,
        "bio": null,
        "website": null,
        "location": null,
        "facebook": null,
        "twitter": null,
        "meta_title": null,
        "meta_description": null,
        "url": "/author/approx/"
      }
    ],
    "primary_author": {
      "id": "1",
      "name": "Approx",
      "slug": "approx",
      "profile_image": null,
      "cover_image": null,
      "bio": null,
      "website": null,
      "location": null,
      "facebook": null,
      "twitter": null,
      "meta_title": null,
      "meta_description": null,
      "url": "/author/approx/"
    },
    "tags": [
      {
        "id": "68024c08a9db3b2a9cc5d001",
        "name": "archive",
        "slug": "archive",
        "description": null,
        "feature_image": null,
        "visibility": "public",
        "meta_title": null,
        "meta_description": null,
        "og_image": null,
        "og_title": null,
        "og_description": null,
        "twitter_image": null,
        "twitter_title": null,
        "twitter_description": null,
        "codeinjection_head": null,
        "codeinjection_foot": null,
        "canonical_url": null,
        "accent_color": "#715ff7",
        "url": "/tag/archive/"
      }
    ],
    "primary_tag": {
      "id": "68024c08a9db3b2a9cc5d001",
      "name": "archive",
      "slug": "archive",
      "description": null,
      "feature_image": null,
      "visibility": "public",
      "meta_title": null,
      "meta_description": null,
      "og_image": null,
      "og_title": null,
      "og_description": null,
      "twitter_image": null,
      "twitter_title": null,
      "twitter_description": null,
      "codeinjection_head": null,
      "codeinjection_foot": null,
      "canonical_url": null,
      "accent_color": "#715ff7",
      "url": "/tag/archive/"
    }
  },
  {
    "id": "68024d0aa9db3b2a9cc5d01b",
    "uuid": "029792b3-498b-40ba-870b-8c17de0dcec6",
    "title": "Flow State Diary – Day 1",
    "slug": "flow-state-diary-day-1",
    "html": "<p>Intentional silence after high-focus work generated emotional residue. No music, no scrolling. Flow lingered. Writing this to trace what triggers it best.</p>",
    "comment_id": "68024d0aa9db3b2a9cc5d01b",
    "feature_image": null,
    "featured": false,
    "visibility": "public",
    "created_at": "2025-04-18 13:00:58",
    "updated_at": "2025-04-18 13:01:22",
    "published_at": "2025-04-18 13:01:22",
    "custom_excerpt": "First log entry of conscious flow manipulation.",
    "codeinjection_head": null,
    "codeinjection_foot": null,
    "custom_template": null,
    "canonical_url": null,
    "url": "/flow-state-diary-day-1/",
    "excerpt": "First log entry of conscious flow manipulation.",
    "reading_time": 1,
    "access": "public",
    "authors": [
      {
        "id": "1",
        "name": "Approx",
        "slug": "approx",
        "profile_image": null,
        "cover_image": null,
        "bio": null,
        "website": null,
        "location": null,
        "facebook": null,
        "twitter": null,
        "meta_title": null,
        "meta_description": null,
        "url": "/author/approx/"
      }
    ],
    "primary_author": {
      "id": "1",
      "name": "Approx",
      "slug": "approx",
      "profile_image": null,
      "cover_image": null,
      "bio": null,
      "website": null,
      "location": null,
      "facebook": null,
      "twitter": null,
      "meta_title": null,
      "meta_description": null,

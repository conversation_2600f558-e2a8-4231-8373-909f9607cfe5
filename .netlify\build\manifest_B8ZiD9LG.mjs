import '@astrojs/internal-helpers/path';
import 'cookie';
import 'kleur/colors';
import 'es-module-lexer';
import { N as NOOP_MIDDLEWARE_HEADER, k as decodeKey } from './chunks/astro/server_Dba0FyIl.mjs';
import 'clsx';
import 'html-escaper';

const NOOP_MIDDLEWARE_FN = async (_ctx, next) => {
  const response = await next();
  response.headers.set(NOOP_MIDDLEWARE_HEADER, "true");
  return response;
};

const codeToStatusMap = {
  // Implemented from tRPC error code table
  // https://trpc.io/docs/server/error-handling#error-codes
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  TIMEOUT: 405,
  CONFLICT: 409,
  PRECONDITION_FAILED: 412,
  PAYLOAD_TOO_LARGE: 413,
  UNSUPPORTED_MEDIA_TYPE: 415,
  UNPROCESSABLE_CONTENT: 422,
  TOO_MANY_REQUESTS: 429,
  CLIENT_CLOSED_REQUEST: 499,
  INTERNAL_SERVER_ERROR: 500
};
Object.entries(codeToStatusMap).reduce(
  // reverse the key-value pairs
  (acc, [key, value]) => ({ ...acc, [value]: key }),
  {}
);

function sanitizeParams(params) {
  return Object.fromEntries(
    Object.entries(params).map(([key, value]) => {
      if (typeof value === "string") {
        return [key, value.normalize().replace(/#/g, "%23").replace(/\?/g, "%3F")];
      }
      return [key, value];
    })
  );
}
function getParameter(part, params) {
  if (part.spread) {
    return params[part.content.slice(3)] || "";
  }
  if (part.dynamic) {
    if (!params[part.content]) {
      throw new TypeError(`Missing parameter: ${part.content}`);
    }
    return params[part.content];
  }
  return part.content.normalize().replace(/\?/g, "%3F").replace(/#/g, "%23").replace(/%5B/g, "[").replace(/%5D/g, "]");
}
function getSegment(segment, params) {
  const segmentPath = segment.map((part) => getParameter(part, params)).join("");
  return segmentPath ? "/" + segmentPath : "";
}
function getRouteGenerator(segments, addTrailingSlash) {
  return (params) => {
    const sanitizedParams = sanitizeParams(params);
    let trailing = "";
    if (addTrailingSlash === "always" && segments.length) {
      trailing = "/";
    }
    const path = segments.map((segment) => getSegment(segment, sanitizedParams)).join("") + trailing;
    return path || "/";
  };
}

function deserializeRouteData(rawRouteData) {
  return {
    route: rawRouteData.route,
    type: rawRouteData.type,
    pattern: new RegExp(rawRouteData.pattern),
    params: rawRouteData.params,
    component: rawRouteData.component,
    generate: getRouteGenerator(rawRouteData.segments, rawRouteData._meta.trailingSlash),
    pathname: rawRouteData.pathname || void 0,
    segments: rawRouteData.segments,
    prerender: rawRouteData.prerender,
    redirect: rawRouteData.redirect,
    redirectRoute: rawRouteData.redirectRoute ? deserializeRouteData(rawRouteData.redirectRoute) : void 0,
    fallbackRoutes: rawRouteData.fallbackRoutes.map((fallback) => {
      return deserializeRouteData(fallback);
    }),
    isIndex: rawRouteData.isIndex
  };
}

function deserializeManifest(serializedManifest) {
  const routes = [];
  for (const serializedRoute of serializedManifest.routes) {
    routes.push({
      ...serializedRoute,
      routeData: deserializeRouteData(serializedRoute.routeData)
    });
    const route = serializedRoute;
    route.routeData = deserializeRouteData(serializedRoute.routeData);
  }
  const assets = new Set(serializedManifest.assets);
  const componentMetadata = new Map(serializedManifest.componentMetadata);
  const inlinedScripts = new Map(serializedManifest.inlinedScripts);
  const clientDirectives = new Map(serializedManifest.clientDirectives);
  const serverIslandNameMap = new Map(serializedManifest.serverIslandNameMap);
  const key = decodeKey(serializedManifest.key);
  return {
    // in case user middleware exists, this no-op middleware will be reassigned (see plugin-ssr.ts)
    middleware() {
      return { onRequest: NOOP_MIDDLEWARE_FN };
    },
    ...serializedManifest,
    assets,
    componentMetadata,
    inlinedScripts,
    clientDirectives,
    routes,
    serverIslandNameMap,
    key
  };
}

const manifest = deserializeManifest({"hrefRoot":"file:///C:/Users/<USER>/Desktop/pvb-astro/","adapterName":"@astrojs/netlify","routes":[{"file":"about/index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/about","isIndex":false,"type":"page","pattern":"^\\/about\\/?$","segments":[[{"content":"about","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/about.astro","pathname":"/about","prerender":true,"fallbackRoutes":[],"_meta":{"trailingSlash":"ignore"}}},{"file":"blog/timeline/index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/blog/timeline","isIndex":false,"type":"page","pattern":"^\\/blog\\/timeline\\/?$","segments":[[{"content":"blog","dynamic":false,"spread":false}],[{"content":"timeline","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/blog/timeline.astro","pathname":"/blog/timeline","prerender":true,"fallbackRoutes":[],"_meta":{"trailingSlash":"ignore"}}},{"file":"blog/index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/blog","isIndex":false,"type":"page","pattern":"^\\/blog\\/?$","segments":[[{"content":"blog","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/blog.astro","pathname":"/blog","prerender":true,"fallbackRoutes":[],"_meta":{"trailingSlash":"ignore"}}},{"file":"search/index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/search","isIndex":false,"type":"page","pattern":"^\\/search\\/?$","segments":[[{"content":"search","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/search.astro","pathname":"/search","prerender":true,"fallbackRoutes":[],"_meta":{"trailingSlash":"ignore"}}},{"file":"tags/index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/tags","isIndex":true,"type":"page","pattern":"^\\/tags\\/?$","segments":[[{"content":"tags","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/tags/index.astro","pathname":"/tags","prerender":true,"fallbackRoutes":[],"_meta":{"trailingSlash":"ignore"}}},{"file":"work/index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/work","isIndex":false,"type":"page","pattern":"^\\/work\\/?$","segments":[[{"content":"work","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/work.astro","pathname":"/work","prerender":true,"fallbackRoutes":[],"_meta":{"trailingSlash":"ignore"}}},{"file":"index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/","isIndex":true,"type":"page","pattern":"^\\/$","segments":[],"params":[],"component":"src/pages/index.astro","pathname":"/","prerender":true,"fallbackRoutes":[],"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"type":"endpoint","isIndex":false,"route":"/_image","pattern":"^\\/_image$","segments":[[{"content":"_image","dynamic":false,"spread":false}]],"params":[],"component":"node_modules/astro/dist/assets/endpoint/generic.js","pathname":"/_image","prerender":false,"fallbackRoutes":[],"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"route":"/api/random-quote.json","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/random-quote\\.json\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"random-quote.json","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/random-quote.json.js","pathname":"/api/random-quote.json","prerender":false,"fallbackRoutes":[],"_meta":{"trailingSlash":"ignore"}}}],"site":"https://pvb.com","base":"/","trailingSlash":"ignore","compressHTML":true,"componentMetadata":[["C:/Users/<USER>/Desktop/pvb-astro/src/components/BlogPostCard.astro",{"propagation":"in-tree","containsHead":false}],["C:/Users/<USER>/Desktop/pvb-astro/src/pages/tags/[tag]/index.astro",{"propagation":"in-tree","containsHead":true}],["\u0000@astro-page:src/pages/tags/[tag]/index@_@astro",{"propagation":"in-tree","containsHead":false}],["\u0000@astrojs-ssr-virtual-entry",{"propagation":"in-tree","containsHead":false}],["C:/Users/<USER>/Desktop/pvb-astro/src/components/ProjectCard.astro",{"propagation":"in-tree","containsHead":false}],["C:/Users/<USER>/Desktop/pvb-astro/src/pages/work.astro",{"propagation":"in-tree","containsHead":true}],["\u0000@astro-page:src/pages/work@_@astro",{"propagation":"in-tree","containsHead":false}],["C:/Users/<USER>/Desktop/pvb-astro/src/layouts/BlogPost.astro",{"propagation":"in-tree","containsHead":false}],["C:/Users/<USER>/Desktop/pvb-astro/src/pages/blog/[slug].astro",{"propagation":"in-tree","containsHead":true}],["\u0000@astro-page:src/pages/blog/[slug]@_@astro",{"propagation":"in-tree","containsHead":false}],["C:/Users/<USER>/Desktop/pvb-astro/src/pages/work/[...slug].astro",{"propagation":"in-tree","containsHead":true}],["\u0000@astro-page:src/pages/work/[...slug]@_@astro",{"propagation":"in-tree","containsHead":false}],["\u0000astro:content",{"propagation":"in-tree","containsHead":false}],["C:/Users/<USER>/Desktop/pvb-astro/src/pages/api/random-quote.json.js",{"propagation":"in-tree","containsHead":false}],["\u0000@astro-page:src/pages/api/random-quote.json@_@js",{"propagation":"in-tree","containsHead":false}],["C:/Users/<USER>/Desktop/pvb-astro/src/pages/blog.astro",{"propagation":"in-tree","containsHead":true}],["\u0000@astro-page:src/pages/blog@_@astro",{"propagation":"in-tree","containsHead":false}],["C:/Users/<USER>/Desktop/pvb-astro/src/pages/blog/tag/[tag].astro",{"propagation":"in-tree","containsHead":true}],["\u0000@astro-page:src/pages/blog/tag/[tag]@_@astro",{"propagation":"in-tree","containsHead":false}],["C:/Users/<USER>/Desktop/pvb-astro/src/pages/blog/timeline.astro",{"propagation":"in-tree","containsHead":true}],["\u0000@astro-page:src/pages/blog/timeline@_@astro",{"propagation":"in-tree","containsHead":false}],["C:/Users/<USER>/Desktop/pvb-astro/src/pages/tags/index.astro",{"propagation":"in-tree","containsHead":true}],["\u0000@astro-page:src/pages/tags/index@_@astro",{"propagation":"in-tree","containsHead":false}],["C:/Users/<USER>/Desktop/pvb-astro/src/pages/about.astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/Desktop/pvb-astro/src/pages/index.astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/Desktop/pvb-astro/src/pages/search.astro",{"propagation":"none","containsHead":true}]],"renderers":[],"clientDirectives":[["idle","(()=>{var l=(o,t)=>{let i=async()=>{await(await o())()},e=typeof t.value==\"object\"?t.value:void 0,s={timeout:e==null?void 0:e.timeout};\"requestIdleCallback\"in window?window.requestIdleCallback(i,s):setTimeout(i,s.timeout||200)};(self.Astro||(self.Astro={})).idle=l;window.dispatchEvent(new Event(\"astro:idle\"));})();"],["load","(()=>{var e=async t=>{await(await t())()};(self.Astro||(self.Astro={})).load=e;window.dispatchEvent(new Event(\"astro:load\"));})();"],["media","(()=>{var s=(i,t)=>{let a=async()=>{await(await i())()};if(t.value){let e=matchMedia(t.value);e.matches?a():e.addEventListener(\"change\",a,{once:!0})}};(self.Astro||(self.Astro={})).media=s;window.dispatchEvent(new Event(\"astro:media\"));})();"],["only","(()=>{var e=async t=>{await(await t())()};(self.Astro||(self.Astro={})).only=e;window.dispatchEvent(new Event(\"astro:only\"));})();"],["visible","(()=>{var l=(s,i,o)=>{let r=async()=>{await(await s())()},t=typeof i.value==\"object\"?i.value:void 0,c={rootMargin:t==null?void 0:t.rootMargin},n=new IntersectionObserver(e=>{for(let a of e)if(a.isIntersecting){n.disconnect(),r();break}},c);for(let e of o.children)n.observe(e)};(self.Astro||(self.Astro={})).visible=l;window.dispatchEvent(new Event(\"astro:visible\"));})();"]],"entryModules":{"\u0000noop-middleware":"_noop-middleware.mjs","\u0000@astro-page:node_modules/astro/dist/assets/endpoint/generic@_@js":"pages/_image.astro.mjs","\u0000@astro-page:src/pages/about@_@astro":"pages/about.astro.mjs","\u0000@astro-page:src/pages/api/random-quote.json@_@js":"pages/api/random-quote.json.astro.mjs","\u0000@astro-page:src/pages/blog/tag/[tag]@_@astro":"pages/blog/tag/_tag_.astro.mjs","\u0000@astro-page:src/pages/blog/timeline@_@astro":"pages/blog/timeline.astro.mjs","\u0000@astro-page:src/pages/blog/[slug]@_@astro":"pages/blog/_slug_.astro.mjs","\u0000@astro-page:src/pages/blog@_@astro":"pages/blog.astro.mjs","\u0000@astro-page:src/pages/search@_@astro":"pages/search.astro.mjs","\u0000@astro-page:src/pages/tags/[tag]/index@_@astro":"pages/tags/_tag_.astro.mjs","\u0000@astro-page:src/pages/tags/index@_@astro":"pages/tags.astro.mjs","\u0000@astro-page:src/pages/work@_@astro":"pages/work.astro.mjs","\u0000@astro-page:src/pages/work/[...slug]@_@astro":"pages/work/_---slug_.astro.mjs","\u0000@astro-page:src/pages/index@_@astro":"pages/index.astro.mjs","\u0000@astrojs-ssr-virtual-entry":"entry.mjs","\u0000@astro-renderers":"renderers.mjs","\u0000@astrojs-ssr-adapter":"<EMAIL>","\u0000@astrojs-manifest":"manifest_B8ZiD9LG.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/blog/age-of-synthesis.md?astroContentCollectionEntry=true":"chunks/age-of-synthesis_B-VSexue.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/blog/hello-world.md?astroContentCollectionEntry=true":"chunks/hello-world_BwOf8HZz.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/blog/mastery-and-knowledge.md?astroContentCollectionEntry=true":"chunks/mastery-and-knowledge_CkJKa8VC.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/quotes/learning.md?astroContentCollectionEntry=true":"chunks/learning_DcKpeM5O.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/quotes/mindfulness.md?astroContentCollectionEntry=true":"chunks/mindfulness_DTX3H095.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/quotes/philosophy.md?astroContentCollectionEntry=true":"chunks/philosophy_fXVfYPzN.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-five.md?astroContentCollectionEntry=true":"chunks/project-five_BCHmoapR.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-four.md?astroContentCollectionEntry=true":"chunks/project-four_BiijU9ph.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-one.md?astroContentCollectionEntry=true":"chunks/project-one_kU0mYB6U.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-three.md?astroContentCollectionEntry=true":"chunks/project-three_C8CEKmI8.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-two.md?astroContentCollectionEntry=true":"chunks/project-two_BbjtZZaS.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/blog/age-of-synthesis.md?astroPropagatedAssets":"chunks/age-of-synthesis_DqChI2cq.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/blog/hello-world.md?astroPropagatedAssets":"chunks/hello-world_BNhj20-t.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/blog/mastery-and-knowledge.md?astroPropagatedAssets":"chunks/mastery-and-knowledge_B3L9fdmh.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/quotes/learning.md?astroPropagatedAssets":"chunks/learning_SfTw29v4.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/quotes/mindfulness.md?astroPropagatedAssets":"chunks/mindfulness_BG5BPaMj.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/quotes/philosophy.md?astroPropagatedAssets":"chunks/philosophy_D9CDMOq1.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-five.md?astroPropagatedAssets":"chunks/project-five_B9I66poG.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-four.md?astroPropagatedAssets":"chunks/project-four_DlJCn5JQ.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-one.md?astroPropagatedAssets":"chunks/project-one_BLBOXsjf.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-three.md?astroPropagatedAssets":"chunks/project-three_CCeq-lTS.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-two.md?astroPropagatedAssets":"chunks/project-two_CMkRBYFL.mjs","\u0000astro:asset-imports":"chunks/_astro_asset-imports_D9aVaOQr.mjs","\u0000astro:data-layer-content":"chunks/_astro_data-layer-content_BcEe_9wP.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/blog/age-of-synthesis.md":"chunks/age-of-synthesis_DLAOmwU2.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/blog/hello-world.md":"chunks/hello-world_Bu55fqv5.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/blog/mastery-and-knowledge.md":"chunks/mastery-and-knowledge_C1zLMmNr.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/quotes/learning.md":"chunks/learning_BtzpT67X.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/quotes/mindfulness.md":"chunks/mindfulness_3c8gSlQq.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/quotes/philosophy.md":"chunks/philosophy_EjjKZrv8.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-five.md":"chunks/project-five_bFgqsuz9.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-four.md":"chunks/project-four_BWkJ1E-x.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-one.md":"chunks/project-one_Cse1VnLC.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-three.md":"chunks/project-three_Dplria35.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-two.md":"chunks/project-two_BWh9uzc8.mjs","/astro/hoisted.js?q=0":"_astro/hoisted.C6XmR4Gm.js","/astro/hoisted.js?q=1":"_astro/hoisted.DSoxCwEA.js","astro:scripts/before-hydration.js":""},"inlinedScripts":[],"assets":["/_astro/_slug_.CLq3GFE2.css","/_astro/_slug_.DONxtleH.css","/_astro/_tag_.Rb4hvsWq.css","/_astro/blog.DWMZHUHN.css","/_astro/work.DbR1XWHN.css","/fonts/georgia-bold-2.ttf","/fonts/georgia-ref.ttf","/fonts/serif12-beta-regular.otf","/images/about.png","/images/blackgranite.jpg","/images/blackgranite.png","/images/concrete.png","/images/OIP.jpg","/images/whitemarble.png","/scripts/main.js","/about/index.html","/blog/timeline/index.html","/blog/index.html","/search/index.html","/tags/index.html","/work/index.html","/index.html"],"buildFormat":"directory","checkOrigin":false,"serverIslandNameMap":[],"key":"V6qj4omlYy4jp+5WfqxFNW2LDQ1C8N0L3oJFFy0D9Q8=","experimentalEnvGetSecretEnabled":false});

export { manifest };

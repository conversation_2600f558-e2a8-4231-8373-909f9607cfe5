import { c as createAstro, a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead, b as addAttribute } from '../../../chunks/astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import { g as getCollection } from '../../../chunks/_astro_content_CcaQj6Wl.mjs';
import { $ as $$Layout } from '../../../chunks/Layout_rXbp99fE.mjs';
/* empty css                                       */
export { renderers } from '../../../renderers.mjs';

const $$Astro = createAstro("https://pvb.com");
async function getStaticPaths() {
  try {
    const allPosts = await getCollection("blog", ({ data }) => !data.draft);
    const allTags = [...new Set(allPosts.flatMap((post) => post.data.tags || []))];
    const tags = allTags.length > 0 ? allTags : [
      "philosophy",
      "wisdom",
      "mastery",
      "AI",
      "synthesis",
      "innovation",
      "strategy",
      "introduction",
      "blogging"
    ];
    return tags.map((tag) => ({
      params: { tag },
      props: { tag }
    }));
  } catch (error) {
    console.error("Error in getStaticPaths:", error);
    const fallbackTags = [
      "philosophy",
      "wisdom",
      "mastery",
      "AI",
      "synthesis",
      "innovation",
      "strategy",
      "introduction",
      "blogging"
    ];
    return fallbackTags.map((tag) => ({
      params: { tag },
      props: { tag, errorInStaticPaths: true }
    }));
  }
}
const $$tag = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$tag;
  const { tag } = Astro2.props;
  let posts = [];
  let allTags = [];
  let error = false;
  try {
    const allPosts = await getCollection("blog");
    const nonDraftPosts = allPosts.filter((post) => !post.data.draft);
    allTags = [...new Set(nonDraftPosts.flatMap((post) => post.data.tags))].sort();
    posts = nonDraftPosts.filter((post) => post.data.tags.includes(tag));
    posts = posts.sort((a, b) => {
      const dateA = new Date(a.data.pubDatetime).getTime();
      const dateB = new Date(b.data.pubDatetime).getTime();
      return dateB - dateA;
    });
  } catch (e) {
    console.error("Error fetching posts:", e);
    error = true;
  }
  return renderTemplate`${error ? renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "pageTitle": "Tag Error | Blog | PVB", "isHomePage": false, "accentColor": "#f0f0f0", "bgColor": "rgba(10, 10, 10, 0.94)", "backgroundImageUrl": "/images/blackgranite.png", "bodyDataPage": "blog", "data-astro-cid-trjsnkp3": true }, { "default": async ($$result2) => renderTemplate`${maybeRenderHead()}<div class="error-container" data-astro-cid-trjsnkp3><h1 data-astro-cid-trjsnkp3>Error Loading Tag</h1><p data-astro-cid-trjsnkp3>Sorry, there was an error loading posts for this tag.</p><a href="/blog" class="back-to-blog" data-astro-cid-trjsnkp3>&larr; back to blog</a></div><style>
      .error-container {
        max-width: 600px;
        margin: 100px auto;
        text-align: center;
        padding: 0 20px;
      }

      h1 {
        font-size: 1.8rem;
        color: rgba(240, 240, 240, 0.9);
        margin-bottom: 20px;
        font-family: 'Georgia Custom', Georgia, serif;
      }

      p {
        font-size: 1.1rem;
        color: rgba(220, 220, 220, 0.8);
        margin-bottom: 30px;
        font-family: 'Georgia Custom', Georgia, serif;
      }

      .back-to-blog {
        font-family: 'Georgia Custom', Georgia, serif;
        font-size: 1rem;
        color: rgba(200, 200, 200, 0.8);
        text-decoration: none;
        transition: color 0.3s ease, transform 0.3s ease;
        display: inline-block;
      }

      .back-to-blog:hover {
        color: rgba(240, 240, 240, 1);
        transform: translateX(-3px);
      }
    </style>` })}` : renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "pageTitle": `${tag} | Blog | PVB`, "isHomePage": false, "accentColor": "#f0f0f0", "bgColor": "rgba(10, 10, 10, 0.94)", "backgroundImageUrl": "/images/blackgranite.png", "bodyDataPage": "blog", "data-astro-cid-trjsnkp3": true }, { "default": async ($$result2) => renderTemplate`<div class="blog-header" data-astro-cid-trjsnkp3><div class="blog-title" data-astro-cid-trjsnkp3>#${tag}</div></div><div class="page-container" data-astro-cid-trjsnkp3><!-- Left Sidebar --><div class="blog-sidebar" data-astro-cid-trjsnkp3><!-- Back to Blog --><div class="sidebar-section" data-astro-cid-trjsnkp3><a href="/blog" class="back-to-blog" data-astro-cid-trjsnkp3>&larr; back to blog</a></div><!-- Tags Filter --><div class="tags-container sidebar-section" data-astro-cid-trjsnkp3><div class="tags-title" data-astro-cid-trjsnkp3>other tags:</div><div class="tags-list" data-astro-cid-trjsnkp3>${allTags.filter((t) => t !== tag).map((otherTag) => renderTemplate`<a${addAttribute(`/blog/tag/${otherTag}`, "href")} class="tag-link" data-astro-cid-trjsnkp3>${otherTag}</a>`)}</div></div></div><!-- Main Content --><div class="blog-content" data-astro-cid-trjsnkp3><div class="tag-header" data-astro-cid-trjsnkp3><h2 class="tag-heading" data-astro-cid-trjsnkp3>Posts tagged with "${tag}"</h2><div class="post-count" data-astro-cid-trjsnkp3>${posts.length} post${posts.length !== 1 ? "s" : ""}</div></div><!-- Posts List --><div class="tag-posts" data-astro-cid-trjsnkp3>${posts.length > 0 ? posts.map((post) => renderTemplate`<div class="post-item" data-astro-cid-trjsnkp3><h3 class="post-item-title" data-astro-cid-trjsnkp3><a${addAttribute(`/blog/${post.slug}`, "href")} data-astro-cid-trjsnkp3>${post.data.title}</a></h3><div class="post-item-date" data-astro-cid-trjsnkp3>${new Date(post.data.pubDatetime).toLocaleDateString("en-US", { day: "numeric", month: "long", year: "numeric" })}</div><p class="post-item-description" data-astro-cid-trjsnkp3>${post.data.description}</p><a${addAttribute(`/blog/${post.slug}`, "href")} class="post-item-read-more" data-astro-cid-trjsnkp3>read</a></div>`) : renderTemplate`<div class="no-posts" data-astro-cid-trjsnkp3><p data-astro-cid-trjsnkp3>No posts found with this tag.</p></div>`}</div></div></div>` })}`}`;
}, "C:/Users/<USER>/Desktop/pvb-astro/src/pages/blog/tag/[tag].astro", void 0);

const $$file = "C:/Users/<USER>/Desktop/pvb-astro/src/pages/blog/tag/[tag].astro";
const $$url = "/blog/tag/[tag]";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$tag,
  file: $$file,
  getStaticPaths,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

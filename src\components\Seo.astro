---
import settings from '../data/ghost/settings.json';

export interface Props {
  title?: string;
  description?: string;
  ogImage?: string;
  canonicalURL?: URL | string;
  publishedDate?: string;
  // Add any other props you need
}

const { 
    title = settings.settings.title,
    description = settings.settings.description,
    ogImage = settings.settings.cover_image,
    canonicalURL = new URL(Astro.url.pathname, Astro.site),
    publishedDate
} = Astro.props;

const formattedTitle = `${title} | ${settings.settings.title}`;
const imageURL = ogImage ? new URL(ogImage, Astro.site).href : new URL('/placeholder.jpg', Astro.site).href;
---
<meta charset="utf-8" />
<title>{formattedTitle}</title>
<meta name="description" content={description} />
<link rel="canonical" href={canonicalURL} />

<meta property="og:title" content={title} />
<meta property="og:description" content={description} />
<meta property="og:url" content={canonicalURL} />
<meta property="og:site_name" content={settings.settings.title} />
<meta property="og:image" content={imageURL} />
<meta property="og:type" content={publishedDate ? "article" : "website"} />
{publishedDate && <meta property="article:published_time" content={publishedDate} />}

<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content={title} />
<meta name="twitter:description" content={description} />
<meta name="twitter:image" content={imageURL} />

<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
<meta name="theme-color" content="#ffffff" />
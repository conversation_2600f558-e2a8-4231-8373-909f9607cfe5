/**
 * Unified Content Loader (JSON-only, Ghost export)
 *
 * Reads all content from src/data/ghost/posts.json
 * Provides filtering, pagination, timeline, and tag utilities
 */

import fs from 'fs';
import path from 'path';

const POSTS_PATH = path.resolve(process.cwd(), 'src/data/ghost/posts.json');

// Read and parse posts.json once at module load
let posts = [];
try {
  console.log('Reading posts.json...');
  const json = fs.readFileSync(POSTS_PATH, 'utf8');
  console.log('Raw posts.json content length:', json.length);
  posts = JSON.parse(json);
  console.log('Parsed posts.json - Total posts:', posts.length);
} catch (err) {
  console.error('Error loading posts.json:', err);
  posts = [];
}

// Normalize published_at and html for consistency
posts = posts.map(post => ({
  ...post,
  published_at: post.published_at || post.pubDatetime,
  html: post.html || post.mob_html || post.lex_html || ''
}));

/**
 * Get all blog posts from both <PERSON>down and Ghost
 * @returns {Promise<Array>} Combined array of blog posts
 */
export function getAllPosts() {
  // Return all posts, sorted by published_at (newest first)
  return posts.slice().sort((a, b) => new Date(b.published_at) - new Date(a.published_at));
}

/**
 * Find a post by slug from either content source
 * @param {string} slug - Post slug to find
 * @returns {Promise<Object|null>} The post object or null if not found
 */
export function getPostBySlug(slug) {
  return posts.find(post => post.slug === slug);
}

/**
 * Get all unique tags from both Markdown and Ghost posts
 * @returns {Promise<Array>} Array of unique tag names
 */
export function getAllUniqueTags() {
  console.log('Getting all unique tags...');

  // Extract tag names, handling both string tags and object tags with name property
  const allTags = posts.flatMap(post => {
    if (!Array.isArray(post.tags)) return [];

    return post.tags.map(tag => {
      // Handle Ghost format where tags are objects with a name property
      if (typeof tag === 'object' && tag !== null && tag.name) {
        console.log(`Found tag object with name: ${tag.name}`);
        return tag.name;
      }
      // Handle string tags
      if (typeof tag === 'string') {
        return tag;
      }
      // Skip invalid tags
      console.log(`Skipping invalid tag:`, tag);
      return null;
    }).filter(Boolean); // Remove null/undefined values
  });

  // Convert to lowercase and get unique values
  const uniqueTags = [...new Set(allTags.map(tag => tag.toLowerCase()))];
  console.log(`Found ${uniqueTags.length} unique tags:`, uniqueTags);
  return uniqueTags;
}

/**
 * Get all posts with a specific tag
 * @param {string} tagSlug - Tag slug to filter by
 * @returns {Promise<Array>} Array of posts with the specified tag
 */
export function getPostsByTag(tagSlug) {
  console.log(`DEBUG: getPostsByTag called for slug: "${tagSlug}". Processing ${posts?.length || 0} total posts.`);
  const normalizedTagSlug = tagSlug.toLowerCase();

  const filtered = posts.filter(post => {
    if (!Array.isArray(post.tags)) {
      return false;
    }

    // Normalize all post tag slugs
    const postTagSlugs = post.tags.map(t =>
      typeof t === 'object' && t !== null && t.slug ? t.slug.toLowerCase() :
      typeof t === 'object' && t !== null && t.name ? t.name.toLowerCase() :
      typeof t === 'string' ? t.toLowerCase() : ''
    ).filter(Boolean);

    const hasTag = postTagSlugs.includes(normalizedTagSlug);
    console.log(`DEBUG (${tagSlug}): Checking post "${post.title}". Has slugs: [${postTagSlugs.join(', ')}]. Match found: ${hasTag}`);
    return hasTag;
  });

  console.log(`DEBUG: getPostsByTag for slug: "${tagSlug}" returning ${filtered.length} posts.`);
  return filtered;
}

/**
 * Determine content type based on first tag
 * @param {Object} post - Post object
 * @returns {string} Content type ('blog', 'archive', or 'work')
 */
export function getContentType(post) {
  console.log(`Determining content type for post: "${post.title}"`);

  if (post.type && typeof post.type === 'string') {
    const type = post.type.toLowerCase();
    console.log(`  Post has explicit type: ${type}`);
    if (["archive", "work", "blog"].includes(type)) {
      console.log(`  Using explicit type: ${type}`);
      return type;
    }
  }

  if (Array.isArray(post.tags)) {
    // Extract tag names/slugs, handling both string tags and object tags
    const tagValues = post.tags.map(t => {
      if (typeof t === 'object' && t !== null) {
        // Prefer slug if available, otherwise use name
        return (t.slug || t.name || '').toLowerCase();
      }
      return typeof t === 'string' ? t.toLowerCase() : '';
    }).filter(Boolean); // Remove empty strings

    console.log(`  Post has tag values: ${JSON.stringify(tagValues)}`);

    if (tagValues.includes("archive")) {
      console.log(`  Using tag-based type: archive`);
      return "archive";
    }
    if (tagValues.includes("work") || tagValues.includes("project")) {
      console.log(`  Using tag-based type: work`);
      return "work";
    }
    if (tagValues.includes("blog")) {
      console.log(`  Using tag-based type: blog`);
      return "blog";
    }
  }

  console.log(`  No type or relevant tags found, defaulting to: blog`);
  return "blog";
}

/**
 * Get posts by content type
 * @param {string} type - Content type ('blog', 'archive', or 'work')
 * @returns {Promise<Array>} Filtered posts
 */
export function getPostsByType(type) {
  const allPosts = getAllPosts();
  console.log(`getPostsByType called for type: ${type}, processing ${allPosts.length} total posts`);

  // Debug the structure of the first post to see what we're working with
  if (allPosts.length > 0) {
    console.log('First post structure:', JSON.stringify({
      title: allPosts[0].title,
      slug: allPosts[0].slug,
      type: allPosts[0].type,
      tags: allPosts[0].tags,
      featured: allPosts[0].featured
    }, null, 2));
  }

  const filteredPosts = allPosts.filter(post => {
    const contentType = getContentType(post);
    console.log(`Post "${post.title}" has content type: ${contentType}`);
    return contentType === type;
  });

  console.log(`getPostsByType found ${filteredPosts.length} posts for type: ${type}`);
  return filteredPosts;
}

/**
 * Paginate a list of posts
 * @param {Array} posts - Array of posts to paginate
 * @param {number} currentPage - Current page number (1-based)
 * @param {number} postsPerPage - Number of posts per page
 * @returns {Object} Paginated posts and pagination info
 */
export function paginatePosts(posts, page = 1, pageSize = 5) {
  const totalPosts = posts.length;
  const totalPages = Math.max(1, Math.ceil(totalPosts / pageSize));
  const currentPage = Math.max(1, Math.min(page, totalPages));
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  return {
    posts: posts.slice(startIndex, endIndex),
    pagination: {
      currentPage,
      totalPages,
      totalPosts,
      pageSize,
      hasNextPage: currentPage < totalPages,
      hasPrevPage: currentPage > 1,
      nextPage: currentPage < totalPages ? currentPage + 1 : null,
      prevPage: currentPage > 1 ? currentPage - 1 : null
    }
  };
}

/**
 * Group posts into a timeline by year and month (newest first)
 * @param {Array} posts
 * @returns {Object} { [year]: { [month]: [posts] } }
 */
export function getTimeline(posts) {
  // Sort posts by published_at (newest first)
  const sorted = posts.slice().sort((a, b) => new Date(b.published_at) - new Date(a.published_at));
  const timeline = {};
  for (const post of sorted) {
    const date = new Date(post.published_at);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    if (!timeline[year]) timeline[year] = {};
    if (!timeline[year][month]) timeline[year][month] = [];
    timeline[year][month].push(post);
  }
  return timeline;
}

// Utility to slugify strings for tags and URLs
export function slugifyStr(str) {
  return String(str)
    .trim()
    .toLowerCase()
    .replace(/\s+/g, '-')       // Replace spaces with -
    .replace(/[^a-z0-9-\-]/g, '') // Remove invalid chars
    .replace(/-+/g, '-')          // Collapse multiple -
    .replace(/^-+|-+$/g, '');     // Trim - from ends
}

---
import Layout from "../../layouts/Layout.astro";
import Tag from "../../components/Tag.astro";
import { getAllUniqueTags } from "../../utils/unifiedContent";
import { slugifyStr } from "../../utils/slugify";
import { SITE } from "../../config";

// Get all unique tags from JSON content
const allTagNames = await getAllUniqueTags();
const tags = allTagNames.map(tagName => ({
  tag: slugifyStr(tagName),
  tagName
}));
---

<Layout
  pageTitle="Tags | PVB"
  isHomePage={false}
  accentColor="#f0f0f0"
  bgColor="rgba(0, 0, 0, 0.9)"
  backgroundImageUrl="none"
  style="background: linear-gradient(to bottom right, rgba(0, 0, 0, 0.95), rgba(50, 50, 50, 0.9))"
  bodyDataPage="tags"
>
  <div class="quote-container" style="max-width: 800px;">
    <h1 class="text-3xl font-bold mb-4 text-accent">Tags</h1>
    <p class="mb-8 text-muted">Browse posts by topic</p>

    <div class="tags-container">
      {tags.map(({ tag, tagName }) =>
        <Tag tag={tag} tagName={tagName} size="lg" />
      )}
    </div>

    <div class="mt-10">
      <a
        href="/blog"
        class="back-link"
      >
        <span class="back-arrow">←</span>
        <span>Back to blog</span>
      </a>
    </div>
  </div>
</Layout>

<style>
  .quote-container {
    margin: 0 auto;
    text-align: center;
  }
  h1 {
    font-family: 'Georgia Custom', Georgia, serif;
  }
  .text-muted {
    color: var(--color-text-secondary);
  }
  .text-accent {
    color: var(--color-accent);
  }
  .tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
    margin: 2rem 0;
  }
  .back-link {
    display: inline-flex;
    align-items: center;
    color: var(--color-accent);
    text-decoration: none;
    transition: opacity 0.2s ease;
    font-family: 'Georgia Custom', Georgia, serif;
  }
  .back-link:hover {
    opacity: 0.75;
  }
  .back-arrow {
    margin-right: 0.5rem;
  }
</style>
---
import { slugifyStr } from "../utils/slugify";

export interface Props {
  projects: any[]; // Array of projects
}

const { projects } = Astro.props;
---

<div class="utopia-container">
  <h2 class="utopia-world-title">UTOPIA WORLD</h2>
  
  <div class="utopia-grid">
    {projects.map((project) => (
      <div class="grid-item">
        <div class="project-image-container">
          <img 
            src={project.feature_image || project.cover_image || '/images/placeholder.jpg'} 
            alt={project.title}
            class="project-image"
          />
          <a href={`/work/${project.slug}`} class="explore-link">Explore</a>
        </div>
      </div>
    ))}
  </div>
</div>

<style>
  /* Utopia World styling */
  .utopia-container {
    width: 100%;
    max-width: 90rem;
    margin: 0 auto;
    padding: 2rem 1rem;
  }

  .utopia-world-title {
    font-size: 1.5rem;
    text-align: center;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 0.05em;
    margin-bottom: 3rem;
    color: #fff;
  }

  .utopia-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2px;
    width: 100%;
  }

  .grid-item {
    aspect-ratio: 1;
    overflow: hidden;
    position: relative;
  }

  .project-image-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .project-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
  }

  .explore-link {
    position: absolute;
    bottom: 1.5rem;
    left: 50%;
    transform: translateX(-50%);
    background: transparent;
    color: white;
    padding: 0.5rem 1rem;
    text-decoration: none;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 0.1em;
    opacity: 0;
    transition: opacity 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.3);
    z-index: 2;
  }

  .project-image-container:hover .project-image {
    transform: scale(1.05);
  }

  .project-image-container:hover .explore-link {
    opacity: 1;
  }

  .project-image-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.2);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .project-image-container:hover::after {
    opacity: 1;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .utopia-grid {
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
  }

  @media (max-width: 480px) {
    .utopia-grid {
      grid-template-columns: 1fr;
    }
    
    .grid-item {
      aspect-ratio: 1 / 1.2;
    }
  }
</style>
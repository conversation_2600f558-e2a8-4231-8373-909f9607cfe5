					return "\n## Mastery and Knowledge\n\nI realized something. You know, from a young age I thought that knowledge are associated with trees. And I understand now that they're talking about mastery. Because mastery isn't about knowledge. It's about knowing what's real and what isn't.\n\nBecause the tree knows that it's a tree. It's not up for debate. The tree knows it has leaves. It's not up for debate. The tree knows that it is not a lake. There's no negotiation there.\n\nSo when somebody says to the tree that you're not a tree, you're just a lake, the tree doesn't react. The tree says, no, I'm a tree. No matter if the person calls it arrogant, an old geezer, or whatever they call it, it will still reply, I am a tree. Nothing less, nothing more.\n\nThat is knowledge. It's not about knowing information. It's about knowing what's real.\n\n## The Wisdom of Certainty\n\nThis perspective shifts how we might think about expertise and learning. It's not about how many facts you can recite or how many books you've read. It's about having such a deep understanding that you recognize truth from falsehood without hesitation.\n\n## Beyond Information\n\nIn our information-saturated age, we often confuse data collection with wisdom. But the tree doesn't need to constantly update its database to know what it is. Its knowledge is embodied, certain, and unshakable.\n\n## Finding Your Truth\n\nPerhaps mastery in any field comes when you reach this level of certainty - not from arrogance, but from deep connection with reality. When you know something so thoroughly that external opinions cannot shake your understanding.\n";
				}
				function compiledContent() {
					return html;
				}
				function getHeadings() {
					return [{"depth":2,"slug":"mastery-and-knowledge","text":"Mastery and Knowledge"},{"depth":2,"slug":"the-wisdom-of-certainty","text":"The Wisdom of Certainty"},{"depth":2,"slug":"beyond-information","text":"Beyond Information"},{"depth":2,"slug":"finding-your-truth","text":"Finding Your Truth"}];
				}

				const Content = createComponent((result, _props, slots) => {
					const { layout, ...content } = frontmatter;
					content.file = file;
					content.url = url;

					return renderTemplate`${maybeRenderHead()}${unescapeHTML(html)}`;
				});

export { Content, compiledContent, Content as default, file, frontmatter, getHeadings, rawContent, url };
</file>

<file path=".netlify/build/chunks/mastery-and-knowledge_CkJKa8VC.mjs">
const id = "mastery-and-knowledge.md";
						const collection = "blog";
						const slug = "mastery-and-knowledge";
						const body = "\n## Mastery and Knowledge\n\nI realized something. You know, from a young age I thought that knowledge are associated with trees. And I understand now that they're talking about mastery. Because mastery isn't about knowledge. It's about knowing what's real and what isn't.\n\nBecause the tree knows that it's a tree. It's not up for debate. The tree knows it has leaves. It's not up for debate. The tree knows that it is not a lake. There's no negotiation there.\n\nSo when somebody says to the tree that you're not a tree, you're just a lake, the tree doesn't react. The tree says, no, I'm a tree. No matter if the person calls it arrogant, an old geezer, or whatever they call it, it will still reply, I am a tree. Nothing less, nothing more.\n\nThat is knowledge. It's not about knowing information. It's about knowing what's real.\n\n## The Wisdom of Certainty\n\nThis perspective shifts how we might think about expertise and learning. It's not about how many facts you can recite or how many books you've read. It's about having such a deep understanding that you recognize truth from falsehood without hesitation.\n\n## Beyond Information\n\nIn our information-saturated age, we often confuse data collection with wisdom. But the tree doesn't need to constantly update its database to know what it is. Its knowledge is embodied, certain, and unshakable.\n\n## Finding Your Truth\n\nPerhaps mastery in any field comes when you reach this level of certainty - not from arrogance, but from deep connection with reality. When you know something so thoroughly that external opinions cannot shake your understanding.\n";
						const data = {author:"PVB",pubDatetime:new Date(1702123200000),title:"Mastery and Knowledge - The Tree's Wisdom",featured:true,draft:false,tags:["philosophy","wisdom","mastery"],description:"Reflections on how true knowledge is about recognizing what's real, not just accumulating information."};
						const _internal = {
							type: 'content',
							filePath: "C:/Users/<USER>/Desktop/pvb-astro/src/content/blog/mastery-and-knowledge.md",
							rawData: undefined,
						};

export { _internal, body, collection, data, id, slug };
</file>

<file path=".netlify/build/chunks/mindfulness_3c8gSlQq.mjs">
import { a as createComponent, m as maybeRenderHead, u as unescapeHTML, r as renderTemplate } from './astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import 'clsx';

const html = "";

				const frontmatter = {"text":"Silence is not empty, it's full of answers.","author":"Unknown","linkedPage":"/blog/power-of-silence","cardTitle":"Mindfulness","cardSubtitle":"Finding clarity in quiet","featured":false,"tags":["mindfulness","silence"]};
				const file = "C:/Users/<USER>/Desktop/pvb-astro/src/content/quotes/mindfulness.md";
				const url = undefined;
				function rawContent() {
					return "";
				}
				function compiledContent() {
					return html;
				}
				function getHeadings() {
					return [];
				}

				const Content = createComponent((result, _props, slots) => {
					const { layout, ...content } = frontmatter;
					content.file = file;
					content.url = url;

					return renderTemplate`${maybeRenderHead()}${unescapeHTML(html)}`;
				});

export { Content, compiledContent, Content as default, file, frontmatter, getHeadings, rawContent, url };
</file>

<file path=".netlify/build/chunks/mindfulness_BG5BPaMj.mjs">
async function getMod() {
						return import('./mindfulness_3c8gSlQq.mjs');
					}
					const collectedLinks = [];
					const collectedStyles = [];
					const collectedScripts = [];
					const defaultMod = { __astroPropagation: true, getMod, collectedLinks, collectedStyles, collectedScripts };

export { defaultMod as default };
</file>

<file path=".netlify/build/chunks/mindfulness_DTX3H095.mjs">
const id = "mindfulness.md";
						const collection = "quotes";
						const slug = "mindfulness";
						const body = "";
						const data = {text:"Silence is not empty, it's full of answers.",author:"Unknown",linkedPage:"/blog/power-of-silence",cardTitle:"Mindfulness",cardSubtitle:"Finding clarity in quiet",featured:false,tags:["mindfulness","silence"]};
						const _internal = {
							type: 'content',
							filePath: "C:/Users/<USER>/Desktop/pvb-astro/src/content/quotes/mindfulness.md",
							rawData: undefined,
						};

export { _internal, body, collection, data, id, slug };
</file>

<file path=".netlify/build/chunks/philosophy_D9CDMOq1.mjs">
async function getMod() {
						return import('./philosophy_EjjKZrv8.mjs');
					}
					const collectedLinks = [];
					const collectedStyles = [];
					const collectedScripts = [];
					const defaultMod = { __astroPropagation: true, getMod, collectedLinks, collectedStyles, collectedScripts };

export { defaultMod as default };
</file>

<file path=".netlify/build/chunks/philosophy_EjjKZrv8.mjs">
import { a as createComponent, m as maybeRenderHead, u as unescapeHTML, r as renderTemplate } from './astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import 'clsx';

const html = "";

				const frontmatter = {"text":"Many mistake stability for safety, but only the dead remain still.","author":"Pruthvi Bhat","linkedPage":"/blog/stability-vs-safety","cardTitle":"Core Philosophy","cardSubtitle":"Exploring the need for change","featured":true,"tags":["philosophy","change"]};
				const file = "C:/Users/<USER>/Desktop/pvb-astro/src/content/quotes/philosophy.md";
				const url = undefined;
				function rawContent() {
					return "";
				}
				function compiledContent() {
					return html;
				}
				function getHeadings() {
					return [];
				}

				const Content = createComponent((result, _props, slots) => {
					const { layout, ...content } = frontmatter;
					content.file = file;
					content.url = url;

					return renderTemplate`${maybeRenderHead()}${unescapeHTML(html)}`;
				});

export { Content, compiledContent, Content as default, file, frontmatter, getHeadings, rawContent, url };
</file>

<file path=".netlify/build/chunks/philosophy_fXVfYPzN.mjs">
const id = "philosophy.md";
						const collection = "quotes";
						const slug = "philosophy";
						const body = "";
						const data = {text:"Many mistake stability for safety, but only the dead remain still.",author:"Pruthvi Bhat",linkedPage:"/blog/stability-vs-safety",cardTitle:"Core Philosophy",cardSubtitle:"Exploring the need for change",featured:true,tags:["philosophy","change"]};
						const _internal = {
							type: 'content',
							filePath: "C:/Users/<USER>/Desktop/pvb-astro/src/content/quotes/philosophy.md",
							rawData: undefined,
						};

export { _internal, body, collection, data, id, slug };
</file>

<file path=".netlify/build/chunks/project-five_B9I66poG.mjs">
async function getMod() {
						return import('./project-five_bFgqsuz9.mjs');
					}
					const collectedLinks = [];
					const collectedStyles = [];
					const collectedScripts = [];
					const defaultMod = { __astroPropagation: true, getMod, collectedLinks, collectedStyles, collectedScripts };

export { defaultMod as default };
</file>

<file path=".netlify/build/chunks/project-five_BCHmoapR.mjs">
const id = "project-five.md";
						const collection = "work";
						const slug = "project-five";
						const body = "\n## Project Overview\n\nResonance is a framework for creating sophisticated spatial audio experiences that respond dynamically to virtual environments. The system enables sound to behave with physical accuracy in three-dimensional space, creating immersive audio landscapes that complement visual elements in VR, AR, and traditional interfaces.\n\n## Key Features\n\n- **Physically-Based Sound Propagation**: Simulates how sound waves travel through and interact with virtual environments.\n- **Spatial Positioning**: Places audio sources in 3D space with accurate distance attenuation and directional properties.\n- **Material-Based Acoustics**: Models how different materials absorb, reflect, and diffract sound waves.\n- **Room Acoustics**: Simulates reverberations, echoes, and other acoustic properties based on environment geometry.\n- **Dynamic Adaptation**: Audio characteristics change in real-time as users or sound sources move through space.\n- **Performance Optimization**: Uses a multi-level approach to balance computational complexity with audio fidelity.\n\n## Current Implementation\n\nThe framework is built on the Web Audio API and integrates with Three.js for spatial coordination. It employs a hybrid approach combining physical models for close-range interactions and statistically-based models for distant or complex interactions.\n\n```javascript\n// Example of material-based acoustic modeling\nclass AcousticMaterial {\n  constructor({\n    absorption = { low: 0.1, mid: 0.2, high: 0.3 },\n    scattering = 0.1,\n    transmission = 0.05\n  } = {}) {\n    this.absorption = absorption;\n    this.scattering = scattering;\n    this.transmission = transmission;\n  }\n  \n  // Calculate frequency-dependent reflection coefficient\n  getReflectionCoefficient(frequency) {\n    let band;\n    if (frequency < 250) band = 'low';\n    else if (frequency < 2000) band = 'mid';\n    else band = 'high';\n    \n    return 1 - this.absorption[band];\n  }\n  \n  // Apply material properties to an incident sound ray\n  processIncidentRay(ray, intersection, frequency) {\n    // Calculate reflected energy\n    const reflectionCoeff = this.getReflectionCoefficient(frequency);\n    const reflectedEnergy = ray.energy * reflectionCoeff * (1 - this.scattering);\n    \n    // Calculate scattered energy\n    const scatteredEnergy = ray.energy * reflectionCoeff * this.scattering;\n    \n    // Calculate transmitted energy\n    const transmittedEnergy = ray.energy * this.transmission;\n    \n    return {\n      reflectedRay: {\n        ...ray,\n        direction: calculateReflectionVector(ray.direction, intersection.normal),\n        energy: reflectedEnergy\n      },\n      scatteredRays: generateScatteredRays(intersection, scatteredEnergy),\n      transmittedRay: {\n        ...ray,\n        direction: ray.direction, // Simplified; should account for refraction\n        energy: transmittedEnergy\n      }\n    };\n  }\n}\n```\n\n## Current Status\n\nThe core audio processing engine and spatial positioning system are complete. Current development is focused on improving the material acoustics system, optimizing performance for complex environments, and creating a more intuitive API for developers.\n\n## Future Directions\n\nPlanned developments include:\n- Integration with popular game engines and AR frameworks\n- Support for ambisonics and object-based audio formats\n- Machine learning acceleration for acoustic modeling\n- Collaborative audio environments for shared spatial experiences\n- Tools for non-technical creators to design spatial soundscapes";
						const data = {title:"Resonance: Spatial Audio Framework",projectDate:new Date(*************),status:"In Progress",featured:true,tags:["Audio","WebAudio API","Spatial Computing","JavaScript","Three.js"],ogImage:"/images/resonance-preview.png",description:"A framework for creating immersive spatial audio experiences that respond to virtual environments, user movement, and interactive elements.",repoUrl:"https://github.com/username/resonance-audio",liveUrl:"https://resonance-audio-demo.com"};
						const _internal = {
							type: 'content',
							filePath: "C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-five.md",
							rawData: undefined,
						};

export { _internal, body, collection, data, id, slug };
</file>

<file path=".netlify/build/chunks/project-five_bFgqsuz9.mjs">
import { a as createComponent, m as maybeRenderHead, u as unescapeHTML, r as renderTemplate } from './astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import 'clsx';

const html = "<h2 id=\"project-overview\">Project Overview</h2>\n<p>Resonance is a framework for creating sophisticated spatial audio experiences that respond dynamically to virtual environments. The system enables sound to behave with physical accuracy in three-dimensional space, creating immersive audio landscapes that complement visual elements in VR, AR, and traditional interfaces.</p>\n<h2 id=\"key-features\">Key Features</h2>\n<ul>\n<li><strong>Physically-Based Sound Propagation</strong>: Simulates how sound waves travel through and interact with virtual environments.</li>\n<li><strong>Spatial Positioning</strong>: Places audio sources in 3D space with accurate distance attenuation and directional properties.</li>\n<li><strong>Material-Based Acoustics</strong>: Models how different materials absorb, reflect, and diffract sound waves.</li>\n<li><strong>Room Acoustics</strong>: Simulates reverberations, echoes, and other acoustic properties based on environment geometry.</li>\n<li><strong>Dynamic Adaptation</strong>: Audio characteristics change in real-time as users or sound sources move through space.</li>\n<li><strong>Performance Optimization</strong>: Uses a multi-level approach to balance computational complexity with audio fidelity.</li>\n</ul>\n<h2 id=\"current-implementation\">Current Implementation</h2>\n<p>The framework is built on the Web Audio API and integrates with Three.js for spatial coordination. It employs a hybrid approach combining physical models for close-range interactions and statistically-based models for distant or complex interactions.</p>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto; white-space: pre-wrap; word-wrap: break-word;\" tabindex=\"0\" data-language=\"javascript\"><code><span class=\"line\"><span style=\"color:#6A737D\">// Example of material-based acoustic modeling</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">class</span><span style=\"color:#B392F0\"> AcousticMaterial</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">  constructor</span><span style=\"color:#E1E4E8\">({</span></span>\n<span class=\"line\"><span style=\"color:#FFAB70\">    absorption</span><span style=\"color:#F97583\"> =</span><span style=\"color:#E1E4E8\"> { low: </span><span style=\"color:#79B8FF\">0.1</span><span style=\"color:#E1E4E8\">, mid: </span><span style=\"color:#79B8FF\">0.2</span><span style=\"color:#E1E4E8\">, high: </span><span style=\"color:#79B8FF\">0.3</span><span style=\"color:#E1E4E8\"> },</span></span>\n<span class=\"line\"><span style=\"color:#FFAB70\">    scattering</span><span style=\"color:#F97583\"> =</span><span style=\"color:#79B8FF\"> 0.1</span><span style=\"color:#E1E4E8\">,</span></span>\n<span class=\"line\"><span style=\"color:#FFAB70\">    transmission</span><span style=\"color:#F97583\"> =</span><span style=\"color:#79B8FF\"> 0.05</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  } </span><span style=\"color:#F97583\">=</span><span style=\"color:#E1E4E8\"> {}) {</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">    this</span><span style=\"color:#E1E4E8\">.absorption </span><span style=\"color:#F97583\">=</span><span style=\"color:#E1E4E8\"> absorption;</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">    this</span><span style=\"color:#E1E4E8\">.scattering </span><span style=\"color:#F97583\">=</span><span style=\"color:#E1E4E8\"> scattering;</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">    this</span><span style=\"color:#E1E4E8\">.transmission </span><span style=\"color:#F97583\">=</span><span style=\"color:#E1E4E8\"> transmission;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  }</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  </span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">  // Calculate frequency-dependent reflection coefficient</span></span>\n<span class=\"line\"><span style=\"color:#B392F0\">  getReflectionCoefficient</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#FFAB70\">frequency</span><span style=\"color:#E1E4E8\">) {</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    let</span><span style=\"color:#E1E4E8\"> band;</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    if</span><span style=\"color:#E1E4E8\"> (frequency </span><span style=\"color:#F97583\">&#x3C;</span><span style=\"color:#79B8FF\"> 250</span><span style=\"color:#E1E4E8\">) band </span><span style=\"color:#F97583\">=</span><span style=\"color:#9ECBFF\"> 'low'</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    else</span><span style=\"color:#F97583\"> if</span><span style=\"color:#E1E4E8\"> (frequency </span><span style=\"color:#F97583\">&#x3C;</span><span style=\"color:#79B8FF\"> 2000</span><span style=\"color:#E1E4E8\">) band </span><span style=\"color:#F97583\">=</span><span style=\"color:#9ECBFF\"> 'mid'</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    else</span><span style=\"color:#E1E4E8\"> band </span><span style=\"color:#F97583\">=</span><span style=\"color:#9ECBFF\"> 'high'</span><span style=\"color:#E1E4E8\">;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    </span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    return</span><span style=\"color:#79B8FF\"> 1</span><span style=\"color:#F97583\"> -</span><span style=\"color:#79B8FF\"> this</span><span style=\"color:#E1E4E8\">.absorption[band];</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  }</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  </span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">  // Apply material properties to an incident sound ray</span></span>\n<span class=\"line\"><span style=\"color:#B392F0\">  processIncidentRay</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#FFAB70\">ray</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#FFAB70\">intersection</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#FFAB70\">frequency</span><span style=\"color:#E1E4E8\">) {</span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">    // Calculate reflected energy</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    const</span><span style=\"color:#79B8FF\"> reflectionCoeff</span><span style=\"color:#F97583\"> =</span><span style=\"color:#79B8FF\"> this</span><span style=\"color:#E1E4E8\">.</span><span style=\"color:#B392F0\">getReflectionCoefficient</span><span style=\"color:#E1E4E8\">(frequency);</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    const</span><span style=\"color:#79B8FF\"> reflectedEnergy</span><span style=\"color:#F97583\"> =</span><span style=\"color:#E1E4E8\"> ray.energy </span><span style=\"color:#F97583\">*</span><span style=\"color:#E1E4E8\"> reflectionCoeff </span><span style=\"color:#F97583\">*</span><span style=\"color:#E1E4E8\"> (</span><span style=\"color:#79B8FF\">1</span><span style=\"color:#F97583\"> -</span><span style=\"color:#79B8FF\"> this</span><span style=\"color:#E1E4E8\">.scattering);</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    </span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">    // Calculate scattered energy</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    const</span><span style=\"color:#79B8FF\"> scatteredEnergy</span><span style=\"color:#F97583\"> =</span><span style=\"color:#E1E4E8\"> ray.energy </span><span style=\"color:#F97583\">*</span><span style=\"color:#E1E4E8\"> reflectionCoeff </span><span style=\"color:#F97583\">*</span><span style=\"color:#79B8FF\"> this</span><span style=\"color:#E1E4E8\">.scattering;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    </span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">    // Calculate transmitted energy</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    const</span><span style=\"color:#79B8FF\"> transmittedEnergy</span><span style=\"color:#F97583\"> =</span><span style=\"color:#E1E4E8\"> ray.energy </span><span style=\"color:#F97583\">*</span><span style=\"color:#79B8FF\"> this</span><span style=\"color:#E1E4E8\">.transmission;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    </span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    return</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">      reflectedRay: {</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">        ...</span><span style=\"color:#E1E4E8\">ray,</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">        direction: </span><span style=\"color:#B392F0\">calculateReflectionVector</span><span style=\"color:#E1E4E8\">(ray.direction, intersection.normal),</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">        energy: reflectedEnergy</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">      },</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">      scatteredRays: </span><span style=\"color:#B392F0\">generateScatteredRays</span><span style=\"color:#E1E4E8\">(intersection, scatteredEnergy),</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">      transmittedRay: {</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">        ...</span><span style=\"color:#E1E4E8\">ray,</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">        direction: ray.direction, </span><span style=\"color:#6A737D\">// Simplified; should account for refraction</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">        energy: transmittedEnergy</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">      }</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    };</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  }</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span>\n<span class=\"line\"></span></code></pre>\n<h2 id=\"current-status\">Current Status</h2>\n<p>The core audio processing engine and spatial positioning system are complete. Current development is focused on improving the material acoustics system, optimizing performance for complex environments, and creating a more intuitive API for developers.</p>\n<h2 id=\"future-directions\">Future Directions</h2>\n<p>Planned developments include:</p>\n<ul>\n<li>Integration with popular game engines and AR frameworks</li>\n<li>Support for ambisonics and object-based audio formats</li>\n<li>Machine learning acceleration for acoustic modeling</li>\n<li>Collaborative audio environments for shared spatial experiences</li>\n<li>Tools for non-technical creators to design spatial soundscapes</li>\n</ul>";

				const frontmatter = {"title":"Resonance: Spatial Audio Framework","projectDate":"2024-03-20T00:00:00.000Z","status":"In Progress","featured":true,"tags":["Audio","WebAudio API","Spatial Computing","JavaScript","Three.js"],"ogImage":"/images/resonance-preview.png","description":"A framework for creating immersive spatial audio experiences that respond to virtual environments, user movement, and interactive elements.","repoUrl":"https://github.com/username/resonance-audio","liveUrl":"https://resonance-audio-demo.com"};
				const file = "C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-five.md";
				const url = undefined;
				function rawContent() {
					return "\n## Project Overview\n\nResonance is a framework for creating sophisticated spatial audio experiences that respond dynamically to virtual environments. The system enables sound to behave with physical accuracy in three-dimensional space, creating immersive audio landscapes that complement visual elements in VR, AR, and traditional interfaces.\n\n## Key Features\n\n- **Physically-Based Sound Propagation**: Simulates how sound waves travel through and interact with virtual environments.\n- **Spatial Positioning**: Places audio sources in 3D space with accurate distance attenuation and directional properties.\n- **Material-Based Acoustics**: Models how different materials absorb, reflect, and diffract sound waves.\n- **Room Acoustics**: Simulates reverberations, echoes, and other acoustic properties based on environment geometry.\n- **Dynamic Adaptation**: Audio characteristics change in real-time as users or sound sources move through space.\n- **Performance Optimization**: Uses a multi-level approach to balance computational complexity with audio fidelity.\n\n## Current Implementation\n\nThe framework is built on the Web Audio API and integrates with Three.js for spatial coordination. It employs a hybrid approach combining physical models for close-range interactions and statistically-based models for distant or complex interactions.\n\n```javascript\n// Example of material-based acoustic modeling\nclass AcousticMaterial {\n  constructor({\n    absorption = { low: 0.1, mid: 0.2, high: 0.3 },\n    scattering = 0.1,\n    transmission = 0.05\n  } = {}) {\n    this.absorption = absorption;\n    this.scattering = scattering;\n    this.transmission = transmission;\n  }\n  \n  // Calculate frequency-dependent reflection coefficient\n  getReflectionCoefficient(frequency) {\n    let band;\n    if (frequency < 250) band = 'low';\n    else if (frequency < 2000) band = 'mid';\n    else band = 'high';\n    \n    return 1 - this.absorption[band];\n  }\n  \n  // Apply material properties to an incident sound ray\n  processIncidentRay(ray, intersection, frequency) {\n    // Calculate reflected energy\n    const reflectionCoeff = this.getReflectionCoefficient(frequency);\n    const reflectedEnergy = ray.energy * reflectionCoeff * (1 - this.scattering);\n    \n    // Calculate scattered energy\n    const scatteredEnergy = ray.energy * reflectionCoeff * this.scattering;\n    \n    // Calculate transmitted energy\n    const transmittedEnergy = ray.energy * this.transmission;\n    \n    return {\n      reflectedRay: {\n        ...ray,\n        direction: calculateReflectionVector(ray.direction, intersection.normal),\n        energy: reflectedEnergy\n      },\n      scatteredRays: generateScatteredRays(intersection, scatteredEnergy),\n      transmittedRay: {\n        ...ray,\n        direction: ray.direction, // Simplified; should account for refraction\n        energy: transmittedEnergy\n      }\n    };\n  }\n}\n```\n\n## Current Status\n\nThe core audio processing engine and spatial positioning system are complete. Current development is focused on improving the material acoustics system, optimizing performance for complex environments, and creating a more intuitive API for developers.\n\n## Future Directions\n\nPlanned developments include:\n- Integration with popular game engines and AR frameworks\n- Support for ambisonics and object-based audio formats\n- Machine learning acceleration for acoustic modeling\n- Collaborative audio environments for shared spatial experiences\n- Tools for non-technical creators to design spatial soundscapes";
				}
				function compiledContent() {
					return html;
				}
				function getHeadings() {
					return [{"depth":2,"slug":"project-overview","text":"Project Overview"},{"depth":2,"slug":"key-features","text":"Key Features"},{"depth":2,"slug":"current-implementation","text":"Current Implementation"},{"depth":2,"slug":"current-status","text":"Current Status"},{"depth":2,"slug":"future-directions","text":"Future Directions"}];
				}

				const Content = createComponent((result, _props, slots) => {
					const { layout, ...content } = frontmatter;
					content.file = file;
					content.url = url;

					return renderTemplate`${maybeRenderHead()}${unescapeHTML(html)}`;
				});

export { Content, compiledContent, Content as default, file, frontmatter, getHeadings, rawContent, url };
</file>

<file path=".netlify/build/chunks/project-four_BiijU9ph.mjs">
const id = "project-four.md";
						const collection = "work";
						const slug = "project-four";
						const body = "\n## Project Overview\n\nThis project implements a sophisticated multi-modal biometric authentication system that combines facial recognition, voice pattern analysis, and behavioral biometrics to create a highly secure yet user-friendly authentication solution. The system is designed for applications requiring heightened security measures while maintaining a seamless user experience.\n\n## Key Features\n\n- **Multi-modal Authentication**: Combines multiple biometric factors to significantly reduce false positives and negatives.\n- **Facial Recognition**: Uses depth-aware facial recognition resistant to presentation attacks.\n- **Voice Authentication**: Analyzes voice patterns beyond simple recordings, including stress indicators and natural variations.\n- **Behavioral Biometrics**: Monitors typing patterns, gesture dynamics, and interaction habits for continuous authentication.\n- **Privacy-Preserving**: Implements differential privacy techniques to protect biometric templates.\n\n## Technical Implementation\n\nThe system uses a modular architecture where each biometric component can operate independently or in concert with others. The core is built with Python, leveraging TensorFlow for the deep learning components and specialized libraries for each biometric modality.\n\n```python\n# Example of the multi-modal fusion algorithm\ndef authenticate_user(face_features, voice_features, behavior_features, user_profile):\n    # Compute individual confidence scores\n    face_confidence = face_recognizer.verify(face_features, user_profile.face_template)\n    voice_confidence = voice_analyzer.verify(voice_features, user_profile.voice_template)\n    behavior_confidence = behavior_analyzer.verify(behavior_features, user_profile.behavior_template)\n\n    # Dynamic weighting based on environmental factors and quality\n    weights = compute_adaptive_weights(\n        face_quality=face_features.quality_score,\n        voice_quality=voice_features.quality_score,\n        behavior_consistency=behavior_features.consistency_score,\n        environmental_noise=get_environmental_context()\n    )\n\n    # Weighted fusion with adaptive threshold\n    combined_score = (\n        weights.face * face_confidence +\n        weights.voice * voice_confidence +\n        weights.behavior * behavior_confidence\n    ) / sum(weights.values())\n\n    threshold = compute_adaptive_threshold(user_profile, get_risk_context())\n\n    return combined_score >= threshold, combined_score\n```\n\n## Results and Impact\n\nThe system has been deployed in three high-security environments, showing a 99.7% true acceptance rate and a 0.003% false acceptance rate in field tests—a significant improvement over single-factor biometric systems. The adaptive fusion algorithm has demonstrated particular effectiveness in challenging environmental conditions where individual biometric factors might be compromised.\n\n## Ethical Considerations\n\nThe project included extensive work on privacy protection, bias mitigation, and ethical guidelines for deployment. All data collection followed strict consent protocols, and the system includes features allowing users to review and delete their biometric templates. Regular audits ensure that the system's performance is consistent across demographic groups.";
						const data = {title:"Biometric Authentication System",projectDate:new Date(1695340800000),status:"Completed",featured:false,tags:["Security","Computer Vision","Biometrics","Python","TensorFlow"],ogImage:"/images/biometric-auth-preview.png",description:"A multi-modal biometric authentication system combining facial recognition, voice analysis, and behavioral patterns for high-security applications.",repoUrl:"https://github.com/username/biometric-auth",liveUrl:"https://example.com/biometric-demo"};
						const _internal = {
							type: 'content',
							filePath: "C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-four.md",
							rawData: undefined,
						};

export { _internal, body, collection, data, id, slug };
</file>

<file path=".netlify/build/chunks/project-four_BWkJ1E-x.mjs">
import { a as createComponent, m as maybeRenderHead, u as unescapeHTML, r as renderTemplate } from './astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import 'clsx';

const html = "<h2 id=\"project-overview\">Project Overview</h2>\n<p>This project implements a sophisticated multi-modal biometric authentication system that combines facial recognition, voice pattern analysis, and behavioral biometrics to create a highly secure yet user-friendly authentication solution. The system is designed for applications requiring heightened security measures while maintaining a seamless user experience.</p>\n<h2 id=\"key-features\">Key Features</h2>\n<ul>\n<li><strong>Multi-modal Authentication</strong>: Combines multiple biometric factors to significantly reduce false positives and negatives.</li>\n<li><strong>Facial Recognition</strong>: Uses depth-aware facial recognition resistant to presentation attacks.</li>\n<li><strong>Voice Authentication</strong>: Analyzes voice patterns beyond simple recordings, including stress indicators and natural variations.</li>\n<li><strong>Behavioral Biometrics</strong>: Monitors typing patterns, gesture dynamics, and interaction habits for continuous authentication.</li>\n<li><strong>Privacy-Preserving</strong>: Implements differential privacy techniques to protect biometric templates.</li>\n</ul>\n<h2 id=\"technical-implementation\">Technical Implementation</h2>\n<p>The system uses a modular architecture where each biometric component can operate independently or in concert with others. The core is built with Python, leveraging TensorFlow for the deep learning components and specialized libraries for each biometric modality.</p>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto; white-space: pre-wrap; word-wrap: break-word;\" tabindex=\"0\" data-language=\"python\"><code><span class=\"line\"><span style=\"color:#6A737D\"># Example of the multi-modal fusion algorithm</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">def</span><span style=\"color:#B392F0\"> authenticate_user</span><span style=\"color:#E1E4E8\">(face_features, voice_features, behavior_features, user_profile):</span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">    # Compute individual confidence scores</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    face_confidence </span><span style=\"color:#F97583\">=</span><span style=\"color:#E1E4E8\"> face_recognizer.verify(face_features, user_profile.face_template)</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    voice_confidence </span><span style=\"color:#F97583\">=</span><span style=\"color:#E1E4E8\"> voice_analyzer.verify(voice_features, user_profile.voice_template)</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    behavior_confidence </span><span style=\"color:#F97583\">=</span><span style=\"color:#E1E4E8\"> behavior_analyzer.verify(behavior_features, user_profile.behavior_template)</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#6A737D\">    # Dynamic weighting based on environmental factors and quality</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    weights </span><span style=\"color:#F97583\">=</span><span style=\"color:#E1E4E8\"> compute_adaptive_weights(</span></span>\n<span class=\"line\"><span style=\"color:#FFAB70\">        face_quality</span><span style=\"color:#F97583\">=</span><span style=\"color:#E1E4E8\">face_features.quality_score,</span></span>\n<span class=\"line\"><span style=\"color:#FFAB70\">        voice_quality</span><span style=\"color:#F97583\">=</span><span style=\"color:#E1E4E8\">voice_features.quality_score,</span></span>\n<span class=\"line\"><span style=\"color:#FFAB70\">        behavior_consistency</span><span style=\"color:#F97583\">=</span><span style=\"color:#E1E4E8\">behavior_features.consistency_score,</span></span>\n<span class=\"line\"><span style=\"color:#FFAB70\">        environmental_noise</span><span style=\"color:#F97583\">=</span><span style=\"color:#E1E4E8\">get_environmental_context()</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    )</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#6A737D\">    # Weighted fusion with adaptive threshold</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    combined_score </span><span style=\"color:#F97583\">=</span><span style=\"color:#E1E4E8\"> (</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">        weights.face </span><span style=\"color:#F97583\">*</span><span style=\"color:#E1E4E8\"> face_confidence </span><span style=\"color:#F97583\">+</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">        weights.voice </span><span style=\"color:#F97583\">*</span><span style=\"color:#E1E4E8\"> voice_confidence </span><span style=\"color:#F97583\">+</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">        weights.behavior </span><span style=\"color:#F97583\">*</span><span style=\"color:#E1E4E8\"> behavior_confidence</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    ) </span><span style=\"color:#F97583\">/</span><span style=\"color:#79B8FF\"> sum</span><span style=\"color:#E1E4E8\">(weights.values())</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    threshold </span><span style=\"color:#F97583\">=</span><span style=\"color:#E1E4E8\"> compute_adaptive_threshold(user_profile, get_risk_context())</span></span>\n<span class=\"line\"></span>\n<span class=\"line\"><span style=\"color:#F97583\">    return</span><span style=\"color:#E1E4E8\"> combined_score </span><span style=\"color:#F97583\">>=</span><span style=\"color:#E1E4E8\"> threshold, combined_score</span></span>\n<span class=\"line\"></span></code></pre>\n<h2 id=\"results-and-impact\">Results and Impact</h2>\n<p>The system has been deployed in three high-security environments, showing a 99.7% true acceptance rate and a 0.003% false acceptance rate in field tests—a significant improvement over single-factor biometric systems. The adaptive fusion algorithm has demonstrated particular effectiveness in challenging environmental conditions where individual biometric factors might be compromised.</p>\n<h2 id=\"ethical-considerations\">Ethical Considerations</h2>\n<p>The project included extensive work on privacy protection, bias mitigation, and ethical guidelines for deployment. All data collection followed strict consent protocols, and the system includes features allowing users to review and delete their biometric templates. Regular audits ensure that the system’s performance is consistent across demographic groups.</p>";

				const frontmatter = {"title":"Biometric Authentication System","projectDate":"2023-09-22T00:00:00.000Z","status":"Completed","featured":false,"tags":["Security","Computer Vision","Biometrics","Python","TensorFlow"],"ogImage":"/images/biometric-auth-preview.png","description":"A multi-modal biometric authentication system combining facial recognition, voice analysis, and behavioral patterns for high-security applications.","repoUrl":"https://github.com/username/biometric-auth","liveUrl":"https://example.com/biometric-demo"};
				const file = "C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-four.md";
				const url = undefined;
				function rawContent() {
					return "\n## Project Overview\n\nThis project implements a sophisticated multi-modal biometric authentication system that combines facial recognition, voice pattern analysis, and behavioral biometrics to create a highly secure yet user-friendly authentication solution. The system is designed for applications requiring heightened security measures while maintaining a seamless user experience.\n\n## Key Features\n\n- **Multi-modal Authentication**: Combines multiple biometric factors to significantly reduce false positives and negatives.\n- **Facial Recognition**: Uses depth-aware facial recognition resistant to presentation attacks.\n- **Voice Authentication**: Analyzes voice patterns beyond simple recordings, including stress indicators and natural variations.\n- **Behavioral Biometrics**: Monitors typing patterns, gesture dynamics, and interaction habits for continuous authentication.\n- **Privacy-Preserving**: Implements differential privacy techniques to protect biometric templates.\n\n## Technical Implementation\n\nThe system uses a modular architecture where each biometric component can operate independently or in concert with others. The core is built with Python, leveraging TensorFlow for the deep learning components and specialized libraries for each biometric modality.\n\n```python\n# Example of the multi-modal fusion algorithm\ndef authenticate_user(face_features, voice_features, behavior_features, user_profile):\n    # Compute individual confidence scores\n    face_confidence = face_recognizer.verify(face_features, user_profile.face_template)\n    voice_confidence = voice_analyzer.verify(voice_features, user_profile.voice_template)\n    behavior_confidence = behavior_analyzer.verify(behavior_features, user_profile.behavior_template)\n\n    # Dynamic weighting based on environmental factors and quality\n    weights = compute_adaptive_weights(\n        face_quality=face_features.quality_score,\n        voice_quality=voice_features.quality_score,\n        behavior_consistency=behavior_features.consistency_score,\n        environmental_noise=get_environmental_context()\n    )\n\n    # Weighted fusion with adaptive threshold\n    combined_score = (\n        weights.face * face_confidence +\n        weights.voice * voice_confidence +\n        weights.behavior * behavior_confidence\n    ) / sum(weights.values())\n\n    threshold = compute_adaptive_threshold(user_profile, get_risk_context())\n\n    return combined_score >= threshold, combined_score\n```\n\n## Results and Impact\n\nThe system has been deployed in three high-security environments, showing a 99.7% true acceptance rate and a 0.003% false acceptance rate in field tests—a significant improvement over single-factor biometric systems. The adaptive fusion algorithm has demonstrated particular effectiveness in challenging environmental conditions where individual biometric factors might be compromised.\n\n## Ethical Considerations\n\nThe project included extensive work on privacy protection, bias mitigation, and ethical guidelines for deployment. All data collection followed strict consent protocols, and the system includes features allowing users to review and delete their biometric templates. Regular audits ensure that the system's performance is consistent across demographic groups.";
				}
				function compiledContent() {
					return html;
				}
				function getHeadings() {
					return [{"depth":2,"slug":"project-overview","text":"Project Overview"},{"depth":2,"slug":"key-features","text":"Key Features"},{"depth":2,"slug":"technical-implementation","text":"Technical Implementation"},{"depth":2,"slug":"results-and-impact","text":"Results and Impact"},{"depth":2,"slug":"ethical-considerations","text":"Ethical Considerations"}];
				}

				const Content = createComponent((result, _props, slots) => {
					const { layout, ...content } = frontmatter;
					content.file = file;
					content.url = url;

					return renderTemplate`${maybeRenderHead()}${unescapeHTML(html)}`;
				});

export { Content, compiledContent, Content as default, file, frontmatter, getHeadings, rawContent, url };
</file>

<file path=".netlify/build/chunks/project-four_DlJCn5JQ.mjs">
async function getMod() {
						return import('./project-four_BWkJ1E-x.mjs');
					}
					const collectedLinks = [];
					const collectedStyles = [];
					const collectedScripts = [];
					const defaultMod = { __astroPropagation: true, getMod, collectedLinks, collectedStyles, collectedScripts };

export { defaultMod as default };
</file>

<file path=".netlify/build/chunks/project-one_BLBOXsjf.mjs">
async function getMod() {
						return import('./project-one_Cse1VnLC.mjs');
					}
					const collectedLinks = [];
					const collectedStyles = [];
					const collectedScripts = [];
					const defaultMod = { __astroPropagation: true, getMod, collectedLinks, collectedStyles, collectedScripts };

export { defaultMod as default };
</file>

<file path=".netlify/build/chunks/project-one_Cse1VnLC.mjs">
import { a as createComponent, m as maybeRenderHead, u as unescapeHTML, r as renderTemplate } from './astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import 'clsx';

const html = "<h2 id=\"project-overview\">Project Overview</h2>\n<p>This project implements an advanced content generation system that uses state-of-the-art language models to create high-quality, contextually relevant content for various purposes. The system is designed to understand user requirements and generate content that matches the desired tone, style, and format.</p>\n<h2 id=\"key-features\">Key Features</h2>\n<ul>\n<li><strong>Intelligent Context Understanding</strong>: The system analyzes provided context to ensure generated content is relevant and appropriate.</li>\n<li><strong>Style Matching</strong>: Content can be generated in various styles, from formal academic to casual conversational.</li>\n<li><strong>Multi-format Support</strong>: Generates content for blog posts, articles, product descriptions, social media posts, and more.</li>\n<li><strong>Customizable Parameters</strong>: Users can adjust parameters like creativity, length, and technical depth.</li>\n<li><strong>Feedback Integration</strong>: The system learns from user feedback to improve future generations.</li>\n</ul>\n<h2 id=\"technical-implementation\">Technical Implementation</h2>\n<p>The backend is built with Python, utilizing transformer-based language models fine-tuned on specific content types. The frontend is a React application that provides an intuitive interface for content specification and generation.</p>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto; white-space: pre-wrap; word-wrap: break-word;\" tabindex=\"0\" data-language=\"python\"><code><span class=\"line\"><span style=\"color:#6A737D\"># Example of the content generation pipeline</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">def</span><span style=\"color:#B392F0\"> generate_content</span><span style=\"color:#E1E4E8\">(context, style, format, parameters):</span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">    # Preprocess the context</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    processed_context </span><span style=\"color:#F97583\">=</span><span style=\"color:#E1E4E8\"> preprocess(context)</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    </span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">    # Select the appropriate model based on style and format</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    model </span><span style=\"color:#F97583\">=</span><span style=\"color:#E1E4E8\"> model_selector(style, </span><span style=\"color:#79B8FF\">format</span><span style=\"color:#E1E4E8\">)</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    </span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">    # Generate the content</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    content </span><span style=\"color:#F97583\">=</span><span style=\"color:#E1E4E8\"> model.generate(</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">        processed_context,</span></span>\n<span class=\"line\"><span style=\"color:#FFAB70\">        max_length</span><span style=\"color:#F97583\">=</span><span style=\"color:#E1E4E8\">parameters[</span><span style=\"color:#9ECBFF\">'length'</span><span style=\"color:#E1E4E8\">],</span></span>\n<span class=\"line\"><span style=\"color:#FFAB70\">        temperature</span><span style=\"color:#F97583\">=</span><span style=\"color:#E1E4E8\">parameters[</span><span style=\"color:#9ECBFF\">'creativity'</span><span style=\"color:#E1E4E8\">],</span></span>\n<span class=\"line\"><span style=\"color:#FFAB70\">        technical_depth</span><span style=\"color:#F97583\">=</span><span style=\"color:#E1E4E8\">parameters[</span><span style=\"color:#9ECBFF\">'technical_depth'</span><span style=\"color:#E1E4E8\">]</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    )</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    </span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">    # Post-process the content</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    final_content </span><span style=\"color:#F97583\">=</span><span style=\"color:#E1E4E8\"> postprocess(content, </span><span style=\"color:#79B8FF\">format</span><span style=\"color:#E1E4E8\">)</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    </span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    return</span><span style=\"color:#E1E4E8\"> final_content</span></span>\n<span class=\"line\"></span></code></pre>\n<h2 id=\"results-and-impact\">Results and Impact</h2>\n<p>The system has been successfully deployed in multiple content creation workflows, reducing content creation time by 60% while maintaining high quality standards. Users report that the generated content requires minimal editing and often serves as an excellent starting point for further refinement.</p>\n<h2 id=\"future-directions\">Future Directions</h2>\n<p>Future development will focus on expanding the range of supported content types, improving the understanding of specialized domains, and implementing more sophisticated feedback mechanisms to continuously improve the quality of generated content.</p>";

				const frontmatter = {"title":"AI-Powered Content Generation System","projectDate":"2023-12-15T00:00:00.000Z","status":"Completed","featured":true,"tags":["AI","Machine Learning","NLP","Python","React"],"ogImage":"/images/project-one-card.png","description":"A sophisticated content generation system leveraging state-of-the-art language models for creating high-quality, contextually relevant content.","repoUrl":"https://github.com/username/ai-content-gen","liveUrl":"https://ai-content-gen-demo.com"};
				const file = "C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-one.md";
				const url = undefined;
				function rawContent() {
					return "\n## Project Overview\n\nThis project implements an advanced content generation system that uses state-of-the-art language models to create high-quality, contextually relevant content for various purposes. The system is designed to understand user requirements and generate content that matches the desired tone, style, and format.\n\n## Key Features\n\n- **Intelligent Context Understanding**: The system analyzes provided context to ensure generated content is relevant and appropriate.\n- **Style Matching**: Content can be generated in various styles, from formal academic to casual conversational.\n- **Multi-format Support**: Generates content for blog posts, articles, product descriptions, social media posts, and more.\n- **Customizable Parameters**: Users can adjust parameters like creativity, length, and technical depth.\n- **Feedback Integration**: The system learns from user feedback to improve future generations.\n\n## Technical Implementation\n\nThe backend is built with Python, utilizing transformer-based language models fine-tuned on specific content types. The frontend is a React application that provides an intuitive interface for content specification and generation.\n\n```python\n# Example of the content generation pipeline\ndef generate_content(context, style, format, parameters):\n    # Preprocess the context\n    processed_context = preprocess(context)\n    \n    # Select the appropriate model based on style and format\n    model = model_selector(style, format)\n    \n    # Generate the content\n    content = model.generate(\n        processed_context,\n        max_length=parameters['length'],\n        temperature=parameters['creativity'],\n        technical_depth=parameters['technical_depth']\n    )\n    \n    # Post-process the content\n    final_content = postprocess(content, format)\n    \n    return final_content\n```\n\n## Results and Impact\n\nThe system has been successfully deployed in multiple content creation workflows, reducing content creation time by 60% while maintaining high quality standards. Users report that the generated content requires minimal editing and often serves as an excellent starting point for further refinement.\n\n## Future Directions\n\nFuture development will focus on expanding the range of supported content types, improving the understanding of specialized domains, and implementing more sophisticated feedback mechanisms to continuously improve the quality of generated content.\n";
				}
				function compiledContent() {
					return html;
				}
				function getHeadings() {
					return [{"depth":2,"slug":"project-overview","text":"Project Overview"},{"depth":2,"slug":"key-features","text":"Key Features"},{"depth":2,"slug":"technical-implementation","text":"Technical Implementation"},{"depth":2,"slug":"results-and-impact","text":"Results and Impact"},{"depth":2,"slug":"future-directions","text":"Future Directions"}];
				}

				const Content = createComponent((result, _props, slots) => {
					const { layout, ...content } = frontmatter;
					content.file = file;
					content.url = url;

					return renderTemplate`${maybeRenderHead()}${unescapeHTML(html)}`;
				});

export { Content, compiledContent, Content as default, file, frontmatter, getHeadings, rawContent, url };
</file>

<file path=".netlify/build/chunks/project-one_kU0mYB6U.mjs">
const id = "project-one.md";
						const collection = "work";
						const slug = "project-one";
						const body = "\n## Project Overview\n\nThis project implements an advanced content generation system that uses state-of-the-art language models to create high-quality, contextually relevant content for various purposes. The system is designed to understand user requirements and generate content that matches the desired tone, style, and format.\n\n## Key Features\n\n- **Intelligent Context Understanding**: The system analyzes provided context to ensure generated content is relevant and appropriate.\n- **Style Matching**: Content can be generated in various styles, from formal academic to casual conversational.\n- **Multi-format Support**: Generates content for blog posts, articles, product descriptions, social media posts, and more.\n- **Customizable Parameters**: Users can adjust parameters like creativity, length, and technical depth.\n- **Feedback Integration**: The system learns from user feedback to improve future generations.\n\n## Technical Implementation\n\nThe backend is built with Python, utilizing transformer-based language models fine-tuned on specific content types. The frontend is a React application that provides an intuitive interface for content specification and generation.\n\n```python\n# Example of the content generation pipeline\ndef generate_content(context, style, format, parameters):\n    # Preprocess the context\n    processed_context = preprocess(context)\n    \n    # Select the appropriate model based on style and format\n    model = model_selector(style, format)\n    \n    # Generate the content\n    content = model.generate(\n        processed_context,\n        max_length=parameters['length'],\n        temperature=parameters['creativity'],\n        technical_depth=parameters['technical_depth']\n    )\n    \n    # Post-process the content\n    final_content = postprocess(content, format)\n    \n    return final_content\n```\n\n## Results and Impact\n\nThe system has been successfully deployed in multiple content creation workflows, reducing content creation time by 60% while maintaining high quality standards. Users report that the generated content requires minimal editing and often serves as an excellent starting point for further refinement.\n\n## Future Directions\n\nFuture development will focus on expanding the range of supported content types, improving the understanding of specialized domains, and implementing more sophisticated feedback mechanisms to continuously improve the quality of generated content.\n";
						const data = {title:"AI-Powered Content Generation System",projectDate:new Date(1702598400000),status:"Completed",featured:true,tags:["AI","Machine Learning","NLP","Python","React"],ogImage:"/images/project-one-card.png",description:"A sophisticated content generation system leveraging state-of-the-art language models for creating high-quality, contextually relevant content.",repoUrl:"https://github.com/username/ai-content-gen",liveUrl:"https://ai-content-gen-demo.com"};
						const _internal = {
							type: 'content',
							filePath: "C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-one.md",
							rawData: undefined,
						};

export { _internal, body, collection, data, id, slug };
</file>

<file path=".netlify/build/chunks/project-three_C8CEKmI8.mjs">
const id = "project-three.md";
						const collection = "work";
						const slug = "project-three";
						const body = "\n## Project Overview\n\nThe Neural Network Visualization Framework creates intuitive, interactive 3D visualizations of neural network architectures and their learning processes. It translates complex mathematical structures into spatially coherent visual representations that researchers, students, and practitioners can explore and understand.\n\n## Key Features\n\n- **Architecture Visualization**: Renders neural networks as interactive 3D structures with intuitive representations of layers, neurons, and connections.\n- **Learning Process Animation**: Visualizes the training process in real-time, showing weight adjustments, activation patterns, and error propagation.\n- **Customizable Views**: Offers multiple visualization perspectives from high-level architecture overview to neuron-level activation details.\n- **Interactive Exploration**: Allows users to rotate, zoom, isolate layers, and select specific neurons to inspect their behavior.\n- **Time Controls**: Includes playback controls to pause, slow down, or speed up the visualization of training processes.\n\n## Technical Implementation\n\nThe framework is built using Three.js for WebGL-based 3D rendering, with a custom data processing pipeline that efficiently transforms neural network state data into visual elements. The system supports both pre-trained models and live training sessions.\n\n```javascript\n// Example of neuron activity visualization\nfunction visualizeNeuronActivity(layer, activations, timeStep) {\n  const neurons = layerMap.get(layer);\n  \n  // Update each neuron's visual properties based on its activation\n  neurons.forEach((neuron, index) => {\n    const activation = activations[index];\n    \n    // Map activation to visual properties\n    const intensity = normalizeActivation(activation);\n    const hue = 220 + (120 * intensity); // Blue to purple\n    const scale = 1 + (intensity * 0.5);\n    \n    // Apply visual updates with temporal easing\n    neuron.material.color.setHSL(hue/360, 0.8, 0.5);\n    neuron.scale.lerp(new THREE.Vector3(scale, scale, scale), 0.3);\n    \n    // Add activation trail for temporal context\n    if (timeStep % 5 === 0) {\n      addActivationTrail(neuron, intensity);\n    }\n  });\n}\n```\n\n## Current Progress\n\nThe core visualization engine and data processing pipeline are complete. Current development is focused on improving performance for large networks, enhancing the user interface for better exploration, and adding support for more network architectures including transformers and CNNs.\n\n## Future Directions\n\nFuture plans include adding support for collaborative exploration in shared virtual spaces, creating predefined educational pathways that guide users through neural network concepts, and developing an API that allows real-time visualization of custom models during training in popular frameworks like TensorFlow and PyTorch.";
						const data = {title:"Neural Network Visualization Framework",projectDate:new Date(1707523200000),status:"In Progress",featured:false,tags:["Visualization","Machine Learning","JavaScript","Three.js","WebGL"],ogImage:"/images/neural-viz-preview.png",description:"An interactive framework for visualizing neural network architectures and real-time learning processes through elegant 3D representations.",repoUrl:"https://github.com/username/neural-viz",liveUrl:"https://neural-viz-demo.com"};
						const _internal = {
							type: 'content',
							filePath: "C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-three.md",
							rawData: undefined,
						};

export { _internal, body, collection, data, id, slug };
</file>

<file path=".netlify/build/chunks/project-three_CCeq-lTS.mjs">
async function getMod() {
						return import('./project-three_Dplria35.mjs');
					}
					const collectedLinks = [];
					const collectedStyles = [];
					const collectedScripts = [];
					const defaultMod = { __astroPropagation: true, getMod, collectedLinks, collectedStyles, collectedScripts };

export { defaultMod as default };
</file>

<file path=".netlify/build/chunks/project-three_Dplria35.mjs">
import { a as createComponent, m as maybeRenderHead, u as unescapeHTML, r as renderTemplate } from './astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import 'clsx';

const html = "<h2 id=\"project-overview\">Project Overview</h2>\n<p>The Neural Network Visualization Framework creates intuitive, interactive 3D visualizations of neural network architectures and their learning processes. It translates complex mathematical structures into spatially coherent visual representations that researchers, students, and practitioners can explore and understand.</p>\n<h2 id=\"key-features\">Key Features</h2>\n<ul>\n<li><strong>Architecture Visualization</strong>: Renders neural networks as interactive 3D structures with intuitive representations of layers, neurons, and connections.</li>\n<li><strong>Learning Process Animation</strong>: Visualizes the training process in real-time, showing weight adjustments, activation patterns, and error propagation.</li>\n<li><strong>Customizable Views</strong>: Offers multiple visualization perspectives from high-level architecture overview to neuron-level activation details.</li>\n<li><strong>Interactive Exploration</strong>: Allows users to rotate, zoom, isolate layers, and select specific neurons to inspect their behavior.</li>\n<li><strong>Time Controls</strong>: Includes playback controls to pause, slow down, or speed up the visualization of training processes.</li>\n</ul>\n<h2 id=\"technical-implementation\">Technical Implementation</h2>\n<p>The framework is built using Three.js for WebGL-based 3D rendering, with a custom data processing pipeline that efficiently transforms neural network state data into visual elements. The system supports both pre-trained models and live training sessions.</p>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto; white-space: pre-wrap; word-wrap: break-word;\" tabindex=\"0\" data-language=\"javascript\"><code><span class=\"line\"><span style=\"color:#6A737D\">// Example of neuron activity visualization</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">function</span><span style=\"color:#B392F0\"> visualizeNeuronActivity</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#FFAB70\">layer</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#FFAB70\">activations</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#FFAB70\">timeStep</span><span style=\"color:#E1E4E8\">) {</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">  const</span><span style=\"color:#79B8FF\"> neurons</span><span style=\"color:#F97583\"> =</span><span style=\"color:#E1E4E8\"> layerMap.</span><span style=\"color:#B392F0\">get</span><span style=\"color:#E1E4E8\">(layer);</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  </span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">  // Update each neuron's visual properties based on its activation</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  neurons.</span><span style=\"color:#B392F0\">forEach</span><span style=\"color:#E1E4E8\">((</span><span style=\"color:#FFAB70\">neuron</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#FFAB70\">index</span><span style=\"color:#E1E4E8\">) </span><span style=\"color:#F97583\">=></span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    const</span><span style=\"color:#79B8FF\"> activation</span><span style=\"color:#F97583\"> =</span><span style=\"color:#E1E4E8\"> activations[index];</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    </span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">    // Map activation to visual properties</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    const</span><span style=\"color:#79B8FF\"> intensity</span><span style=\"color:#F97583\"> =</span><span style=\"color:#B392F0\"> normalizeActivation</span><span style=\"color:#E1E4E8\">(activation);</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    const</span><span style=\"color:#79B8FF\"> hue</span><span style=\"color:#F97583\"> =</span><span style=\"color:#79B8FF\"> 220</span><span style=\"color:#F97583\"> +</span><span style=\"color:#E1E4E8\"> (</span><span style=\"color:#79B8FF\">120</span><span style=\"color:#F97583\"> *</span><span style=\"color:#E1E4E8\"> intensity); </span><span style=\"color:#6A737D\">// Blue to purple</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    const</span><span style=\"color:#79B8FF\"> scale</span><span style=\"color:#F97583\"> =</span><span style=\"color:#79B8FF\"> 1</span><span style=\"color:#F97583\"> +</span><span style=\"color:#E1E4E8\"> (intensity </span><span style=\"color:#F97583\">*</span><span style=\"color:#79B8FF\"> 0.5</span><span style=\"color:#E1E4E8\">);</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    </span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">    // Apply visual updates with temporal easing</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    neuron.material.color.</span><span style=\"color:#B392F0\">setHSL</span><span style=\"color:#E1E4E8\">(hue</span><span style=\"color:#F97583\">/</span><span style=\"color:#79B8FF\">360</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#79B8FF\">0.8</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#79B8FF\">0.5</span><span style=\"color:#E1E4E8\">);</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    neuron.scale.</span><span style=\"color:#B392F0\">lerp</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#F97583\">new</span><span style=\"color:#79B8FF\"> THREE</span><span style=\"color:#E1E4E8\">.</span><span style=\"color:#B392F0\">Vector3</span><span style=\"color:#E1E4E8\">(scale, scale, scale), </span><span style=\"color:#79B8FF\">0.3</span><span style=\"color:#E1E4E8\">);</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    </span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">    // Add activation trail for temporal context</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    if</span><span style=\"color:#E1E4E8\"> (timeStep </span><span style=\"color:#F97583\">%</span><span style=\"color:#79B8FF\"> 5</span><span style=\"color:#F97583\"> ===</span><span style=\"color:#79B8FF\"> 0</span><span style=\"color:#E1E4E8\">) {</span></span>\n<span class=\"line\"><span style=\"color:#B392F0\">      addActivationTrail</span><span style=\"color:#E1E4E8\">(neuron, intensity);</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    }</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  });</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span>\n<span class=\"line\"></span></code></pre>\n<h2 id=\"current-progress\">Current Progress</h2>\n<p>The core visualization engine and data processing pipeline are complete. Current development is focused on improving performance for large networks, enhancing the user interface for better exploration, and adding support for more network architectures including transformers and CNNs.</p>\n<h2 id=\"future-directions\">Future Directions</h2>\n<p>Future plans include adding support for collaborative exploration in shared virtual spaces, creating predefined educational pathways that guide users through neural network concepts, and developing an API that allows real-time visualization of custom models during training in popular frameworks like TensorFlow and PyTorch.</p>";

				const frontmatter = {"title":"Neural Network Visualization Framework","projectDate":"2024-02-10T00:00:00.000Z","status":"In Progress","featured":false,"tags":["Visualization","Machine Learning","JavaScript","Three.js","WebGL"],"ogImage":"/images/neural-viz-preview.png","description":"An interactive framework for visualizing neural network architectures and real-time learning processes through elegant 3D representations.","repoUrl":"https://github.com/username/neural-viz","liveUrl":"https://neural-viz-demo.com"};
				const file = "C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-three.md";
				const url = undefined;
				function rawContent() {
					return "\n## Project Overview\n\nThe Neural Network Visualization Framework creates intuitive, interactive 3D visualizations of neural network architectures and their learning processes. It translates complex mathematical structures into spatially coherent visual representations that researchers, students, and practitioners can explore and understand.\n\n## Key Features\n\n- **Architecture Visualization**: Renders neural networks as interactive 3D structures with intuitive representations of layers, neurons, and connections.\n- **Learning Process Animation**: Visualizes the training process in real-time, showing weight adjustments, activation patterns, and error propagation.\n- **Customizable Views**: Offers multiple visualization perspectives from high-level architecture overview to neuron-level activation details.\n- **Interactive Exploration**: Allows users to rotate, zoom, isolate layers, and select specific neurons to inspect their behavior.\n- **Time Controls**: Includes playback controls to pause, slow down, or speed up the visualization of training processes.\n\n## Technical Implementation\n\nThe framework is built using Three.js for WebGL-based 3D rendering, with a custom data processing pipeline that efficiently transforms neural network state data into visual elements. The system supports both pre-trained models and live training sessions.\n\n```javascript\n// Example of neuron activity visualization\nfunction visualizeNeuronActivity(layer, activations, timeStep) {\n  const neurons = layerMap.get(layer);\n  \n  // Update each neuron's visual properties based on its activation\n  neurons.forEach((neuron, index) => {\n    const activation = activations[index];\n    \n    // Map activation to visual properties\n    const intensity = normalizeActivation(activation);\n    const hue = 220 + (120 * intensity); // Blue to purple\n    const scale = 1 + (intensity * 0.5);\n    \n    // Apply visual updates with temporal easing\n    neuron.material.color.setHSL(hue/360, 0.8, 0.5);\n    neuron.scale.lerp(new THREE.Vector3(scale, scale, scale), 0.3);\n    \n    // Add activation trail for temporal context\n    if (timeStep % 5 === 0) {\n      addActivationTrail(neuron, intensity);\n    }\n  });\n}\n```\n\n## Current Progress\n\nThe core visualization engine and data processing pipeline are complete. Current development is focused on improving performance for large networks, enhancing the user interface for better exploration, and adding support for more network architectures including transformers and CNNs.\n\n## Future Directions\n\nFuture plans include adding support for collaborative exploration in shared virtual spaces, creating predefined educational pathways that guide users through neural network concepts, and developing an API that allows real-time visualization of custom models during training in popular frameworks like TensorFlow and PyTorch.";
				}
				function compiledContent() {
					return html;
				}
				function getHeadings() {
					return [{"depth":2,"slug":"project-overview","text":"Project Overview"},{"depth":2,"slug":"key-features","text":"Key Features"},{"depth":2,"slug":"technical-implementation","text":"Technical Implementation"},{"depth":2,"slug":"current-progress","text":"Current Progress"},{"depth":2,"slug":"future-directions","text":"Future Directions"}];
				}

				const Content = createComponent((result, _props, slots) => {
					const { layout, ...content } = frontmatter;
					content.file = file;
					content.url = url;

					return renderTemplate`${maybeRenderHead()}${unescapeHTML(html)}`;
				});

export { Content, compiledContent, Content as default, file, frontmatter, getHeadings, rawContent, url };
</file>

<file path=".netlify/build/chunks/project-two_BbjtZZaS.mjs">
const id = "project-two.md";
						const collection = "work";
						const slug = "project-two";
						const body = "\n## Project Overview\n\nThis project is developing a sophisticated knowledge management system inspired by tools like Obsidian and Roam Research. It focuses on creating a seamless experience for capturing, connecting, and retrieving information using a graph-based approach to knowledge.\n\n## Key Features\n\n- **Bidirectional Linking**: Create connections between notes that work in both directions, allowing for natural knowledge discovery.\n- **Graph Visualization**: Interactive visualization of your knowledge network, helping identify clusters and connections.\n- **Markdown-Based**: Simple yet powerful formatting using extended Markdown syntax.\n- **Local-First**: All data stored locally with optional encrypted sync.\n- **Customizable Workspace**: Flexible layout system with multiple panes and views.\n- **Advanced Search**: Full-text search with support for complex queries and filters.\n\n## Technical Implementation\n\nThe application is built using Electron for cross-platform compatibility, with a React frontend and TypeScript for type safety. The knowledge graph is stored in a specialized graph database optimized for quick traversal and querying.\n\n```typescript\n// Example of the bidirectional linking implementation\nclass NoteManager {\n  createLink(sourceNoteId: string, targetNoteId: string, linkText: string): Link {\n    // Create the forward link\n    const forwardLink = new Link({\n      sourceId: sourceNoteId,\n      targetId: targetNoteId,\n      text: linkText,\n      direction: 'forward'\n    });\n    \n    // Create the backward link\n    const backwardLink = new Link({\n      sourceId: targetNoteId,\n      targetId: sourceNoteId,\n      text: `Referenced by: ${this.getNoteTitle(sourceNoteId)}`,\n      direction: 'backward'\n    });\n    \n    // Store both links in the graph\n    this.graphDb.addLink(forwardLink);\n    this.graphDb.addLink(backwardLink);\n    \n    // Update the note content to include the link\n    this.updateNoteContent(sourceNoteId, linkText, targetNoteId);\n    \n    return forwardLink;\n  }\n}\n```\n\n## Current Status\n\nThe project is currently in active development with a working prototype that demonstrates the core functionality. The graph visualization and bidirectional linking features are operational, while advanced search capabilities are being refined.\n\n## Next Steps\n\n- Implement the encrypted sync functionality\n- Enhance the graph visualization with filtering and focus modes\n- Develop plugins system for extensibility\n- Create templates for different knowledge management workflows\n- Optimize performance for large knowledge bases\n";
						const data = {title:"Obsidian-Inspired Knowledge Management System",projectDate:new Date(1708387200000),status:"In Progress",featured:true,tags:["Knowledge Management","Electron","React","TypeScript","Graph Database"],ogImage:"/images/project-two-card.png",description:"A modern knowledge management system inspired by Obsidian, featuring bidirectional linking, graph visualization, and advanced search capabilities.",repoUrl:"https://github.com/username/knowledge-graph"};
						const _internal = {
							type: 'content',
							filePath: "C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-two.md",
							rawData: undefined,
						};

export { _internal, body, collection, data, id, slug };
</file>

<file path=".netlify/build/chunks/project-two_BWh9uzc8.mjs">
import { a as createComponent, m as maybeRenderHead, u as unescapeHTML, r as renderTemplate } from './astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import 'clsx';

const html = "<h2 id=\"project-overview\">Project Overview</h2>\n<p>This project is developing a sophisticated knowledge management system inspired by tools like Obsidian and Roam Research. It focuses on creating a seamless experience for capturing, connecting, and retrieving information using a graph-based approach to knowledge.</p>\n<h2 id=\"key-features\">Key Features</h2>\n<ul>\n<li><strong>Bidirectional Linking</strong>: Create connections between notes that work in both directions, allowing for natural knowledge discovery.</li>\n<li><strong>Graph Visualization</strong>: Interactive visualization of your knowledge network, helping identify clusters and connections.</li>\n<li><strong>Markdown-Based</strong>: Simple yet powerful formatting using extended Markdown syntax.</li>\n<li><strong>Local-First</strong>: All data stored locally with optional encrypted sync.</li>\n<li><strong>Customizable Workspace</strong>: Flexible layout system with multiple panes and views.</li>\n<li><strong>Advanced Search</strong>: Full-text search with support for complex queries and filters.</li>\n</ul>\n<h2 id=\"technical-implementation\">Technical Implementation</h2>\n<p>The application is built using Electron for cross-platform compatibility, with a React frontend and TypeScript for type safety. The knowledge graph is stored in a specialized graph database optimized for quick traversal and querying.</p>\n<pre class=\"astro-code github-dark\" style=\"background-color:#24292e;color:#e1e4e8; overflow-x: auto; white-space: pre-wrap; word-wrap: break-word;\" tabindex=\"0\" data-language=\"typescript\"><code><span class=\"line\"><span style=\"color:#6A737D\">// Example of the bidirectional linking implementation</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">class</span><span style=\"color:#B392F0\"> NoteManager</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#B392F0\">  createLink</span><span style=\"color:#E1E4E8\">(</span><span style=\"color:#FFAB70\">sourceNoteId</span><span style=\"color:#F97583\">:</span><span style=\"color:#79B8FF\"> string</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#FFAB70\">targetNoteId</span><span style=\"color:#F97583\">:</span><span style=\"color:#79B8FF\"> string</span><span style=\"color:#E1E4E8\">, </span><span style=\"color:#FFAB70\">linkText</span><span style=\"color:#F97583\">:</span><span style=\"color:#79B8FF\"> string</span><span style=\"color:#E1E4E8\">)</span><span style=\"color:#F97583\">:</span><span style=\"color:#B392F0\"> Link</span><span style=\"color:#E1E4E8\"> {</span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">    // Create the forward link</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    const</span><span style=\"color:#79B8FF\"> forwardLink</span><span style=\"color:#F97583\"> =</span><span style=\"color:#F97583\"> new</span><span style=\"color:#B392F0\"> Link</span><span style=\"color:#E1E4E8\">({</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">      sourceId: sourceNoteId,</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">      targetId: targetNoteId,</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">      text: linkText,</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">      direction: </span><span style=\"color:#9ECBFF\">'forward'</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    });</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    </span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">    // Create the backward link</span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    const</span><span style=\"color:#79B8FF\"> backwardLink</span><span style=\"color:#F97583\"> =</span><span style=\"color:#F97583\"> new</span><span style=\"color:#B392F0\"> Link</span><span style=\"color:#E1E4E8\">({</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">      sourceId: targetNoteId,</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">      targetId: sourceNoteId,</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">      text: </span><span style=\"color:#9ECBFF\">`Referenced by: ${</span><span style=\"color:#79B8FF\">this</span><span style=\"color:#9ECBFF\">.</span><span style=\"color:#B392F0\">getNoteTitle</span><span style=\"color:#9ECBFF\">(</span><span style=\"color:#E1E4E8\">sourceNoteId</span><span style=\"color:#9ECBFF\">)</span><span style=\"color:#9ECBFF\">}`</span><span style=\"color:#E1E4E8\">,</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">      direction: </span><span style=\"color:#9ECBFF\">'backward'</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    });</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    </span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">    // Store both links in the graph</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">    this</span><span style=\"color:#E1E4E8\">.graphDb.</span><span style=\"color:#B392F0\">addLink</span><span style=\"color:#E1E4E8\">(forwardLink);</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">    this</span><span style=\"color:#E1E4E8\">.graphDb.</span><span style=\"color:#B392F0\">addLink</span><span style=\"color:#E1E4E8\">(backwardLink);</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    </span></span>\n<span class=\"line\"><span style=\"color:#6A737D\">    // Update the note content to include the link</span></span>\n<span class=\"line\"><span style=\"color:#79B8FF\">    this</span><span style=\"color:#E1E4E8\">.</span><span style=\"color:#B392F0\">updateNoteContent</span><span style=\"color:#E1E4E8\">(sourceNoteId, linkText, targetNoteId);</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">    </span></span>\n<span class=\"line\"><span style=\"color:#F97583\">    return</span><span style=\"color:#E1E4E8\"> forwardLink;</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">  }</span></span>\n<span class=\"line\"><span style=\"color:#E1E4E8\">}</span></span>\n<span class=\"line\"></span></code></pre>\n<h2 id=\"current-status\">Current Status</h2>\n<p>The project is currently in active development with a working prototype that demonstrates the core functionality. The graph visualization and bidirectional linking features are operational, while advanced search capabilities are being refined.</p>\n<h2 id=\"next-steps\">Next Steps</h2>\n<ul>\n<li>Implement the encrypted sync functionality</li>\n<li>Enhance the graph visualization with filtering and focus modes</li>\n<li>Develop plugins system for extensibility</li>\n<li>Create templates for different knowledge management workflows</li>\n<li>Optimize performance for large knowledge bases</li>\n</ul>";

				const frontmatter = {"title":"Obsidian-Inspired Knowledge Management System","projectDate":"2024-02-20T00:00:00.000Z","status":"In Progress","featured":true,"tags":["Knowledge Management","Electron","React","TypeScript","Graph Database"],"ogImage":"/images/project-two-card.png","description":"A modern knowledge management system inspired by Obsidian, featuring bidirectional linking, graph visualization, and advanced search capabilities.","repoUrl":"https://github.com/username/knowledge-graph"};
				const file = "C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-two.md";
				const url = undefined;
				function rawContent() {
					return "\n## Project Overview\n\nThis project is developing a sophisticated knowledge management system inspired by tools like Obsidian and Roam Research. It focuses on creating a seamless experience for capturing, connecting, and retrieving information using a graph-based approach to knowledge.\n\n## Key Features\n\n- **Bidirectional Linking**: Create connections between notes that work in both directions, allowing for natural knowledge discovery.\n- **Graph Visualization**: Interactive visualization of your knowledge network, helping identify clusters and connections.\n- **Markdown-Based**: Simple yet powerful formatting using extended Markdown syntax.\n- **Local-First**: All data stored locally with optional encrypted sync.\n- **Customizable Workspace**: Flexible layout system with multiple panes and views.\n- **Advanced Search**: Full-text search with support for complex queries and filters.\n\n## Technical Implementation\n\nThe application is built using Electron for cross-platform compatibility, with a React frontend and TypeScript for type safety. The knowledge graph is stored in a specialized graph database optimized for quick traversal and querying.\n\n```typescript\n// Example of the bidirectional linking implementation\nclass NoteManager {\n  createLink(sourceNoteId: string, targetNoteId: string, linkText: string): Link {\n    // Create the forward link\n    const forwardLink = new Link({\n      sourceId: sourceNoteId,\n      targetId: targetNoteId,\n      text: linkText,\n      direction: 'forward'\n    });\n    \n    // Create the backward link\n    const backwardLink = new Link({\n      sourceId: targetNoteId,\n      targetId: sourceNoteId,\n      text: `Referenced by: ${this.getNoteTitle(sourceNoteId)}`,\n      direction: 'backward'\n    });\n    \n    // Store both links in the graph\n    this.graphDb.addLink(forwardLink);\n    this.graphDb.addLink(backwardLink);\n    \n    // Update the note content to include the link\n    this.updateNoteContent(sourceNoteId, linkText, targetNoteId);\n    \n    return forwardLink;\n  }\n}\n```\n\n## Current Status\n\nThe project is currently in active development with a working prototype that demonstrates the core functionality. The graph visualization and bidirectional linking features are operational, while advanced search capabilities are being refined.\n\n## Next Steps\n\n- Implement the encrypted sync functionality\n- Enhance the graph visualization with filtering and focus modes\n- Develop plugins system for extensibility\n- Create templates for different knowledge management workflows\n- Optimize performance for large knowledge bases\n";
				}
				function compiledContent() {
					return html;
				}
				function getHeadings() {
					return [{"depth":2,"slug":"project-overview","text":"Project Overview"},{"depth":2,"slug":"key-features","text":"Key Features"},{"depth":2,"slug":"technical-implementation","text":"Technical Implementation"},{"depth":2,"slug":"current-status","text":"Current Status"},{"depth":2,"slug":"next-steps","text":"Next Steps"}];
				}

				const Content = createComponent((result, _props, slots) => {
					const { layout, ...content } = frontmatter;
					content.file = file;
					content.url = url;

					return renderTemplate`${maybeRenderHead()}${unescapeHTML(html)}`;
				});

export { Content, compiledContent, Content as default, file, frontmatter, getHeadings, rawContent, url };
</file>

<file path=".netlify/build/chunks/project-two_CMkRBYFL.mjs">
async function getMod() {
						return import('./project-two_BWh9uzc8.mjs');
					}
					const collectedLinks = [];
					const collectedStyles = [];
					const collectedScripts = [];
					const defaultMod = { __astroPropagation: true, getMod, collectedLinks, collectedStyles, collectedScripts };

export { defaultMod as default };
</file>

<file path=".netlify/build/chunks/slugify_CHvHojPC.mjs">
function slugifyStr(str) {
  return str.toString().toLowerCase().trim().replace(/\s+/g, "-").replace(/&/g, "-and-").replace(/[^\w\-]+/g, "").replace(/\-\-+/g, "-");
}

export { slugifyStr as s };
</file>

<file path=".netlify/build/chunks/Tag_DSxhj6Zu.mjs">
import { c as createAstro, a as createComponent, m as maybeRenderHead, b as addAttribute, r as renderTemplate } from './astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import 'clsx';
/* empty css                         */

const $$Astro = createAstro("https://pvb.com");
const $$Tag = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Tag;
  const { tag, tagName, size = "sm", class: className = "" } = Astro2.props;
  return renderTemplate`${maybeRenderHead()}<a${addAttribute(`/tags/${tag}`, "href")}${addAttribute([
    "tag-link",
    size === "sm" ? "tag-sm" : "tag-lg",
    className
  ], "class:list")} data-astro-cid-blwjyjpt>
#${tagName} </a> `;
}, "C:/Users/<USER>/Desktop/pvb-astro/src/components/Tag.astro", void 0);

export { $$Tag as $ };
</file>

<file path=".netlify/build/entry.mjs">
import { renderers } from './renderers.mjs';
import { s as serverEntrypointModule } from './chunks/_@astrojs-ssr-adapter_CvSoi7hX.mjs';
import { manifest } from './manifest_B8ZiD9LG.mjs';
import { createExports } from '@astrojs/netlify/ssr-function.js';

const _page0 = () => import('./pages/_image.astro.mjs');
const _page1 = () => import('./pages/about.astro.mjs');
const _page2 = () => import('./pages/api/random-quote.json.astro.mjs');
const _page3 = () => import('./pages/blog/tag/_tag_.astro.mjs');
const _page4 = () => import('./pages/blog/timeline.astro.mjs');
const _page5 = () => import('./pages/blog/_slug_.astro.mjs');
const _page6 = () => import('./pages/blog.astro.mjs');
const _page7 = () => import('./pages/search.astro.mjs');
const _page8 = () => import('./pages/tags/_tag_.astro.mjs');
const _page9 = () => import('./pages/tags.astro.mjs');
const _page10 = () => import('./pages/work.astro.mjs');
const _page11 = () => import('./pages/work/_---slug_.astro.mjs');
const _page12 = () => import('./pages/index.astro.mjs');

const pageMap = new Map([
    ["node_modules/astro/dist/assets/endpoint/generic.js", _page0],
    ["src/pages/about.astro", _page1],
    ["src/pages/api/random-quote.json.js", _page2],
    ["src/pages/blog/tag/[tag].astro", _page3],
    ["src/pages/blog/timeline.astro", _page4],
    ["src/pages/blog/[slug].astro", _page5],
    ["src/pages/blog.astro", _page6],
    ["src/pages/search.astro", _page7],
    ["src/pages/tags/[tag]/index.astro", _page8],
    ["src/pages/tags/index.astro", _page9],
    ["src/pages/work.astro", _page10],
    ["src/pages/work/[...slug].astro", _page11],
    ["src/pages/index.astro", _page12]
]);
const serverIslandMap = new Map();
const _manifest = Object.assign(manifest, {
    pageMap,
    serverIslandMap,
    renderers,
    middleware: () => import('./_noop-middleware.mjs')
});
const _args = {
    "middlewareSecret": "791fbc5a-3576-43d3-b07d-e9ae1755d9f7"
};
const _exports = createExports(_manifest, _args);
const __astrojsSsrVirtualEntry = _exports.default;
const _start = 'start';
if (_start in serverEntrypointModule) {
	serverEntrypointModule[_start](_manifest, _args);
}

export { __astrojsSsrVirtualEntry as default, pageMap };
</file>

<file path=".netlify/build/manifest_B8ZiD9LG.mjs">
import '@astrojs/internal-helpers/path';
import 'cookie';
import 'kleur/colors';
import 'es-module-lexer';
import { N as NOOP_MIDDLEWARE_HEADER, k as decodeKey } from './chunks/astro/server_Dba0FyIl.mjs';
import 'clsx';
import 'html-escaper';

const NOOP_MIDDLEWARE_FN = async (_ctx, next) => {
  const response = await next();
  response.headers.set(NOOP_MIDDLEWARE_HEADER, "true");
  return response;
};

const codeToStatusMap = {
  // Implemented from tRPC error code table
  // https://trpc.io/docs/server/error-handling#error-codes
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  TIMEOUT: 405,
  CONFLICT: 409,
  PRECONDITION_FAILED: 412,
  PAYLOAD_TOO_LARGE: 413,
  UNSUPPORTED_MEDIA_TYPE: 415,
  UNPROCESSABLE_CONTENT: 422,
  TOO_MANY_REQUESTS: 429,
  CLIENT_CLOSED_REQUEST: 499,
  INTERNAL_SERVER_ERROR: 500
};
Object.entries(codeToStatusMap).reduce(
  // reverse the key-value pairs
  (acc, [key, value]) => ({ ...acc, [value]: key }),
  {}
);

function sanitizeParams(params) {
  return Object.fromEntries(
    Object.entries(params).map(([key, value]) => {
      if (typeof value === "string") {
        return [key, value.normalize().replace(/#/g, "%23").replace(/\?/g, "%3F")];
      }
      return [key, value];
    })
  );
}
function getParameter(part, params) {
  if (part.spread) {
    return params[part.content.slice(3)] || "";
  }
  if (part.dynamic) {
    if (!params[part.content]) {
      throw new TypeError(`Missing parameter: ${part.content}`);
    }
    return params[part.content];
  }
  return part.content.normalize().replace(/\?/g, "%3F").replace(/#/g, "%23").replace(/%5B/g, "[").replace(/%5D/g, "]");
}
function getSegment(segment, params) {
  const segmentPath = segment.map((part) => getParameter(part, params)).join("");
  return segmentPath ? "/" + segmentPath : "";
}
function getRouteGenerator(segments, addTrailingSlash) {
  return (params) => {
    const sanitizedParams = sanitizeParams(params);
    let trailing = "";
    if (addTrailingSlash === "always" && segments.length) {
      trailing = "/";
    }
    const path = segments.map((segment) => getSegment(segment, sanitizedParams)).join("") + trailing;
    return path || "/";
  };
}

function deserializeRouteData(rawRouteData) {
  return {
    route: rawRouteData.route,
    type: rawRouteData.type,
    pattern: new RegExp(rawRouteData.pattern),
    params: rawRouteData.params,
    component: rawRouteData.component,
    generate: getRouteGenerator(rawRouteData.segments, rawRouteData._meta.trailingSlash),
    pathname: rawRouteData.pathname || void 0,
    segments: rawRouteData.segments,
    prerender: rawRouteData.prerender,
    redirect: rawRouteData.redirect,
    redirectRoute: rawRouteData.redirectRoute ? deserializeRouteData(rawRouteData.redirectRoute) : void 0,
    fallbackRoutes: rawRouteData.fallbackRoutes.map((fallback) => {
      return deserializeRouteData(fallback);
    }),
    isIndex: rawRouteData.isIndex
  };
}

function deserializeManifest(serializedManifest) {
  const routes = [];
  for (const serializedRoute of serializedManifest.routes) {
    routes.push({
      ...serializedRoute,
      routeData: deserializeRouteData(serializedRoute.routeData)
    });
    const route = serializedRoute;
    route.routeData = deserializeRouteData(serializedRoute.routeData);
  }
  const assets = new Set(serializedManifest.assets);
  const componentMetadata = new Map(serializedManifest.componentMetadata);
  const inlinedScripts = new Map(serializedManifest.inlinedScripts);
  const clientDirectives = new Map(serializedManifest.clientDirectives);
  const serverIslandNameMap = new Map(serializedManifest.serverIslandNameMap);
  const key = decodeKey(serializedManifest.key);
  return {
    // in case user middleware exists, this no-op middleware will be reassigned (see plugin-ssr.ts)
    middleware() {
      return { onRequest: NOOP_MIDDLEWARE_FN };
    },
    ...serializedManifest,
    assets,
    componentMetadata,
    inlinedScripts,
    clientDirectives,
    routes,
    serverIslandNameMap,
    key
  };
}

const manifest = deserializeManifest({"hrefRoot":"file:///C:/Users/<USER>/Desktop/pvb-astro/","adapterName":"@astrojs/netlify","routes":[{"file":"about/index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/about","isIndex":false,"type":"page","pattern":"^\\/about\\/?$","segments":[[{"content":"about","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/about.astro","pathname":"/about","prerender":true,"fallbackRoutes":[],"_meta":{"trailingSlash":"ignore"}}},{"file":"blog/timeline/index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/blog/timeline","isIndex":false,"type":"page","pattern":"^\\/blog\\/timeline\\/?$","segments":[[{"content":"blog","dynamic":false,"spread":false}],[{"content":"timeline","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/blog/timeline.astro","pathname":"/blog/timeline","prerender":true,"fallbackRoutes":[],"_meta":{"trailingSlash":"ignore"}}},{"file":"blog/index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/blog","isIndex":false,"type":"page","pattern":"^\\/blog\\/?$","segments":[[{"content":"blog","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/blog.astro","pathname":"/blog","prerender":true,"fallbackRoutes":[],"_meta":{"trailingSlash":"ignore"}}},{"file":"search/index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/search","isIndex":false,"type":"page","pattern":"^\\/search\\/?$","segments":[[{"content":"search","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/search.astro","pathname":"/search","prerender":true,"fallbackRoutes":[],"_meta":{"trailingSlash":"ignore"}}},{"file":"tags/index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/tags","isIndex":true,"type":"page","pattern":"^\\/tags\\/?$","segments":[[{"content":"tags","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/tags/index.astro","pathname":"/tags","prerender":true,"fallbackRoutes":[],"_meta":{"trailingSlash":"ignore"}}},{"file":"work/index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/work","isIndex":false,"type":"page","pattern":"^\\/work\\/?$","segments":[[{"content":"work","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/work.astro","pathname":"/work","prerender":true,"fallbackRoutes":[],"_meta":{"trailingSlash":"ignore"}}},{"file":"index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/","isIndex":true,"type":"page","pattern":"^\\/$","segments":[],"params":[],"component":"src/pages/index.astro","pathname":"/","prerender":true,"fallbackRoutes":[],"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"type":"endpoint","isIndex":false,"route":"/_image","pattern":"^\\/_image$","segments":[[{"content":"_image","dynamic":false,"spread":false}]],"params":[],"component":"node_modules/astro/dist/assets/endpoint/generic.js","pathname":"/_image","prerender":false,"fallbackRoutes":[],"_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"route":"/api/random-quote.json","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/random-quote\\.json\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"random-quote.json","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/random-quote.json.js","pathname":"/api/random-quote.json","prerender":false,"fallbackRoutes":[],"_meta":{"trailingSlash":"ignore"}}}],"site":"https://pvb.com","base":"/","trailingSlash":"ignore","compressHTML":true,"componentMetadata":[["C:/Users/<USER>/Desktop/pvb-astro/src/components/BlogPostCard.astro",{"propagation":"in-tree","containsHead":false}],["C:/Users/<USER>/Desktop/pvb-astro/src/pages/tags/[tag]/index.astro",{"propagation":"in-tree","containsHead":true}],["\u0000@astro-page:src/pages/tags/[tag]/index@_@astro",{"propagation":"in-tree","containsHead":false}],["\u0000@astrojs-ssr-virtual-entry",{"propagation":"in-tree","containsHead":false}],["C:/Users/<USER>/Desktop/pvb-astro/src/components/ProjectCard.astro",{"propagation":"in-tree","containsHead":false}],["C:/Users/<USER>/Desktop/pvb-astro/src/pages/work.astro",{"propagation":"in-tree","containsHead":true}],["\u0000@astro-page:src/pages/work@_@astro",{"propagation":"in-tree","containsHead":false}],["C:/Users/<USER>/Desktop/pvb-astro/src/layouts/BlogPost.astro",{"propagation":"in-tree","containsHead":false}],["C:/Users/<USER>/Desktop/pvb-astro/src/pages/blog/[slug].astro",{"propagation":"in-tree","containsHead":true}],["\u0000@astro-page:src/pages/blog/[slug]@_@astro",{"propagation":"in-tree","containsHead":false}],["C:/Users/<USER>/Desktop/pvb-astro/src/pages/work/[...slug].astro",{"propagation":"in-tree","containsHead":true}],["\u0000@astro-page:src/pages/work/[...slug]@_@astro",{"propagation":"in-tree","containsHead":false}],["\u0000astro:content",{"propagation":"in-tree","containsHead":false}],["C:/Users/<USER>/Desktop/pvb-astro/src/pages/api/random-quote.json.js",{"propagation":"in-tree","containsHead":false}],["\u0000@astro-page:src/pages/api/random-quote.json@_@js",{"propagation":"in-tree","containsHead":false}],["C:/Users/<USER>/Desktop/pvb-astro/src/pages/blog.astro",{"propagation":"in-tree","containsHead":true}],["\u0000@astro-page:src/pages/blog@_@astro",{"propagation":"in-tree","containsHead":false}],["C:/Users/<USER>/Desktop/pvb-astro/src/pages/blog/tag/[tag].astro",{"propagation":"in-tree","containsHead":true}],["\u0000@astro-page:src/pages/blog/tag/[tag]@_@astro",{"propagation":"in-tree","containsHead":false}],["C:/Users/<USER>/Desktop/pvb-astro/src/pages/blog/timeline.astro",{"propagation":"in-tree","containsHead":true}],["\u0000@astro-page:src/pages/blog/timeline@_@astro",{"propagation":"in-tree","containsHead":false}],["C:/Users/<USER>/Desktop/pvb-astro/src/pages/tags/index.astro",{"propagation":"in-tree","containsHead":true}],["\u0000@astro-page:src/pages/tags/index@_@astro",{"propagation":"in-tree","containsHead":false}],["C:/Users/<USER>/Desktop/pvb-astro/src/pages/about.astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/Desktop/pvb-astro/src/pages/index.astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/Desktop/pvb-astro/src/pages/search.astro",{"propagation":"none","containsHead":true}]],"renderers":[],"clientDirectives":[["idle","(()=>{var l=(o,t)=>{let i=async()=>{await(await o())()},e=typeof t.value==\"object\"?t.value:void 0,s={timeout:e==null?void 0:e.timeout};\"requestIdleCallback\"in window?window.requestIdleCallback(i,s):setTimeout(i,s.timeout||200)};(self.Astro||(self.Astro={})).idle=l;window.dispatchEvent(new Event(\"astro:idle\"));})();"],["load","(()=>{var e=async t=>{await(await t())()};(self.Astro||(self.Astro={})).load=e;window.dispatchEvent(new Event(\"astro:load\"));})();"],["media","(()=>{var s=(i,t)=>{let a=async()=>{await(await i())()};if(t.value){let e=matchMedia(t.value);e.matches?a():e.addEventListener(\"change\",a,{once:!0})}};(self.Astro||(self.Astro={})).media=s;window.dispatchEvent(new Event(\"astro:media\"));})();"],["only","(()=>{var e=async t=>{await(await t())()};(self.Astro||(self.Astro={})).only=e;window.dispatchEvent(new Event(\"astro:only\"));})();"],["visible","(()=>{var l=(s,i,o)=>{let r=async()=>{await(await s())()},t=typeof i.value==\"object\"?i.value:void 0,c={rootMargin:t==null?void 0:t.rootMargin},n=new IntersectionObserver(e=>{for(let a of e)if(a.isIntersecting){n.disconnect(),r();break}},c);for(let e of o.children)n.observe(e)};(self.Astro||(self.Astro={})).visible=l;window.dispatchEvent(new Event(\"astro:visible\"));})();"]],"entryModules":{"\u0000noop-middleware":"_noop-middleware.mjs","\u0000@astro-page:node_modules/astro/dist/assets/endpoint/generic@_@js":"pages/_image.astro.mjs","\u0000@astro-page:src/pages/about@_@astro":"pages/about.astro.mjs","\u0000@astro-page:src/pages/api/random-quote.json@_@js":"pages/api/random-quote.json.astro.mjs","\u0000@astro-page:src/pages/blog/tag/[tag]@_@astro":"pages/blog/tag/_tag_.astro.mjs","\u0000@astro-page:src/pages/blog/timeline@_@astro":"pages/blog/timeline.astro.mjs","\u0000@astro-page:src/pages/blog/[slug]@_@astro":"pages/blog/_slug_.astro.mjs","\u0000@astro-page:src/pages/blog@_@astro":"pages/blog.astro.mjs","\u0000@astro-page:src/pages/search@_@astro":"pages/search.astro.mjs","\u0000@astro-page:src/pages/tags/[tag]/index@_@astro":"pages/tags/_tag_.astro.mjs","\u0000@astro-page:src/pages/tags/index@_@astro":"pages/tags.astro.mjs","\u0000@astro-page:src/pages/work@_@astro":"pages/work.astro.mjs","\u0000@astro-page:src/pages/work/[...slug]@_@astro":"pages/work/_---slug_.astro.mjs","\u0000@astro-page:src/pages/index@_@astro":"pages/index.astro.mjs","\u0000@astrojs-ssr-virtual-entry":"entry.mjs","\u0000@astro-renderers":"renderers.mjs","\u0000@astrojs-ssr-adapter":"<EMAIL>","\u0000@astrojs-manifest":"manifest_B8ZiD9LG.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/blog/age-of-synthesis.md?astroContentCollectionEntry=true":"chunks/age-of-synthesis_B-VSexue.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/blog/hello-world.md?astroContentCollectionEntry=true":"chunks/hello-world_BwOf8HZz.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/blog/mastery-and-knowledge.md?astroContentCollectionEntry=true":"chunks/mastery-and-knowledge_CkJKa8VC.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/quotes/learning.md?astroContentCollectionEntry=true":"chunks/learning_DcKpeM5O.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/quotes/mindfulness.md?astroContentCollectionEntry=true":"chunks/mindfulness_DTX3H095.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/quotes/philosophy.md?astroContentCollectionEntry=true":"chunks/philosophy_fXVfYPzN.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-five.md?astroContentCollectionEntry=true":"chunks/project-five_BCHmoapR.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-four.md?astroContentCollectionEntry=true":"chunks/project-four_BiijU9ph.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-one.md?astroContentCollectionEntry=true":"chunks/project-one_kU0mYB6U.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-three.md?astroContentCollectionEntry=true":"chunks/project-three_C8CEKmI8.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-two.md?astroContentCollectionEntry=true":"chunks/project-two_BbjtZZaS.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/blog/age-of-synthesis.md?astroPropagatedAssets":"chunks/age-of-synthesis_DqChI2cq.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/blog/hello-world.md?astroPropagatedAssets":"chunks/hello-world_BNhj20-t.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/blog/mastery-and-knowledge.md?astroPropagatedAssets":"chunks/mastery-and-knowledge_B3L9fdmh.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/quotes/learning.md?astroPropagatedAssets":"chunks/learning_SfTw29v4.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/quotes/mindfulness.md?astroPropagatedAssets":"chunks/mindfulness_BG5BPaMj.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/quotes/philosophy.md?astroPropagatedAssets":"chunks/philosophy_D9CDMOq1.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-five.md?astroPropagatedAssets":"chunks/project-five_B9I66poG.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-four.md?astroPropagatedAssets":"chunks/project-four_DlJCn5JQ.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-one.md?astroPropagatedAssets":"chunks/project-one_BLBOXsjf.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-three.md?astroPropagatedAssets":"chunks/project-three_CCeq-lTS.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-two.md?astroPropagatedAssets":"chunks/project-two_CMkRBYFL.mjs","\u0000astro:asset-imports":"chunks/_astro_asset-imports_D9aVaOQr.mjs","\u0000astro:data-layer-content":"chunks/_astro_data-layer-content_BcEe_9wP.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/blog/age-of-synthesis.md":"chunks/age-of-synthesis_DLAOmwU2.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/blog/hello-world.md":"chunks/hello-world_Bu55fqv5.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/blog/mastery-and-knowledge.md":"chunks/mastery-and-knowledge_C1zLMmNr.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/quotes/learning.md":"chunks/learning_BtzpT67X.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/quotes/mindfulness.md":"chunks/mindfulness_3c8gSlQq.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/quotes/philosophy.md":"chunks/philosophy_EjjKZrv8.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-five.md":"chunks/project-five_bFgqsuz9.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-four.md":"chunks/project-four_BWkJ1E-x.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-one.md":"chunks/project-one_Cse1VnLC.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-three.md":"chunks/project-three_Dplria35.mjs","C:/Users/<USER>/Desktop/pvb-astro/src/content/work/project-two.md":"chunks/project-two_BWh9uzc8.mjs","/astro/hoisted.js?q=0":"_astro/hoisted.C6XmR4Gm.js","/astro/hoisted.js?q=1":"_astro/hoisted.DSoxCwEA.js","astro:scripts/before-hydration.js":""},"inlinedScripts":[],"assets":["/_astro/_slug_.CLq3GFE2.css","/_astro/_slug_.DONxtleH.css","/_astro/_tag_.Rb4hvsWq.css","/_astro/blog.DWMZHUHN.css","/_astro/work.DbR1XWHN.css","/fonts/georgia-bold-2.ttf","/fonts/georgia-ref.ttf","/fonts/serif12-beta-regular.otf","/images/about.png","/images/blackgranite.jpg","/images/blackgranite.png","/images/concrete.png","/images/OIP.jpg","/images/whitemarble.png","/scripts/main.js","/about/index.html","/blog/timeline/index.html","/blog/index.html","/search/index.html","/tags/index.html","/work/index.html","/index.html"],"buildFormat":"directory","checkOrigin":false,"serverIslandNameMap":[],"key":"V6qj4omlYy4jp+5WfqxFNW2LDQ1C8N0L3oJFFy0D9Q8=","experimentalEnvGetSecretEnabled":false});

export { manifest };
</file>

<file path=".netlify/build/pages/_image.astro.mjs">
import { isRemotePath } from '@astrojs/internal-helpers/path';
import { A as AstroError, f as NoImageMetadata, F as FailedToFetchRemoteImageDimensions, E as ExpectedImageOptions, g as ExpectedImage, h as ExpectedNotESMImage, r as resolveSrc, i as isRemoteImage, j as isESMImportedImage, k as isLocalService, D as DEFAULT_HASH_PROPS, l as InvalidImageService, m as ImageMissingAlt, n as isRemoteAllowed } from '../chunks/astro/assets-service_DIMzS0Of.mjs';
import { c as createAstro, a as createComponent, m as maybeRenderHead, b as addAttribute, s as spreadAttributes, r as renderTemplate } from '../chunks/astro/server_Dba0FyIl.mjs';
import 'clsx';
import * as mime from 'mrmime';
export { renderers } from '../renderers.mjs';

function isImageMetadata(src) {
  return src.fsPath && !("fsPath" in src);
}

const decoder = new TextDecoder();
const toUTF8String = (input, start = 0, end = input.length) => decoder.decode(input.slice(start, end));
const toHexString = (input, start = 0, end = input.length) => input.slice(start, end).reduce((memo, i) => memo + ("0" + i.toString(16)).slice(-2), "");
const readInt16LE = (input, offset = 0) => {
  const val = input[offset] + input[offset + 1] * 2 ** 8;
  return val | (val & 2 ** 15) * 131070;
};
const readUInt16BE = (input, offset = 0) => input[offset] * 2 ** 8 + input[offset + 1];
const readUInt16LE = (input, offset = 0) => input[offset] + input[offset + 1] * 2 ** 8;
const readUInt24LE = (input, offset = 0) => input[offset] + input[offset + 1] * 2 ** 8 + input[offset + 2] * 2 ** 16;
const readInt32LE = (input, offset = 0) => input[offset] + input[offset + 1] * 2 ** 8 + input[offset + 2] * 2 ** 16 + (input[offset + 3] << 24);
const readUInt32BE = (input, offset = 0) => input[offset] * 2 ** 24 + input[offset + 1] * 2 ** 16 + input[offset + 2] * 2 ** 8 + input[offset + 3];
const readUInt32LE = (input, offset = 0) => input[offset] + input[offset + 1] * 2 ** 8 + input[offset + 2] * 2 ** 16 + input[offset + 3] * 2 ** 24;
const methods = {
  readUInt16BE,
  readUInt16LE,
  readUInt32BE,
  readUInt32LE
};
function readUInt(input, bits, offset, isBigEndian) {
  offset = offset || 0;
  const endian = isBigEndian ? "BE" : "LE";
  const methodName = "readUInt" + bits + endian;
  return methods[methodName](input, offset);
}
function readBox(buffer, offset) {
  if (buffer.length - offset < 4) return;
  const boxSize = readUInt32BE(buffer, offset);
  if (buffer.length - offset < boxSize) return;
  return {
    name: toUTF8String(buffer, 4 + offset, 8 + offset),
    offset,
    size: boxSize
  };
}
function findBox(buffer, boxName, offset) {
  while (offset < buffer.length) {
    const box = readBox(buffer, offset);
    if (!box) break;
    if (box.name === boxName) return box;
    offset += box.size;
  }
}

const BMP = {
  validate: (input) => toUTF8String(input, 0, 2) === "BM",
  calculate: (input) => ({
    height: Math.abs(readInt32LE(input, 22)),
    width: readUInt32LE(input, 18)
  })
};

const TYPE_ICON = 1;
const SIZE_HEADER$1 = 2 + 2 + 2;
const SIZE_IMAGE_ENTRY = 1 + 1 + 1 + 1 + 2 + 2 + 4 + 4;
function getSizeFromOffset(input, offset) {
  const value = input[offset];
  return value === 0 ? 256 : value;
}
function getImageSize$1(input, imageIndex) {
  const offset = SIZE_HEADER$1 + imageIndex * SIZE_IMAGE_ENTRY;
  return {
    height: getSizeFromOffset(input, offset + 1),
    width: getSizeFromOffset(input, offset)
  };
}
const ICO = {
  validate(input) {
    const reserved = readUInt16LE(input, 0);
    const imageCount = readUInt16LE(input, 4);
    if (reserved !== 0 || imageCount === 0) return false;
    const imageType = readUInt16LE(input, 2);
    return imageType === TYPE_ICON;
  },
  calculate(input) {
    const nbImages = readUInt16LE(input, 4);
    const imageSize = getImageSize$1(input, 0);
    if (nbImages === 1) return imageSize;
    const imgs = [imageSize];
    for (let imageIndex = 1; imageIndex < nbImages; imageIndex += 1) {
      imgs.push(getImageSize$1(input, imageIndex));
    }
    return {
      height: imageSize.height,
      images: imgs,
      width: imageSize.width
    };
  }
};

const TYPE_CURSOR = 2;
const CUR = {
  validate(input) {
    const reserved = readUInt16LE(input, 0);
    const imageCount = readUInt16LE(input, 4);
    if (reserved !== 0 || imageCount === 0) return false;
    const imageType = readUInt16LE(input, 2);
    return imageType === TYPE_CURSOR;
  },
  calculate: (input) => ICO.calculate(input)
};

const DDS = {
  validate: (input) => readUInt32LE(input, 0) === 542327876,
  calculate: (input) => ({
    height: readUInt32LE(input, 12),
    width: readUInt32LE(input, 16)
  })
};

const gifRegexp = /^GIF8[79]a/;
const GIF = {
  validate: (input) => gifRegexp.test(toUTF8String(input, 0, 6)),
  calculate: (input) => ({
    height: readUInt16LE(input, 8),
    width: readUInt16LE(input, 6)
  })
};

const brandMap = {
  avif: "avif",
  mif1: "heif",
  msf1: "heif",
  // hief-sequence
  heic: "heic",
  heix: "heic",
  hevc: "heic",
  // heic-sequence
  hevx: "heic"
  // heic-sequence
};
function detectBrands(buffer, start, end) {
  let brandsDetected = {};
  for (let i = start; i <= end; i += 4) {
    const brand = toUTF8String(buffer, i, i + 4);
    if (brand in brandMap) {
      brandsDetected[brand] = 1;
    }
  }
  if ("avif" in brandsDetected) {
    return "avif";
  } else if ("heic" in brandsDetected || "heix" in brandsDetected || "hevc" in brandsDetected || "hevx" in brandsDetected) {
    return "heic";
  } else if ("mif1" in brandsDetected || "msf1" in brandsDetected) {
    return "heif";
  }
}
const HEIF = {
  validate(buffer) {
    const ftype = toUTF8String(buffer, 4, 8);
    const brand = toUTF8String(buffer, 8, 12);
    return "ftyp" === ftype && brand in brandMap;
  },
  calculate(buffer) {
    const metaBox = findBox(buffer, "meta", 0);
    const iprpBox = metaBox && findBox(buffer, "iprp", metaBox.offset + 12);
    const ipcoBox = iprpBox && findBox(buffer, "ipco", iprpBox.offset + 8);

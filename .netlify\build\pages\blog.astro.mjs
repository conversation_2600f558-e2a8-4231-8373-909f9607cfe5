import { a as createComponent, d as renderComponent, r as renderTemplate, b as addAttribute, m as maybeRenderHead } from '../chunks/astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import { g as getCollection } from '../chunks/_astro_content_CcaQj6Wl.mjs';
import { $ as $$Layout } from '../chunks/Layout_rXbp99fE.mjs';
/* empty css                                */
export { renderers } from '../renderers.mjs';

var __freeze = Object.freeze;
var __defProp = Object.defineProperty;
var __template = (cooked, raw) => __freeze(__defProp(cooked, "raw", { value: __freeze(cooked.slice()) }));
var _a;
const $$Blog = createComponent(async ($$result, $$props, $$slots) => {
  let sortedPosts = [];
  let mostRecentPost;
  let allTags = [];
  try {
    const posts = await getCollection("blog");
    const nonDraftPosts = posts.filter(({ data }) => !data.draft);
    sortedPosts = [...nonDraftPosts].sort(
      (a, b) => Math.floor(new Date(b.data.pubDatetime).getTime() / 1e3) - Math.floor(new Date(a.data.pubDatetime).getTime() / 1e3)
    );
    mostRecentPost = sortedPosts[0];
    allTags = [...new Set(nonDraftPosts.flatMap((post) => post.data.tags || []))].sort();
  } catch (e) {
    console.error("Error loading blog posts:", e);
    allTags = ["philosophy", "wisdom", "mastery", "AI", "synthesis", "innovation"];
  }
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "pageTitle": "Blog | PVB", "isHomePage": false, "accentColor": "#f0f0f0", "bgColor": "rgba(10, 10, 10, 0.94)", "backgroundImageUrl": "/images/blackgranite.png", "bodyDataPage": "blog", "data-astro-cid-ijnerlr2": true }, { "default": async ($$result2) => renderTemplate(_a || (_a = __template(["  ", '<div class="blog-header" data-astro-cid-ijnerlr2> <div class="blog-title" data-astro-cid-ijnerlr2>blog</div> </div>  <div class="page-container" data-astro-cid-ijnerlr2> <!-- Left Sidebar --> <div class="blog-sidebar" data-astro-cid-ijnerlr2> <!-- Search Bar --> <div class="search-container sidebar-section" data-astro-cid-ijnerlr2> <a href="/search" class="search-link" data-astro-cid-ijnerlr2> <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-astro-cid-ijnerlr2> <circle cx="11" cy="11" r="8" data-astro-cid-ijnerlr2></circle> <line x1="21" y1="21" x2="16.65" y2="16.65" data-astro-cid-ijnerlr2></line> </svg> <span data-astro-cid-ijnerlr2>search</span> </a> </div> <!-- Archive Button --> <div class="archive-container sidebar-section" data-astro-cid-ijnerlr2> <a href="/blog/timeline" class="archive-link" data-astro-cid-ijnerlr2> <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-astro-cid-ijnerlr2> <path d="M3 3v18h18" data-astro-cid-ijnerlr2></path> <path d="M7 17l4-4 4 4 4-4" data-astro-cid-ijnerlr2></path> <path d="M7 11l4-4 4 4 4-4" data-astro-cid-ijnerlr2></path> </svg> <span data-astro-cid-ijnerlr2>archive</span> </a> </div> <!-- Subscribe --> <div class="subscribe-container sidebar-section" data-astro-cid-ijnerlr2> <a href="#" class="subscribe-link" data-astro-cid-ijnerlr2>subscribe by email</a> </div> <!-- Tags Filter (Collapsible) --> <div class="tags-container sidebar-section" data-astro-cid-ijnerlr2> <button class="tags-toggle" id="tags-toggle" aria-expanded="false" aria-controls="tags-list" data-astro-cid-ijnerlr2> <span class="tags-title" data-astro-cid-ijnerlr2> <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-astro-cid-ijnerlr2> <path d="M9 5H2v7l6.29 6.29c.94.94 2.48.94 3.42 0l7.58-7.58c.94-.94.94-2.48 0-3.42L13 2c-.94-.94-2.48-.94-3.42 0L9 5Z" data-astro-cid-ijnerlr2></path> <path d="M6 9.01V9" data-astro-cid-ijnerlr2></path> </svg> <span data-astro-cid-ijnerlr2>tags</span> </span> <span class="toggle-icon" data-astro-cid-ijnerlr2>+</span> </button> <div class="tags-list" id="tags-list" hidden data-astro-cid-ijnerlr2> <a href="/tags" class="tag-link all-tags-link" data-astro-cid-ijnerlr2>all tags</a> ', ' </div> </div> </div> <!-- Main Content --> <div class="blog-content" data-astro-cid-ijnerlr2> <!-- Recent Post Preview --> ', ' <!-- Dividing Line --> <div class="posts-divider" data-astro-cid-ijnerlr2></div> <!-- More Posts (without header) --> <div class="more-posts" data-astro-cid-ijnerlr2> ', ` </div> </div> </div> <script>
    // Make the bottom button slightly transparent on scroll
    document.addEventListener('DOMContentLoaded', function() {
      const bottomButton = document.querySelector('.nav-circle.bottom-center');

      window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
          bottomButton.style.opacity = "0.7";
        } else {
          bottomButton.style.opacity = "1";
        }
      });

      // Fix for navigation button - Attach event to the navigation button
      const backButton = document.querySelector('.nav-circle.top-left');
      if (backButton) {
        backButton.addEventListener('click', () => {
          window.history.back();
        });
      }

      // Toggle tags list
      const tagsToggle = document.getElementById('tags-toggle');
      const tagsList = document.getElementById('tags-list');

      if (tagsToggle && tagsList) {
        // Function to close tags list
        const closeTagsList = () => {
          tagsToggle.setAttribute('aria-expanded', 'false');
          tagsList.setAttribute('hidden', '');
        };

        // Toggle tags list when clicking the toggle button
        tagsToggle.addEventListener('click', () => {
          const expanded = tagsToggle.getAttribute('aria-expanded') === 'true';
          tagsToggle.setAttribute('aria-expanded', !expanded);

          if (expanded) {
            tagsList.setAttribute('hidden', '');
          } else {
            tagsList.removeAttribute('hidden');
          }
        });

        // Close tags list when clicking outside on mobile
        if (window.innerWidth <= 768) {
          document.addEventListener('click', (e) => {
            if (!tagsList.hasAttribute('hidden') &&
                !tagsToggle.contains(e.target) &&
                !tagsList.contains(e.target)) {
              closeTagsList();
            }
          });

          // Close tags list when pressing escape
          document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && !tagsList.hasAttribute('hidden')) {
              closeTagsList();
            }
          });
        }
      }
    });
  <\/script> `])), maybeRenderHead(), allTags.map((tag) => renderTemplate`<a${addAttribute(`/blog/tag/${tag}`, "href")} class="tag-link" data-astro-cid-ijnerlr2>${tag}</a>`), mostRecentPost && renderTemplate`<div class="recent-post" data-astro-cid-ijnerlr2> <h2 class="post-title" data-astro-cid-ijnerlr2> <a${addAttribute(`/blog/${mostRecentPost.slug}`, "href")} data-astro-cid-ijnerlr2>${mostRecentPost.data.title}</a> </h2> <div class="post-date" data-astro-cid-ijnerlr2>${new Date(mostRecentPost.data.pubDatetime).toLocaleDateString("en-US", { day: "numeric", month: "long", year: "numeric" })}</div> <div class="post-preview" data-astro-cid-ijnerlr2> <p class="post-description" data-astro-cid-ijnerlr2>${mostRecentPost.data.description}</p> <a${addAttribute(`/blog/${mostRecentPost.slug}`, "href")} class="read-more" data-astro-cid-ijnerlr2>
read <span class="read-more-arrow" data-astro-cid-ijnerlr2>→</span> </a> </div> </div>`, sortedPosts.slice(1).map((post) => renderTemplate`<div class="post-item" data-astro-cid-ijnerlr2> <h3 class="post-item-title" data-astro-cid-ijnerlr2> <a${addAttribute(`/blog/${post.slug}`, "href")} data-astro-cid-ijnerlr2>${post.data.title}</a> </h3> <div class="post-item-date" data-astro-cid-ijnerlr2> ${new Date(post.data.pubDatetime).toLocaleDateString("en-US", { day: "numeric", month: "long", year: "numeric" })} </div> <p class="post-item-description" data-astro-cid-ijnerlr2>${post.data.description}</p> <a${addAttribute(`/blog/${post.slug}`, "href")} class="post-item-read-more" data-astro-cid-ijnerlr2>read</a> </div>`)) })} `;
}, "C:/Users/<USER>/Desktop/pvb-astro/src/pages/blog.astro", void 0);

const $$file = "C:/Users/<USER>/Desktop/pvb-astro/src/pages/blog.astro";
const $$url = "/blog";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Blog,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

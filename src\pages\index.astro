---
// Home page
import Layout from '../layouts/Layout.astro';
---

<Layout
    pageTitle="PVB Home"
    isHomePage={true}
    accentColor="#3a2c23"
    bgColor="rgba(250, 246, 242, 0)"
    backgroundImageUrl="/images/whitemarble.png"
    bodyDataPage="home"
>
    <div class="quote-container">
       <div class="quote-indicator-wrapper" id="quote-indicator-wrapper" title="Quote Info" aria-label="Show Quote Information">
         <div class="quote-indicator" id="quote-indicator"></div>
       </div>
      <p class="quote-text"></p>
      <p class="quote-attribution"></p>
    </div>
</Layout>

<style>
  /* Quote container styling - exactly matching the reference HTML */
  .quote-container {
    text-align: center;
    max-width: 550px;
    padding: 20px;
    position: relative;
    margin-top: 0; /* Center aligned by layout */
  }
  
  .quote-container .quote-indicator-wrapper {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-bottom: 25px;
    width: 44px; /* Match reference HTML */
    height: 44px; /* Match reference HTML */
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    z-index: 50;
  }
  
  .quote-indicator {
    width: 12px; /* Match reference HTML */
    height: 2px; /* Match reference HTML */
    background-color: var(--color-inactive, #8a8178);
    border-radius: 1px;
    transition: transform var(--transition-duration) var(--easing-standard),
                width var(--transition-duration) var(--easing-standard),
                height var(--transition-duration) var(--easing-standard),
                background-color var(--transition-duration) var(--easing-standard),
                border var(--transition-duration) var(--easing-standard),
                border-radius var(--transition-duration) var(--easing-standard);
  }
  
  .quote-indicator-wrapper.active .quote-indicator {
    width: 20px; /* Match reference HTML */
    height: 20px; /* Match reference HTML */
    background-color: transparent;
    border: var(--circle-border-width) solid var(--color-inactive, #8a8178);
    border-radius: 50%;
  }
  
  .quote-indicator-wrapper.active:hover .quote-indicator {
    transform: scale(1.15);
  }
  
  .quote-text {
    font-size: 1.05rem;
    line-height: 1.5;
    margin-bottom: 0.8em;
    color: var(--color-text, #3a2c23);
    font-family: 'Georgia Custom', Georgia, serif;
  }
  
  .quote-attribution {
    font-size: 0.8rem;
    font-style: italic;
    color: var(--color-text-secondary, #6a5a4f);
    font-family: 'Georgia Custom', Georgia, serif;
  }
  
  /* Responsive */
  @media (max-width: 768px) {
    .quote-container {
      max-width: 90%;
      padding: 15px;
    }
    
    .quote-container .quote-indicator-wrapper {
      margin-bottom: 20px;
    }
    
    .quote-text {
      font-size: 1rem;
    }
    
    .quote-attribution {
      font-size: 0.75rem;
    }
  }
</style>
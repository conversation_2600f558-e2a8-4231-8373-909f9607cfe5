import { defineConfig } from 'astro/config';
import { SITE } from './src/config';

import react from '@astrojs/react';

// https://astro.build/config
export default defineConfig({
  // This should match your actual Netlify domain, not pvb.com
  site: 'https://pvb-astro.netlify.app',

  // This ensures all paths are relative to the root
  base: '/',

  output: 'static',

  markdown: {
    shikiConfig: {
      theme: 'github-dark',
      wrap: true
    },
  },

  experimental: {
    contentCollectionCache: true,
    viewTransitions: true, // Enable View Transitions
  },

  vite: {
    optimizeDeps: {
      exclude: ['@astrojs/mdx']
    }
  },

  integrations: [react()]
});
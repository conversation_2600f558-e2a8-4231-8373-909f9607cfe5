---
import "../styles/global.css";

// Import necessary components
interface Props {
  title?: string;
}

const { title = "UTOPIA WORLD" } = Astro.props;
---

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <title>{title}</title>
  </head>
  <body class="utopia-body">
    <header class="utopia-header">
      <a href="/work" class="utopia-nav-link back-btn">[Back]</a>
      <div class="utopia-logo">
        <!-- <PERSON><PERSON> could go here -->
      </div>
      <a href="#" class="utopia-nav-link shop-btn">Shop</a>
    </header>

    <main class="utopia-main">
      <slot />
    </main>

    <!-- Optional Newsletter signup -->
    <div class="newsletter-container">
      <form class="newsletter-form">
        <input type="email" placeholder="Enter email for updates" required />
        <button type="submit">Sign Up</button>
      </form>
    </div>
  </body>
</html>

<style>
  .utopia-body {
    background-color: #000;
    color: #fff;
    font-family: sans-serif;
    margin: 0;
    padding: 0;
    min-height: 100vh;
  }

  .utopia-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    position: relative;
  }

  .utopia-nav-link {
    color: #fff;
    text-decoration: none;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    transition: opacity 0.3s ease;
  }

  .utopia-nav-link:hover {
    opacity: 0.7;
  }

  .utopia-logo {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    font-size: 1.2rem;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.1em;
  }

  .utopia-main {
    margin-top: 1rem;
  }

  .newsletter-container {
    margin: 4rem auto;
    max-width: 500px;
    padding: 0 1rem;
  }

  .newsletter-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .newsletter-form input {
    padding: 0.8rem 1rem;
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 0.9rem;
  }

  .newsletter-form button {
    padding: 0.8rem 1rem;
    background-color: rgba(255, 255, 255, 0.8);
    color: black;
    border: none;
    cursor: pointer;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    transition: background-color 0.3s ease;
  }

  .newsletter-form button:hover {
    background-color: white;
  }

  @media (max-width: 768px) {
    .utopia-header {
      padding: 1rem;
    }
  }
</style>
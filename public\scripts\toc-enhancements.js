/**
 * TOC enhancements for PVB Astro
 * Using the same approach that worked previously with more subtle styling
 */

console.log('TOC enhancements loaded');

document.addEventListener('DOMContentLoaded', function() {
  console.log('TOC DOM loaded, applying enhancements');
  
  // Add styles directly through JavaScript
  const style = document.createElement('style');
  style.textContent = `
    /* Consistent spacing for TOC items */
    .toc-item {
      margin-bottom: 24px !important;
    }
    
    .toc-item.toc-h3 {
      margin-bottom: 18px !important;
      margin-top: 6px !important;
    }
    
    .toc-item.toc-h4 {
      margin-bottom: 16px !important;
      margin-top: 4px !important;
    }
    
    /* Enhanced active state */
    .toc-content a.active {
      color: rgba(255, 255, 255, 1) !important;
      transform: translateX(6px) !important;
      font-size: 1.04em !important;
      font-weight: 600 !important;
      letter-spacing: 0.02em !important;
    }
    
    /* Enhanced active indicator */
    .toc-content a.active::before {
      content: '' !important;
      position: absolute !important;
      left: -10px !important;
      top: 50% !important;
      transform: translateY(-50%) !important;
      width: 4px !important;
      height: 85% !important;
      background-color: rgba(255, 255, 255, 1) !important;
      border-radius: 2px !important;
    }
  `;
  document.head.appendChild(style);
  
  // Handle TOC toggle to ensure proper styling on reopening
  const tocToggle = document.getElementById('toc-toggle');
  const tocPanel = document.getElementById('toc-panel');
  
  if (tocToggle && tocPanel) {
    // Trigger highlighting initially
    setTimeout(triggerHighlighting, 300);
    
    // Re-apply when TOC is opened
    tocToggle.addEventListener('click', function() {
      setTimeout(function() {
        if (tocPanel.classList.contains('active')) {
          console.log('TOC panel opened, refreshing highlighting');
          triggerHighlighting();
        }
      }, 200);
    });
  }
  
  // Function to trigger the scroll-based highlighting
  function triggerHighlighting() {
    // Force a tiny scroll movement to trigger the built-in highlightCurrentSection function
    window.scrollBy(0, 1);
    window.scrollBy(0, -1);
    console.log('Highlighting triggered');
    
    // Add a second trigger after a delay to ensure it catches
    setTimeout(function() {
      window.scrollBy(0, 1);
      window.scrollBy(0, -1);
    }, 300);
  }
  
  // Also trigger highlighting on window load and resize
  window.addEventListener('load', triggerHighlighting);
  window.addEventListener('resize', triggerHighlighting);
});

import { a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../chunks/astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import { $ as $$Layout } from '../chunks/Layout_rXbp99fE.mjs';
/* empty css                                 */
export { renderers } from '../renderers.mjs';

const $$Index = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "pageTitle": "PVB Home", "isHomePage": true, "accentColor": "#3a2c23", "bgColor": "rgba(250, 246, 242, 0)", "backgroundImageUrl": "/images/whitemarble.png", "bodyDataPage": "home", "data-astro-cid-j7pv25f6": true }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="quote-container" data-astro-cid-j7pv25f6> <div class="quote-indicator-wrapper" id="quote-indicator-wrapper" title="Quote Info" aria-label="Show Quote Information" data-astro-cid-j7pv25f6> <div class="quote-indicator" id="quote-indicator" data-astro-cid-j7pv25f6></div> </div> <p class="quote-text" data-astro-cid-j7pv25f6></p> <p class="quote-attribution" data-astro-cid-j7pv25f6></p> </div> ` })} `;
}, "C:/Users/<USER>/Desktop/pvb-astro/src/pages/index.astro", void 0);

const $$file = "C:/Users/<USER>/Desktop/pvb-astro/src/pages/index.astro";
const $$url = "";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Index,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

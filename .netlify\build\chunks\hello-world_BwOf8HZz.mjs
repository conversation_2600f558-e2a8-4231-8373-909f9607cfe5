const id = "hello-world.md";
						const collection = "blog";
						const slug = "hello-world";
						const body = "\n## Welcome to my blog\n\nThis is the first post on my new blog. I've set up this blog using Astro to create a fast, minimal, and personal blogging experience.\n\n## Why I started this blog\n\nI wanted a place to share my thoughts, projects, and experiences. This blog will be a collection of my work, ideas, and learnings.\n\n## What to expect\n\nI'll be posting about:\n\n- Personal projects\n- Things I'm learning\n- Thoughts and reflections\n- Tutorials and guides\n\nStay tuned for more content coming soon!\n\n## Table of contents\n\n## Code examples\n\nHere's a simple code example:\n\n```javascript\nfunction greet(name) {\n  return `Hello, ${name}!`;\n}\n\nconsole.log(greet(\"World\"));\n```\n\n## Conclusion\n\nThanks for reading my first blog post. I'm excited to share more content with you in the future!\n";
						const data = {author:"PVB",pubDatetime:new Date(1680350400000),title:"Hello World - My First Blog Post",featured:true,draft:false,tags:["introduction","blogging"],description:"This is my first blog post using the new Astro-powered blog system."};
						const _internal = {
							type: 'content',
							filePath: "C:/Users/<USER>/Desktop/pvb-astro/src/content/blog/hello-world.md",
							rawData: undefined,
						};

export { _internal, body, collection, data, id, slug };

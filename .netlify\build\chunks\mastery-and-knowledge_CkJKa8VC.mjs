const id = "mastery-and-knowledge.md";
						const collection = "blog";
						const slug = "mastery-and-knowledge";
						const body = "\n## Mastery and Knowledge\n\nI realized something. You know, from a young age I thought that knowledge are associated with trees. And I understand now that they're talking about mastery. Because mastery isn't about knowledge. It's about knowing what's real and what isn't.\n\nBecause the tree knows that it's a tree. It's not up for debate. The tree knows it has leaves. It's not up for debate. The tree knows that it is not a lake. There's no negotiation there.\n\nSo when somebody says to the tree that you're not a tree, you're just a lake, the tree doesn't react. The tree says, no, I'm a tree. No matter if the person calls it arrogant, an old geezer, or whatever they call it, it will still reply, I am a tree. Nothing less, nothing more.\n\nThat is knowledge. It's not about knowing information. It's about knowing what's real.\n\n## The Wisdom of Certainty\n\nThis perspective shifts how we might think about expertise and learning. It's not about how many facts you can recite or how many books you've read. It's about having such a deep understanding that you recognize truth from falsehood without hesitation.\n\n## Beyond Information\n\nIn our information-saturated age, we often confuse data collection with wisdom. But the tree doesn't need to constantly update its database to know what it is. Its knowledge is embodied, certain, and unshakable.\n\n## Finding Your Truth\n\nPerhaps mastery in any field comes when you reach this level of certainty - not from arrogance, but from deep connection with reality. When you know something so thoroughly that external opinions cannot shake your understanding.\n";
						const data = {author:"PVB",pubDatetime:new Date(1702123200000),title:"Mastery and Knowledge - The Tree's Wisdom",featured:true,draft:false,tags:["philosophy","wisdom","mastery"],description:"Reflections on how true knowledge is about recognizing what's real, not just accumulating information."};
						const _internal = {
							type: 'content',
							filePath: "C:/Users/<USER>/Desktop/pvb-astro/src/content/blog/mastery-and-knowledge.md",
							rawData: undefined,
						};

export { _internal, body, collection, data, id, slug };

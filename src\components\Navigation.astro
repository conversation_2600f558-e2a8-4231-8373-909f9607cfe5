---
// Navigation component that displays the navigation dots
export interface Props {
    isHomePage?: boolean;
}

const { isHomePage = false } = Astro.props;

// Logic for top-left icon/title based on isHomePage
const topLeftTitle = isHomePage ? "About" : "Back";
const topLeftAriaLabel = isHomePage ? "About Page" : "Go Back";
---

<!-- Top-Left Nav Circle -->
<div class="nav-circle top-left" title={topLeftTitle} aria-label={topLeftAriaLabel}>
    <span class="nav-icon"></span> <!-- CSS handles the '?' or '←' -->
</div>

{/* Top-Right Nav Circle: only on home page */}
{isHomePage && (
  <div class="nav-circle top-right" title="Links" aria-label="Links Page">
      <span class="nav-icon"></span>
  </div>
)}

<!-- Note: Bottom-center nav circle is now handled by Layout.astro -->

<script is:inline>
  // Initialize navigation functionality
  document.addEventListener('DOMContentLoaded', function() {
    const topLeftNav = document.querySelector('.nav-circle.top-left');
    const isHomePage = document.body.getAttribute('data-page') === 'home';

    if (topLeftNav) {
      topLeftNav.addEventListener('click', function() {
        if (isHomePage) {
          // On home page, go to about page
          window.location.href = '/about';
        } else {
          // On other pages, go back
          window.history.back();
        }
      });
    }
  });
</script>

<style>
    /* Top-Left Nav Circle Styles */
    .nav-circle.top-left,
    .nav-circle.top-right {
        position: fixed;
        width: var(--circle-size);
        height: var(--circle-size);
        border-radius: 50%;
        background-color: var(--color-accent);
        cursor: pointer;
        z-index: 100;
        top: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        border: var(--circle-border-width) solid var(--color-accent);
        transition: transform var(--transition-duration) var(--easing-standard),
                  background-color var(--transition-duration) var(--easing-standard),
                  border-color var(--transition-duration) var(--easing-standard);
    }

    .nav-circle.top-left {
        left: 30px;
    }

    .nav-circle.top-right {
        right: 30px;
    }

    /* Hover effect - grows and becomes hollow */
    .nav-circle.top-left:hover,
    .nav-circle.top-right:hover {
        transform: scale(var(--circle-expand-scale));
        background-color: transparent;
    }

    /* Top Left Icon (?) / (<-) */
    .nav-circle.top-left .nav-icon::before {
        content: "?";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) scale(0.8);
        font-size: 10px;
        opacity: 0;
        transition: opacity calc(var(--transition-duration)*0.8) calc(var(--transition-duration)*0.2) var(--easing-standard),
                  transform calc(var(--transition-duration)*0.8) calc(var(--transition-duration)*0.2) var(--easing-standard);
        color: var(--color-accent);
    }

    body:not([data-page="home"]) .nav-circle.top-left .nav-icon::before {
        content: "←";
        font-size: 12px;
    }

    .nav-circle.top-left:hover .nav-icon::before {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }

    /* Top Right Icon (Link) */
    .nav-circle.top-right .nav-icon::before,
    .nav-circle.top-right .nav-icon::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 6px;
        height: 6px;
        border: 1.2px solid var(--color-accent);
        border-radius: 1.5px;
        opacity: 0;
        transition: opacity calc(var(--transition-duration)*0.8) calc(var(--transition-duration)*0.2) var(--easing-standard),
                  transform calc(var(--transition-duration)*0.8) calc(var(--transition-duration)*0.2) var(--easing-standard);
        transform-origin: center center;
    }

    .nav-circle.top-right .nav-icon::before {
        transform: translate(calc(-50% - 1.8px), -50%) rotate(45deg) scale(0.8);
    }

    .nav-circle.top-right .nav-icon::after {
        transform: translate(calc(-50% + 1.8px), -50%) rotate(45deg) scale(0.8);
    }

    .nav-circle.top-right:hover .nav-icon::before {
        opacity: 1;
        transform: translate(calc(-50% - 1.8px), -50%) rotate(45deg) scale(1);
    }

    .nav-circle.top-right:hover .nav-icon::after {
        opacity: 1;
        transform: translate(calc(-50% + 1.8px), -50%) rotate(45deg) scale(1);
    }

    /* Hide navigation when menu is active */
    body.menu-active .nav-circle.top-left,
    body.menu-active .nav-circle.top-right {
        opacity: 0;
        visibility: hidden;
        pointer-events: none;
        transition: opacity var(--transition-duration) var(--easing-standard),
                  visibility 0s var(--transition-duration);
    }

    /* Restore visibility with delay when menu closes */
    body:not(.menu-active) .nav-circle.top-left,
    body:not(.menu-active) .nav-circle.top-right {
        transition-delay: calc(var(--transition-duration) * 0.2);
    }

    /* Responsive styles */
    @media (max-width: 768px) {
        .nav-circle.top-left,
        .nav-circle.top-right {
            top: 25px;
            width: var(--circle-size);
            height: var(--circle-size);
        }

        .nav-circle.top-left {
            left: 25px;
        }

        .nav-circle.top-right {
            right: 25px;
        }
    }
</style>

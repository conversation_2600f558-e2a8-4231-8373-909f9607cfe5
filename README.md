# PVB Astro Website

A clean, minimalist personal website built with Astro that features a unique 3-dot navigation system and elegant transitions.

## Features

- Minimalist design with focus on content
- Unique 3-dot navigation system
- Menu overlay for site navigation
- Quote display system with info card
- Smooth transitions and animations
- Responsive design for all device sizes

## Project Structure

```
/
├── public/              # Static assets
│   ├── fonts/           # Custom fonts
│   └── images/          # Background images and other static images
├── src/
│   ├── components/      # Reusable UI components
│   │   ├── Navigation.astro    # 3-dot navigation system
│   │   └── MainMenu.astro      # Menu overlay
│   ├── layouts/
│   │   └── Layout.astro        # Main layout template
│   ├── pages/           # Page components
│   │   ├── index.astro         # Home page
│   │   └── about.astro         # About page
│   ├── scripts/
│   │   └── main.js             # Main JavaScript functionality
│   └── styles/
│       └── global.css          # Global styles
├── astro.config.mjs     # Astro configuration
├── package.json         # Project dependencies
└── tsconfig.json        # TypeScript configuration
```

## Getting Started

1. **Install dependencies:**

```bash
npm install
```

2. **Start the development server:**

```bash
npm run dev
```

3. **Build for production:**

```bash
npm run build
```

## Adding New Pages

To add a new page:

1. Create a new `.astro` file in the `src/pages` directory
2. Use the main Layout component with appropriate props
3. Add your page-specific content

Example:

```astro
---
import Layout from '../layouts/Layout.astro';
---

<Layout
    pageTitle="Your Page Title"
    isHomePage={false}
    accentColor="#your-color"
    bgColor="rgba(your-bg-color)"
    backgroundImageUrl="/images/your-background.png"
    bodyDataPage="your-page-name"
>
    <div class="your-content-container">
        <!-- Your content here -->
    </div>
</Layout>
```

## Customization

- Edit global styles in `src/styles/global.css`
- Modify component behavior in the respective component files
- Update the menu items in `src/components/MainMenu.astro`

## License

This project is licensed under the MIT License. See the LICENSE file for details.

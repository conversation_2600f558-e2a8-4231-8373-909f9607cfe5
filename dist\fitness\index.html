<!DOCTYPE html><html lang="en" data-astro-cid-sckkx6r4> <head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>Physical Practice | PVB</title><link rel="stylesheet" href="/_astro/fitness.tvH0hC5j.css">
<link rel="stylesheet" href="/_astro/_slug_.CSlrdnFy.css"><script type="module">document.addEventListener("DOMContentLoaded",function(){window.addEventListener("scroll",function(){const e=window.scrollY;document.body.style.backgroundPosition=`center ${e*.02}px`});const L=document.querySelectorAll('[data-animate="fade-in"]'),h=new IntersectionObserver(e=>{e.forEach(t=>{t.isIntersecting&&(t.target.classList.add("visible"),h.unobserve(t.target))})},{root:null,rootMargin:"0px",threshold:.15});L.forEach(e=>{h.observe(e)});const a=document.querySelectorAll(".transformation-img"),c=document.querySelectorAll(".year-marker"),m=document.getElementById("prev-year"),v=document.getElementById("next-year"),y=document.getElementById("current-year");if(!a.length||!c.length)return;let n=0;const s=a.length;function o(e){e<0&&(e=0),e>=s&&(e=s-1),n=e,a.forEach((t,f)=>{f===n?(t.classList.add("active"),t.style.opacity=1):(t.classList.remove("active"),t.style.opacity=0)}),c.forEach((t,f)=>{t.classList.toggle("active",f===n)}),y&&(y.textContent=a[n].dataset.year)}c.forEach((e,t)=>{e.addEventListener("click",()=>{o(t)})}),m&&m.addEventListener("click",()=>{o(n-1)}),v&&v.addEventListener("click",()=>{o(n+1)});let i=0,l=0;const d=document.querySelector(".current-image");d&&(d.addEventListener("touchstart",e=>{i=e.changedTouches[0].screenX}),d.addEventListener("touchend",e=>{l=e.changedTouches[0].screenX,p()}));function p(){l<i-50?o(n+1):l>i+50&&o(n-1)}let g;function u(){g=setInterval(()=>{let e=(n+1)%s;o(e)},5e3)}function E(){clearInterval(g)}u();const r=document.querySelector(".transformation-gallery");r&&(r.addEventListener("mouseenter",E),r.addEventListener("touchstart",E),r.addEventListener("mouseleave",u),r.addEventListener("touchend",u))});
</script></head> <body data-page="fitness" style="--color-accent: #1a1a1a; --color-bg: rgba(235, 230, 225, 0.85); background-image: url(/images/emperadormarble.png); background-color: rgba(235, 230, 225, 0.85);" data-astro-cid-sckkx6r4> <div class="page-transition" id="page-transition" data-astro-cid-sckkx6r4></div> <!-- Common logo for all pages --> <a href="/" class="logo" data-astro-cid-sckkx6r4>pvb</a> <div class="content-wrapper" data-astro-cid-sckkx6r4> <!-- Slot for page-specific content -->   <link rel="preconnect" href="https://fonts.googleapis.com"> <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin> <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@600;700&family=Crimson+Pro:wght@400;500&display=swap" rel="stylesheet"> <div class="fitness-header" data-astro-cid-6p5wzov7> <h1 class="fitness-title" data-astro-cid-6p5wzov7>PHYSICAL PRACTICE</h1> </div> <div class="content-container" data-astro-cid-6p5wzov7> <!-- Transformation Showcase - Immediate Visual Impact --> <section class="transformation-section" data-animate="fade-in" data-astro-cid-6p5wzov7> <div class="section-header" data-astro-cid-6p5wzov7> <div class="section-line" data-astro-cid-6p5wzov7></div> <h2 class="section-title" data-astro-cid-6p5wzov7>TRANSFORMATION</h2> <div class="section-line" data-astro-cid-6p5wzov7></div> </div> <div class="transformation-gallery" data-astro-cid-6p5wzov7> <div class="current-image-container" data-astro-cid-6p5wzov7> <div class="image-controls" data-astro-cid-6p5wzov7> <button class="control-button prev-button" id="prev-year" data-astro-cid-6p5wzov7> <span class="control-arrow" data-astro-cid-6p5wzov7>←</span> </button> <div class="year-display" id="current-year" data-astro-cid-6p5wzov7>2017</div> <button class="control-button next-button" id="next-year" data-astro-cid-6p5wzov7> <span class="control-arrow" data-astro-cid-6p5wzov7>→</span> </button> </div> <div class="current-image" data-astro-cid-6p5wzov7> <!-- Images will transition here via JS --> <img src="/images/fit2017.jpg" alt="Fitness transformation 2017" class="transformation-img active" data-year="2017" data-index="0" data-astro-cid-6p5wzov7><img src="/images/fit2018.jpg" alt="Fitness transformation 2018" class="transformation-img " data-year="2018" data-index="1" data-astro-cid-6p5wzov7><img src="/images/fit2019.jpg" alt="Fitness transformation 2019" class="transformation-img " data-year="2019" data-index="2" data-astro-cid-6p5wzov7><img src="/images/fit2020.jpeg" alt="Fitness transformation 2020" class="transformation-img " data-year="2020" data-index="3" data-astro-cid-6p5wzov7><img src="/images/fit2021.jpeg" alt="Fitness transformation 2021" class="transformation-img " data-year="2021" data-index="4" data-astro-cid-6p5wzov7><img src="/images/fit2022.2.jpeg" alt="Fitness transformation 2022" class="transformation-img " data-year="2022" data-index="5" data-astro-cid-6p5wzov7><img src="/images/fit2023.1.jpeg" alt="Fitness transformation 2023" class="transformation-img " data-year="2023" data-index="6" data-astro-cid-6p5wzov7><img src="/images/fit2024.1.jpeg" alt="Fitness transformation 2024" class="transformation-img " data-year="2024" data-index="7" data-astro-cid-6p5wzov7><img src="/images/fit2025.png" alt="Fitness transformation 2025" class="transformation-img " data-year="2025" data-index="8" data-astro-cid-6p5wzov7> </div> </div> <div class="year-timeline" data-astro-cid-6p5wzov7> <button class="year-marker active" data-index="0" data-year="2017" data-astro-cid-6p5wzov7> <span class="year-dot" data-astro-cid-6p5wzov7></span> <span class="year-label" data-astro-cid-6p5wzov7>2017</span> </button><button class="year-marker " data-index="1" data-year="2018" data-astro-cid-6p5wzov7> <span class="year-dot" data-astro-cid-6p5wzov7></span> <span class="year-label" data-astro-cid-6p5wzov7>2018</span> </button><button class="year-marker " data-index="2" data-year="2019" data-astro-cid-6p5wzov7> <span class="year-dot" data-astro-cid-6p5wzov7></span> <span class="year-label" data-astro-cid-6p5wzov7>2019</span> </button><button class="year-marker " data-index="3" data-year="2020" data-astro-cid-6p5wzov7> <span class="year-dot" data-astro-cid-6p5wzov7></span> <span class="year-label" data-astro-cid-6p5wzov7>2020</span> </button><button class="year-marker " data-index="4" data-year="2021" data-astro-cid-6p5wzov7> <span class="year-dot" data-astro-cid-6p5wzov7></span> <span class="year-label" data-astro-cid-6p5wzov7>2021</span> </button><button class="year-marker " data-index="5" data-year="2022" data-astro-cid-6p5wzov7> <span class="year-dot" data-astro-cid-6p5wzov7></span> <span class="year-label" data-astro-cid-6p5wzov7>2022</span> </button><button class="year-marker " data-index="6" data-year="2023" data-astro-cid-6p5wzov7> <span class="year-dot" data-astro-cid-6p5wzov7></span> <span class="year-label" data-astro-cid-6p5wzov7>2023</span> </button><button class="year-marker " data-index="7" data-year="2024" data-astro-cid-6p5wzov7> <span class="year-dot" data-astro-cid-6p5wzov7></span> <span class="year-label" data-astro-cid-6p5wzov7>2024</span> </button><button class="year-marker " data-index="8" data-year="2025" data-astro-cid-6p5wzov7> <span class="year-dot" data-astro-cid-6p5wzov7></span> <span class="year-label" data-astro-cid-6p5wzov7>2025</span> </button> </div> </div> </section> <hr class="section-divider" data-astro-cid-6p5wzov7> <!-- Stats Component - Key Metrics --> <section class="stats-section" data-animate="fade-in" data-astro-cid-6p5wzov7> <div class="section-header" data-astro-cid-6p5wzov7> <div class="section-line" data-astro-cid-6p5wzov7></div> <h2 class="section-title" data-astro-cid-6p5wzov7>PERFORMANCE</h2> <div class="section-line" data-astro-cid-6p5wzov7></div> </div> <div class="stats-container" data-astro-cid-6p5wzov7> <div class="stats-column records-column" data-astro-cid-6p5wzov7> <h3 class="stats-heading" data-astro-cid-6p5wzov7>PERSONAL RECORDS</h3> <div class="records-list" data-astro-cid-6p5wzov7> <div class="record-item" data-astro-cid-6p5wzov7> <div class="record-exercise" data-astro-cid-6p5wzov7>Bench Press</div> <div class="record-weight" data-astro-cid-6p5wzov7>120kg</div> <div class="record-note" data-astro-cid-6p5wzov7>3-rep max</div> </div><div class="record-item" data-astro-cid-6p5wzov7> <div class="record-exercise" data-astro-cid-6p5wzov7>Bulgarian Split Squat</div> <div class="record-weight" data-astro-cid-6p5wzov7>60kg</div> <div class="record-note" data-astro-cid-6p5wzov7>each leg</div> </div><div class="record-item" data-astro-cid-6p5wzov7> <div class="record-exercise" data-astro-cid-6p5wzov7>Barbell Row</div> <div class="record-weight" data-astro-cid-6p5wzov7>115kg</div> <div class="record-note" data-astro-cid-6p5wzov7>5-rep max</div> </div> </div> </div> <div class="stats-column metrics-column" data-astro-cid-6p5wzov7> <h3 class="stats-heading" data-astro-cid-6p5wzov7>TRAINING VOLUME</h3> <div class="metrics-grid" data-astro-cid-6p5wzov7> <div class="metric-item" data-astro-cid-6p5wzov7> <div class="metric-value" data-astro-cid-6p5wzov7>7+</div> <div class="metric-label" data-astro-cid-6p5wzov7>Years Training</div> </div><div class="metric-item" data-astro-cid-6p5wzov7> <div class="metric-value" data-astro-cid-6p5wzov7>4,500+</div> <div class="metric-label" data-astro-cid-6p5wzov7>Total Sessions</div> </div><div class="metric-item" data-astro-cid-6p5wzov7> <div class="metric-value" data-astro-cid-6p5wzov7>4.8</div> <div class="metric-label" data-astro-cid-6p5wzov7>Weekly Frequency</div> </div><div class="metric-item" data-astro-cid-6p5wzov7> <div class="metric-value" data-astro-cid-6p5wzov7>9,000+</div> <div class="metric-label" data-astro-cid-6p5wzov7>Total Hours</div> </div> </div> </div> </div> </section> <hr class="section-divider" data-astro-cid-6p5wzov7> <!-- Philosophy Section --> <section class="philosophy-section" data-animate="fade-in" data-astro-cid-6p5wzov7> <div class="section-header" data-astro-cid-6p5wzov7> <div class="section-line" data-astro-cid-6p5wzov7></div> <h2 class="section-title" data-astro-cid-6p5wzov7>PHILOSOPHY</h2> <div class="section-line" data-astro-cid-6p5wzov7></div> </div> <p class="intro-text" data-astro-cid-6p5wzov7>
Since 2017, I've approached physical training as an empirical practice—a deliberate application 
        of progressive overload principles documented through extensive data collection. The transformation 
        above represents not sporadic effort but systematic application of principles.
</p> <div class="philosophy-content" data-astro-cid-6p5wzov7> <div class="philosophy-item" data-astro-cid-6p5wzov7> <h3 class="philosophy-title" data-astro-cid-6p5wzov7>Consistent Progression</h3> <p class="philosophy-text" data-astro-cid-6p5wzov7>Physical development occurs not through sporadic intensity but through sustained, incremental progress. My approach centers on data-driven progression: each workout builds upon the previous, with meticulously tracked performance guiding adjustments to stimulate continued adaptation.</p> </div><div class="philosophy-item" data-astro-cid-6p5wzov7> <h3 class="philosophy-title" data-astro-cid-6p5wzov7>Empirical Methodology</h3> <p class="philosophy-text" data-astro-cid-6p5wzov7>Every repetition, every set, every session exists as a data point in a seven-year experiment. This comprehensive record reveals patterns in recovery, adaptation, and performance that inform my training protocol—transforming fitness from subjective effort into an objective practice.</p> </div><div class="philosophy-item" data-astro-cid-6p5wzov7> <h3 class="philosophy-title" data-astro-cid-6p5wzov7>Recovery Architecture</h3> <p class="philosophy-text" data-astro-cid-6p5wzov7>Training creates the stimulus; recovery enables the transformation. I&#39;ve systematically optimized sleep quality, nutritional timing, and stress management to create the physiological conditions where adaptation can occur without accumulating systemic fatigue or reaching plateaus.</p> </div> </div> </section> <hr class="section-divider" data-astro-cid-6p5wzov7> <!-- Training System Section --> <section class="routines-section" data-animate="fade-in" data-astro-cid-6p5wzov7> <div class="section-header" data-astro-cid-6p5wzov7> <div class="section-line" data-astro-cid-6p5wzov7></div> <h2 class="section-title" data-astro-cid-6p5wzov7>METHODOLOGY</h2> <div class="section-line" data-astro-cid-6p5wzov7></div> </div> <div class="routines-content" data-astro-cid-6p5wzov7> <p class="routines-intro" data-astro-cid-6p5wzov7>
My current training protocol follows a four-day rotation targeting specific movement patterns 
          and muscle groups. Each protocol is meticulously documented and progressively adapted based 
          on performance data.
</p> <div class="routines-grid" data-astro-cid-6p5wzov7> <a href="https://hevy.com/routine/oD9TKyY25F7" class="routine-link" target="_blank" rel="noopener noreferrer" data-astro-cid-6p5wzov7> <div class="routine-name" data-astro-cid-6p5wzov7>Chest &amp; Shoulders</div> <div class="routine-arrow" data-astro-cid-6p5wzov7>→</div> </a><a href="https://hevy.com/routine/PSvJCDxdTAS" class="routine-link" target="_blank" rel="noopener noreferrer" data-astro-cid-6p5wzov7> <div class="routine-name" data-astro-cid-6p5wzov7>Back</div> <div class="routine-arrow" data-astro-cid-6p5wzov7>→</div> </a><a href="https://hevy.com/routine/FBu1xtqJsaW" class="routine-link" target="_blank" rel="noopener noreferrer" data-astro-cid-6p5wzov7> <div class="routine-name" data-astro-cid-6p5wzov7>Arms</div> <div class="routine-arrow" data-astro-cid-6p5wzov7>→</div> </a><a href="https://hevy.com/routine/X9WG47EEhWT" class="routine-link" target="_blank" rel="noopener noreferrer" data-astro-cid-6p5wzov7> <div class="routine-name" data-astro-cid-6p5wzov7>Legs</div> <div class="routine-arrow" data-astro-cid-6p5wzov7>→</div> </a> </div> <div class="hevy-link-container" data-astro-cid-6p5wzov7> <a href="https://hevy.com/user/approxfit" class="hevy-link" target="_blank" rel="noopener noreferrer" data-astro-cid-6p5wzov7> <span class="hevy-link-text" data-astro-cid-6p5wzov7>VIEW COMPLETE TRAINING ARCHIVE</span> <span class="hevy-link-arrow" data-astro-cid-6p5wzov7>→</span> </a> </div> </div> </section> </div>  </div> <div class="quote-card" id="quote-card" data-astro-cid-sckkx6r4> <h3 class="quote-card-title" data-astro-cid-sckkx6r4></h3> <p class="quote-card-subtitle" data-astro-cid-sckkx6r4></p> </div> <!-- Include navigation on all pages --> <!-- Top-Left Nav Circle --><div class="nav-circle top-left" title="Back" aria-label="Go Back" data-astro-cid-pux6a34n> <span class="nav-icon" data-astro-cid-pux6a34n></span> <!-- CSS handles the '?' or '←' --> </div>   <!-- Note: Bottom-center nav circle is now handled by Layout.astro --> <script>
  // Initialize navigation functionality
  document.addEventListener('DOMContentLoaded', function() {
    const topLeftNav = document.querySelector('.nav-circle.top-left');
    const isHomePage = document.body.getAttribute('data-page') === 'home';

    if (topLeftNav) {
      topLeftNav.addEventListener('click', function() {
        if (isHomePage) {
          // On home page, go to about page
          window.location.href = '/about';
        } else {
          // On other pages, go back
          window.history.back();
        }
      });
    }
  });
</script>  <!-- Always include the bottom navigation button --> <div class="nav-circle bottom-center" id="menu-toggle" title="Menu" aria-label="Toggle Menu" data-astro-cid-sckkx6r4> <span class="nav-icon" data-astro-cid-sckkx6r4></span> </div> <!-- Include main menu on all pages --> <div class="main-menu" id="main-menu"> <a href="/" class="logo menu-logo">pvb</a> <div class="menu-wrapper"> <a href="/blog" data-nav>blog</a> <a href="/work" data-nav>work</a> <a href="/about" data-nav>about</a> <a href="/fitness" data-nav>fitness</a> <a href="/cv" class="side-menu-item left" data-nav>cv</a> <a href="/links" class="side-menu-item right" data-nav>links</a> <a href="/more" class="see-more" data-nav>
more
<span class="arrow">↓</span> </a> </div> </div> <script>
        // Function to ensure proper scrolling behavior
        document.addEventListener('DOMContentLoaded', function() {
            // Remove the transition overlay once page loads
            const pageTransition = document.getElementById('page-transition');
            setTimeout(() => {
                if (pageTransition) {
                    pageTransition.classList.remove('active');
                }
            }, 400);

            // Make sure scrollbar positioning is always at the edge
            const bodyEl = document.body;
            const pageType = bodyEl.getAttribute('data-page');

            // Add specific adjustments based on page type
            if (pageType === 'blog' || pageType === 'blog-post' || pageType === 'about' || pageType === 'work' || pageType === 'work-post') {
                bodyEl.style.overflowY = 'auto';
                bodyEl.style.height = 'auto';
                document.documentElement.style.overflowY = 'auto';
                document.documentElement.style.height = 'auto';

                // Special handling for full-page content on blog and blog post pages
                if (pageType === 'blog' || pageType === 'blog-post' || pageType === 'work' || pageType === 'work-post') {
                    const contentWrapper = document.querySelector('.content-wrapper');
                    if (contentWrapper) {
                        contentWrapper.style.position = 'relative';
                        contentWrapper.style.height = 'auto';
                        contentWrapper.style.minHeight = '100vh';
                    }
                }

                // Restore scroll position if it was saved (for navigation back)
                if (sessionStorage.getItem(`scrollPos-${pageType}`)) {
                    const savedScrollPos = parseInt(sessionStorage.getItem(`scrollPos-${pageType}`));
                    setTimeout(() => {
                        window.scrollTo(0, savedScrollPos);
                    }, 100);
                }

                // Save scroll position when navigating away
                window.addEventListener('beforeunload', function() {
                    sessionStorage.setItem(`scrollPos-${pageType}`, window.scrollY.toString());
                });

                // Change bottom button opacity on scroll
                const bottomButton = document.querySelector('.nav-circle.bottom-center');
                if (bottomButton) {
                    window.addEventListener('scroll', function() {
                        if (window.scrollY > 100) {
                            bottomButton.style.opacity = "0.7";
                        } else {
                            bottomButton.style.opacity = "1";
                        }
                    });
                }
            }
        });

        // Simple page transition
        document.addEventListener('DOMContentLoaded', function() {
            const pageTransition = document.getElementById('page-transition');
            const links = document.querySelectorAll('a:not([target="_blank"]):not([href^="#"]):not([href^="javascript"])');

            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    if (this.hostname === window.location.hostname) {
                        e.preventDefault();
                        const href = this.getAttribute('href');

                        // Apply the transition
                        if (pageTransition) {
                            pageTransition.classList.add('active');
                        }

                        // Navigate after transition
                        setTimeout(() => {
                            window.location.href = href;
                        }, 400); // Match the CSS transition duration
                    }
                });
            });
        });
    </script> <!-- Fix: Use either is:inline or src, not both --> <script src="/scripts/main.js" defer></script> </body> </html>   
import { g as getCollection } from '../../chunks/_astro_content_CcaQj6Wl.mjs';
export { renderers } from '../../renderers.mjs';

// Fallback quotes in case the collection fails
const fallbackQuotes = [
  {
    text: "Many mistake stability for safety, but only the dead remain still.",
    author: "Pruthvi Bhat",
    linkedPage: "/blog/stability-vs-safety",
    cardTitle: "Core Philosophy",
    cardSubtitle: "Exploring the need for change"
  },
  {
    text: "The purpose of knowledge is action, not more knowledge.",
    author: "Pruthvi Bhat",
    linkedPage: "/blog/knowledge-and-action",
    cardTitle: "Applied Learning",
    cardSubtitle: "Insights into meaningful action"
  },
  {
    text: "Silence is not empty, it's full of answers.",
    author: "Unknown",
    linkedPage: "/blog/power-of-silence",
    cardTitle: "Mindfulness",
    cardSubtitle: "Finding clarity in quiet"
  }
];

const prerender = false;

async function GET({ request }) {
  try {
    // Get all quotes from the collection
    const allQuotes = await getCollection('quotes');

    // If we have quotes, select a random one
    if (allQuotes && allQuotes.length > 0) {
      const randomIndex = Math.floor(Math.random() * allQuotes.length);
      const randomQuote = allQuotes[randomIndex];

      return new Response(
        JSON.stringify({
          text: randomQuote.data.text,
          author: randomQuote.data.author,
          linkedPage: randomQuote.data.linkedPage || null,
          cardTitle: randomQuote.data.cardTitle,
          cardSubtitle: randomQuote.data.cardSubtitle
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
    }

    // If no quotes found, use fallback
    console.warn('No quotes found in collection, using fallback');
    const randomIndex = Math.floor(Math.random() * fallbackQuotes.length);
    return new Response(
      JSON.stringify(fallbackQuotes[randomIndex]),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  } catch (error) {
    console.error('Error fetching quotes:', error);

    // Return a fallback quote
    const randomIndex = Math.floor(Math.random() * fallbackQuotes.length);
    return new Response(
      JSON.stringify(fallbackQuotes[randomIndex]),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  GET,
  prerender
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

// Number of posts per page
const POSTS_PER_PAGE = 5;

export async function getStaticPaths() {
  // Local posts per page constant
  const LOCAL_POSTS_PER_PAGE = 5;
  // Get only blog posts
  const blogPosts = await getPostsByType('blog');
  const totalPages = Math.ceil(blogPosts.length / LOCAL_POSTS_PER_PAGE);
  
  const paths: GetStaticPathsResult = [];
  
  // Generate pages for each pagination number
  for (let i = 1; i <= totalPages; i++) {
    const paginatedData = paginatePosts(blogPosts, i, LOCAL_POSTS_PER_PAGE);
    
    paths.push({
      params: { page: i.toString() },
      props: { 
        paginatedData,
        currentPage: i,
        totalPages,
      }
    });
  }
  
  return paths;
}

const { paginatedData, currentPage, totalPages } = Astro.props;
const { posts, pagination } = paginatedData;

// Filter internal tags from archive pages
const internalTags = ['blog','work','archive'];

---

<Layout
  pageTitle={`Blog - Page ${currentPage} | PVB`}
  isHomePage={false}
  accentColor="#f0f0f0"
  bgColor="rgba(10, 10, 10, 0.94)"
  backgroundImageUrl="/images/blackgranite.png"
  bodyDataPage="blog"
>
  <!-- Blog title - Static (not fixed) -->
  <div class="blog-header">
    <div class="blog-title">blog</div>
  </div>

  <!-- Full Page Content Container -->
  <div class="page-container">
    <!-- Left Sidebar -->
    <div class="blog-sidebar">
      <!-- Search Bar -->
      <div class="search-container sidebar-section">
        <a href="/search" class="search-link">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="11" cy="11" r="8"></circle>
            <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
          </svg>
          <span>search</span>
        </a>
      </div>

      <!-- Archive Button -->
      <div class="archive-container sidebar-section">
        <a href="/blog/timeline" class="archive-link">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M3 3v18h18"></path>
            <path d="M7 17l4-4 4 4 4-4"></path>
            <path d="M7 11l4-4 4 4 4-4"></path>
          </svg>
          <span>archive</span>
        </a>
      </div>

      <!-- Subscribe -->
      <div class="subscribe-container sidebar-section">
        <a href="#" class="subscribe-link">subscribe by email</a>
      </div>
    </div>

    <!-- Main Content -->
    <div class="blog-content">
      <!-- Posts List -->
      <div class="posts-list">
        {posts.map((post, index) => (
          <div class={`post-item ${index === 0 ? 'first-post' : ''}`}>
            <h2 class="post-title">
              <a href={`/blog/${post.slug}`}>{post.title}</a>
            </h2>
            <div class="post-meta">
              <span class="post-date">
                {new Date(post.published_at).toLocaleDateString('en-US', {day: 'numeric', month: 'long', year: 'numeric'})}
              </span>
              {post.reading_time && (
                <span class="reading-time">
                  • {post.reading_time} min read
                </span>
              )}
            </div>
            <p class="post-description">{post.description}</p>
            <div class="post-tags">
              {post.tags?.map(tag => {
                const name = typeof tag === 'string' ? tag : (tag.slug || tag.name || '');
                return name;
              })
              .filter(name => name && !internalTags.includes(name.toLowerCase()))
              .map(name => (
                <a href={`/blog/tag/${name}`} class="post-tag">#{name}</a>
              ))}
            </div>
            <a href={`/blog/${post.slug}`} class="read-more">
              Read Post <span class="read-more-arrow">→</span>
            </a>
          </div>
        ))}
      </div>

      <!-- Ghost-style Pagination -->
      <div class="pagination">
        {pagination.hasPrevPage ? (
          <a href={`/blog/page/${pagination.prevPage}`} class="pagination-link prev-page">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M19 12H5"></path>
              <path d="M12 19l-7-7 7-7"></path>
            </svg>
            <span>Newer Posts</span>
          </a>
        ) : (
          <span class="pagination-link disabled">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M19 12H5"></path>
              <path d="M12 19l-7-7 7-7"></path>
            </svg>
            <span>Newer Posts</span>
          </span>
        )}
        
        <div class="pagination-info">
          Page {pagination.currentPage} of {pagination.totalPages}
        </div>
        
        {pagination.hasNextPage ? (
          <a href={`/blog/page/${pagination.nextPage}`} class="pagination-link next-page">
            <span>Older Posts</span>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M5 12h14"></path>
              <path d="M12 5l7 7-7 7"></path>
            </svg>
          </a>
        ) : (
          <span class="pagination-link disabled">
            <span>Older Posts</span>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M5 12h14"></path>
              <path d="M12 5l7 7-7 7"></path>
            </svg>
          </span>
        )}
      </div>
    </div>
  </div>
</Layout>

<style>
  /* Global Scrollbar Styling */
  :global(html) {
    scrollbar-width: thin;
    scrollbar-color: rgba(100, 100, 100, 0.4) transparent;
  }

  :global(::-webkit-scrollbar) {
    width: 8px;
    height: 8px;
  }

  :global(::-webkit-scrollbar-track) {
    background: transparent;
  }

  :global(::-webkit-scrollbar-thumb) {
    background-color: rgba(100, 100, 100, 0.4);
    border-radius: 4px;
  }

  :global(::-webkit-scrollbar-thumb:hover) {
    background-color: rgba(120, 120, 120, 0.6);
  }

  /* Enable scrolling on blog page */
  :global(body[data-page="blog"]) {
    overflow-y: auto;
  }

  /* Blog Header - Static (not fixed) positioned below the logo */
  .blog-header {
    width: 100%;
    display: flex;
    justify-content: center;
    margin: 55px 0 30px;
  }

  .blog-title {
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 1.5rem;
    color: rgba(240, 240, 240, 0.9);
    letter-spacing: -0.01em;
    position: relative;
  }

  /* Add subtle underline to blog title */
  .blog-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 1px;
    background-color: rgba(240, 240, 240, 0.4);
  }

  /* Full Page Container */
  .page-container {
    display: flex;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 30px;
    gap: 40px;
  }

  /* Left Sidebar - Better spacing and styling */
  .blog-sidebar {
    width: 220px;
    padding-top: 30px;
    padding-right: 20px;
    position: sticky;
    top: 0;
    height: 100vh;
    align-self: flex-start;
  }

  /* Add more space between sidebar sections */
  .sidebar-section {
    margin-bottom: 40px;
  }

  /* Search Styles */
  .search-link {
    display: flex;
    align-items: center;
    color: rgba(240, 240, 240, 0.9);
    text-decoration: none;
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    padding: 0.4rem 0.9rem;
    background-color: rgba(34, 34, 34, 0.6);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
  }

  .search-link:hover {
    color: rgba(240, 240, 240, 1);
    background-color: rgba(34, 34, 34, 0.95);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .search-link svg {
    margin-right: 10px;
  }

  /* Archive Button */
  .archive-container {
    display: flex;
    align-items: center;
  }

  .archive-link {
    font-size: 0.9rem;
    color: rgba(240, 240, 240, 0.9);
    text-decoration: none;
    font-family: 'Georgia Custom', Georgia, serif;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 0.4rem 0.9rem;
    background-color: rgba(34, 34, 34, 0.6);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
  }

  .archive-link:hover {
    color: rgba(240, 240, 240, 1);
    background-color: rgba(34, 34, 34, 0.95);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  /* Subscribe Link */
  .subscribe-link {
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 0.9rem;
    color: rgba(240, 240, 240, 0.9);
    text-decoration: none;
    background-color: rgba(34, 34, 34, 0.6);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    padding: 0.4rem 0.9rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
    transition: all 0.3s ease;
    display: inline-block;
  }

  .subscribe-link:hover {
    color: rgba(240, 240, 240, 1);
    background-color: rgba(34, 34, 34, 0.95);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  /* Main Content */
  .blog-content {
    flex: 1;
    padding-top: 10px;
    padding-bottom: 60px;
    padding-right: 20px;
    padding-left: 20px;
    max-width: 900px;
  }

  /* Posts List */
  .posts-list {
    display: flex;
    flex-direction: column;
    gap: 50px;
  }

  .post-item {
    margin-bottom: 20px;
    padding-bottom: 30px;
    border-bottom: 1px solid rgba(200, 200, 200, 0.1);
  }

  .post-item:last-child {
    border-bottom: none;
    margin-bottom: 40px;
  }

  .first-post {
    margin-bottom: 30px;
  }

  .post-title {
    font-size: 1.8rem;
    font-weight: normal;
    margin-bottom: 10px;
    font-family: 'Georgia Custom', Georgia, serif;
    line-height: 1.2;
    letter-spacing: -0.02em;
  }

  .first-post .post-title {
    font-size: 2.2rem;
  }

  .post-title a {
    color: rgba(240, 240, 240, 0.95);
    text-decoration: none;
    transition: color 0.3s ease;
  }

  .post-title a:hover {
    color: rgba(255, 255, 255, 1);
    text-decoration: underline;
    text-underline-offset: 3px;
    text-decoration-color: rgba(200, 200, 200, 0.4);
  }

  .post-meta {
    display: flex;
    align-items: center;
    font-size: 0.95rem;
    color: rgba(200, 200, 200, 0.75);
    margin-bottom: 16px;
    font-family: 'Georgia Custom', Georgia, serif;
  }

  .reading-time {
    margin-left: 5px;
  }

  .post-description {
    font-family: 'Georgia Custom', Georgia, serif;
    color: rgba(230, 230, 230, 0.9);
    line-height: 1.6;
    font-size: 1.05rem;
    margin-bottom: 16px;
    font-style: italic;
  }

  .post-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;
  }

  .post-tag {
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 0.85rem;
    color: rgba(200, 200, 200, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
    border-bottom: 1px solid rgba(200, 200, 200, 0.3);
    padding-bottom: 2px;
  }

  .post-tag:hover {
    color: rgba(240, 240, 240, 1);
    border-bottom-color: rgba(240, 240, 240, 0.6);
  }

  /* Read more link */
  .read-more {
    display: inline-block;
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 0.95rem;
    color: rgba(240, 240, 240, 0.9);
    text-decoration: none;
    transition: all 0.3s ease;
    padding: 0.4rem 0.9rem;
    background-color: rgba(34, 34, 34, 0.6);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
  }

  .read-more:hover {
    color: rgba(240, 240, 240, 1);
    background-color: rgba(34, 34, 34, 0.95);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .read-more-arrow {
    display: inline-block;
    margin-left: 2px;
    transition: transform 0.3s ease;
  }

  .read-more:hover .read-more-arrow {
    transform: translateX(3px);
  }

  /* Ghost-style Pagination */
  .pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 60px;
    padding-top: 30px;
    border-top: 1px solid rgba(200, 200, 200, 0.15);
  }

  .pagination-link {
    display: flex;
    align-items: center;
    gap: 8px;
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 0.95rem;
    color: rgba(240, 240, 240, 0.9);
    text-decoration: none;
    transition: all 0.3s ease;
    padding: 0.4rem 0.9rem;
    background-color: rgba(34, 34, 34, 0.6);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
  }

  .pagination-link.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: rgba(34, 34, 34, 0.3);
  }

  .pagination-link:not(.disabled):hover {
    color: rgba(240, 240, 240, 1);
    background-color: rgba(34, 34, 34, 0.95);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .pagination-info {
    font-family: 'Georgia Custom', Georgia, serif;
    font-size: 0.9rem;
    color: rgba(200, 200, 200, 0.7);
  }

  /* Responsive styles */
  @media (max-width: 1024px) {
    .blog-content {
      max-width: 100%;
    }
  }

  @media (max-width: 768px) {
    .page-container {
      flex-direction: column;
    }

    .blog-sidebar {
      width: 100%;
      padding-top: 10px;
      position: relative;
      height: auto;
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      align-items: center;
      gap: 15px;
    }

    .sidebar-section {
      margin: 0 10px 15px 0;
    }

    .blog-content {
      padding-top: 20px;
      width: 100%;
      padding-left: 0;
    }

    .post-title {
      font-size: 1.6rem;
    }

    .first-post .post-title {
      font-size: 1.8rem;
    }

    .pagination {
      flex-direction: column;
      gap: 15px;
    }

    .pagination-info {
      order: -1;
      margin-bottom: 10px;
    }
  }
</style>
</file>

<file path="src/pages/tags/[tag]/index.astro">
---
import Layout from "../../../layouts/Layout.astro";
import BlogPostCard from "../../../components/BlogPostCard.astro";
import { getAllUniqueTags, getPostsByTag } from "../../../utils/unifiedContent";
import { SITE } from "../../../config";
import { slugifyStr } from "../../../utils/slugify";

export interface Props {
  posts: any[];
  tag: string;
  tagName: string;
}

export async function getStaticPaths() {
  // Get all unique tags from JSON content
  const allTags = await getAllUniqueTags();

  // Create paths for each tag
  return Promise.all(allTags.map(async (tagName) => {
    const tag = slugifyStr(tagName);
    // Use the slug for filtering posts, not the tag name
    console.log(`Getting posts for tag: ${tagName}, slug: ${tag}`);
    const posts = await getPostsByTag(tag);

    return {
      params: { tag },
      props: {
        posts,
        tag,
        tagName
      },
    };
  }));
}

const { posts, tag, tagName } = Astro.props;
---

<Layout
  pageTitle={`Tag: ${tagName} | PVB`}
  isHomePage={false}
  accentColor="#f0f0f0"
  bgColor="rgba(0, 0, 0, 0.9)"
  backgroundImageUrl="none"
  bodyDataPage="tag"
>
  <div class="quote-container" style="max-width: 800px;">
    <h1 class="text-3xl font-bold mb-4 text-accent">
      Tag: <span transition:name={tag}>#{tagName}</span>
    </h1>
    <p class="mb-8 text-muted">
      {posts.length} post{posts.length > 1 ? "s" : ""} with this tag
    </p>

    <div class="posts-container">
      <ul>
        {posts.map(post => <BlogPostCard post={post} />)}
      </ul>
    </div>

    <div class="mt-10">
      <a
        href="/tags"
        class="back-link"
      >
        <span class="back-arrow">←</span>
        <span>All tags</span>
      </a>
    </div>
  </div>
</Layout>

<style>
  .quote-container {
    margin: 0 auto;
  }
  h1 {
    font-family: 'Georgia Custom', Georgia, serif;
  }
  .text-muted {
    color: var(--color-text-secondary);
  }
  .text-accent {
    color: var(--color-accent);
  }
  .posts-container {
    margin: 2rem 0;
    text-align: left;
  }
  .posts-container ul {
    list-style: none;
    padding: 0;
  }
  .back-link {
    display: inline-flex;
    align-items: center;
    color: var(--color-accent);
    text-decoration: none;
    transition: opacity 0.2s ease;
    font-family: 'Georgia Custom', Georgia, serif;
  }
  .back-link:hover {
    opacity: 0.75;
  }
  .back-arrow {
    margin-right: 0.5rem;
  }
</style>
</file>

<file path="src/pages/tags/index.astro">
---
import Layout from "../../layouts/Layout.astro";
import Tag from "../../components/Tag.astro";
import { getAllUniqueTags } from "../../utils/unifiedContent";
import { slugifyStr } from "../../utils/slugify";
import { SITE } from "../../config";

// Get all unique tags from JSON content
const allTagNames = await getAllUniqueTags();
const tags = allTagNames.map(tagName => ({
  tag: slugifyStr(tagName),
  tagName
}));
---

<Layout
  pageTitle="Tags | PVB"
  isHomePage={false}
  accentColor="#f0f0f0"
  bgColor="rgba(0, 0, 0, 0.9)"
  backgroundImageUrl="none"
  style="background: linear-gradient(to bottom right, rgba(0, 0, 0, 0.95), rgba(50, 50, 50, 0.9))"
  bodyDataPage="tags"
>
  <div class="quote-container" style="max-width: 800px;">
    <h1 class="text-3xl font-bold mb-4 text-accent">Tags</h1>
    <p class="mb-8 text-muted">Browse posts by topic</p>

    <div class="tags-container">
      {tags.map(({ tag, tagName }) =>
        <Tag tag={tag} tagName={tagName} size="lg" />
      )}
    </div>

    <div class="mt-10">
      <a
        href="/blog"
        class="back-link"
      >
        <span class="back-arrow">←</span>
        <span>Back to blog</span>
      </a>
    </div>
  </div>
</Layout>

<style>
  .quote-container {
    margin: 0 auto;
    text-align: center;
  }
  h1 {
    font-family: 'Georgia Custom', Georgia, serif;
  }
  .text-muted {
    color: var(--color-text-secondary);
  }
  .text-accent {
    color: var(--color-accent);
  }
  .tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
    margin: 2rem 0;
  }
  .back-link {
    display: inline-flex;
    align-items: center;
    color: var(--color-accent);
    text-decoration: none;
    transition: opacity 0.2s ease;
    font-family: 'Georgia Custom', Georgia, serif;
  }
  .back-link:hover {
    opacity: 0.75;
  }
  .back-arrow {
    margin-right: 0.5rem;
  }
</style>
</file>

<file path="src/utils/ghostContent.js">
/**
 * Ghost Content Integration
 * 
 * Treats local JSON files as if they were direct Ghost API responses
 */

import fs from 'fs';
import path from 'path';

// Path to Ghost JSON files
const CONTENT_DIR = path.join(process.cwd(), 'src/content');

/**
 * Load all posts from the local Ghost JSON
 * @returns {Array} Posts array
 */
export function getPosts() {
  try {
    const filePath = path.join(CONTENT_DIR, 'posts.json');
    if (!fs.existsSync(filePath)) {
      console.warn('posts.json not found');
      return [];
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    const data = JSON.parse(content);
    return data.posts || [];
  } catch (error) {
    console.error('Error loading posts:', error);
    return [];
  }
}

/**
 * Get a single post by slug
 * @param {string} slug Post slug
 * @returns {Object|null} Post object or null if not found
 */
export function getPostBySlug(slug) {
  const posts = getPosts();
  return posts.find(post => post.slug === slug) || null;
}

/**
 * Get posts with a specific tag
 * @param {string} tagName Tag name
 * @returns {Array} Filtered posts
 */
export function getPostsByTag(tagName) {
  const posts = getPosts();
  
  // Convert tag name to lowercase for case-insensitive comparison
  const normalizedTagName = tagName.toLowerCase();
  
  return posts.filter(post => 
    (post.tags || []).some(tag => tag.name.toLowerCase() === normalizedTagName)
  );
}

/**
 * Get all tags from posts
 * @returns {Array} Array of unique tags
 */
export function getAllTags() {
  try {
    const filePath = path.join(CONTENT_DIR, 'tags.json');
    if (!fs.existsSync(filePath)) {
      console.warn('tags.json not found');
      return [];
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    const data = JSON.parse(content);
    return data.tags || [];
  } catch (error) {
    console.error('Error loading tags:', error);
    return [];
  }
}

/**
 * Get posts by content type based on first tag
 * @param {string} type Content type ('blog', 'archive', 'work')
 * @returns {Array} Filtered posts
 */
export function getPostsByType(type) {
  const posts = getPosts();
  
  return posts.filter(post => {
    // If no tags, default to 'blog'
    if (!post.tags || post.tags.length === 0) {
      return type === 'blog';
    }
    
    // Check first tag (case insensitive)
    const firstTagName = post.tags[0].name.toLowerCase();
    
    if (type === 'archive' && firstTagName === 'archive') {
      return true;
    }
    
    if (type === 'work' && (firstTagName === 'work' || firstTagName === 'project')) {
      return true;
    }
    
    // Default to blog for anything else
    return type === 'blog' && firstTagName !== 'archive' && 
           firstTagName !== 'work' && firstTagName !== 'project';
  });
}

/**
 * Get featured posts
 * @param {string} type Optional content type filter
 * @returns {Array} Featured posts
 */
export function getFeaturedPosts(type) {
  const posts = type ? getPostsByType(type) : getPosts();
  return posts.filter(post => post.featured);
}

/**
 * Get related posts based on tags
 * @param {Object} post Current post
 * @param {number} limit Maximum number of posts to return
 * @returns {Array} Related posts
 */
export function getRelatedPosts(post, limit = 3) {
  if (!post || !post.tags || post.tags.length === 0) {
    return [];
  }
  
  const allPosts = getPosts();
  const otherPosts = allPosts.filter(p => p.id !== post.id);
  
  // Compute tag similarity score
  const scoredPosts = otherPosts.map(otherPost => {
    // Get tag names for easy comparison
    const postTags = post.tags.map(t => t.name.toLowerCase());
    const otherTags = (otherPost.tags || []).map(t => t.name.toLowerCase());
    
    // Count matching tags
    let score = 0;
    for (const tag of otherTags) {
      if (postTags.includes(tag)) {
        score++;
      }
    }
    
    return { post: otherPost, score };
  });
  
  // Sort by score (highest first) and date (newest first if same score)
  return scoredPosts
    .filter(item => item.score > 0)
    .sort((a, b) => {
      // Sort by score first
      if (b.score !== a.score) {
        return b.score - a.score;
      }
      
      // If same score, sort by date
      return new Date(b.post.published_at) - new Date(a.post.published_at);
    })
    .slice(0, limit)
    .map(item => item.post);
}

/**
 * Group posts by year for timeline view
 * @returns {Object} Posts grouped by year
 */
export function getPostsByYear(type = 'archive') {
  const posts = getPostsByType(type);
  const postsByYear = {};
  
  // Group posts by year
  for (const post of posts) {
    const year = new Date(post.published_at).getFullYear().toString();
    
    if (!postsByYear[year]) {
      postsByYear[year] = [];
    }
    
    postsByYear[year].push(post);
  }
  
  // Sort posts within each year (newest first)
  for (const year in postsByYear) {
    postsByYear[year].sort((a, b) => 
      new Date(b.published_at) - new Date(a.published_at)
    );
  }
  
  return postsByYear;
}

/**
 * Paginate posts
 * @param {Array} posts Posts to paginate
 * @param {number} page Page number (1-based)
 * @param {number} perPage Posts per page
 * @returns {Object} Pagination result with posts and metadata
 */
export function paginatePosts(posts, page = 1, perPage = 5) {
  const totalPosts = posts.length;
  const totalPages = Math.ceil(totalPosts / perPage);
  
  // Ensure valid page number
  page = Math.max(1, Math.min(page, totalPages || 1));
  
  const startIndex = (page - 1) * perPage;
  const endIndex = Math.min(startIndex + perPage, totalPosts);
  
  return {
    posts: posts.slice(startIndex, endIndex),
    pagination: {
      page,
      perPage,
      totalPages,
      totalPosts,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1,
      nextPage: page < totalPages ? page + 1 : null,
      prevPage: page > 1 ? page - 1 : null
    }
  };
}
</file>

<file path="src/components/Navigation.astro">
---
// Navigation component that displays the navigation dots
export interface Props {
    isHomePage?: boolean;
}

const { isHomePage = false } = Astro.props;

// Logic for top-left icon/title based on isHomePage
const topLeftTitle = isHomePage ? "About" : "Back";
const topLeftAriaLabel = isHomePage ? "About Page" : "Go Back";
---

<!-- Top-Left Nav Circle -->
<div class="nav-circle top-left" title={topLeftTitle} aria-label={topLeftAriaLabel}>
    <span class="nav-icon"></span> <!-- CSS handles the '?' or '←' -->
</div>

{/* Top-Right Nav Circle: only on home page */}
{isHomePage && (
  <div class="nav-circle top-right" title="Links" aria-label="Links Page">
      <span class="nav-icon"></span>
  </div>
)}

<!-- Note: Bottom-center nav circle is now handled by Layout.astro -->

<script is:inline>
  // Initialize navigation functionality
  document.addEventListener('DOMContentLoaded', function() {
    const topLeftNav = document.querySelector('.nav-circle.top-left');
    const isHomePage = document.body.getAttribute('data-page') === 'home';

    if (topLeftNav) {
      topLeftNav.addEventListener('click', function() {
        if (isHomePage) {
          // On home page, go to about page
          window.location.href = '/about';
        } else {
          // On other pages, go back
          window.history.back();
        }
      });
    }
  });
</script>

<style>
    /* Top-Left Nav Circle Styles */
    .nav-circle.top-left,
    .nav-circle.top-right {
        position: fixed;
        width: var(--circle-size);
        height: var(--circle-size);
        border-radius: 50%;
        background-color: var(--color-accent);
        cursor: pointer;
        z-index: 100;
        top: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        border: var(--circle-border-width) solid var(--color-accent);
        transition: transform var(--transition-duration) var(--easing-standard),
                  background-color var(--transition-duration) var(--easing-standard),
                  border-color var(--transition-duration) var(--easing-standard);
    }

    .nav-circle.top-left {
        left: 30px;
    }

    .nav-circle.top-right {
        right: 30px;
    }

    /* Hover effect - grows and becomes hollow */
    .nav-circle.top-left:hover,
    .nav-circle.top-right:hover {
        transform: scale(var(--circle-expand-scale));
        background-color: transparent;
    }

    /* Top Left Icon (?) / (<-) */
    .nav-circle.top-left .nav-icon::before {
        content: "?";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) scale(0.8);
        font-size: 10px;
        opacity: 0;
        transition: opacity calc(var(--transition-duration)*0.8) calc(var(--transition-duration)*0.2) var(--easing-standard),
                  transform calc(var(--transition-duration)*0.8) calc(var(--transition-duration)*0.2) var(--easing-standard);
        color: var(--color-accent);
    }

    body:not([data-page="home"]) .nav-circle.top-left .nav-icon::before {
        content: "←";
        font-size: 12px;
    }

    .nav-circle.top-left:hover .nav-icon::before {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }

    /* Top Right Icon (Link) */
    .nav-circle.top-right .nav-icon::before,
    .nav-circle.top-right .nav-icon::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 6px;
        height: 6px;
        border: 1.2px solid var(--color-accent);
        border-radius: 1.5px;
        opacity: 0;
        transition: opacity calc(var(--transition-duration)*0.8) calc(var(--transition-duration)*0.2) var(--easing-standard),
                  transform calc(var(--transition-duration)*0.8) calc(var(--transition-duration)*0.2) var(--easing-standard);
        transform-origin: center center;
    }

    .nav-circle.top-right .nav-icon::before {
        transform: translate(calc(-50% - 1.8px), -50%) rotate(45deg) scale(0.8);
    }

    .nav-circle.top-right .nav-icon::after {
        transform: translate(calc(-50% + 1.8px), -50%) rotate(45deg) scale(0.8);
    }

    .nav-circle.top-right:hover .nav-icon::before {
        opacity: 1;
        transform: translate(calc(-50% - 1.8px), -50%) rotate(45deg) scale(1);
    }

    .nav-circle.top-right:hover .nav-icon::after {
        opacity: 1;
        transform: translate(calc(-50% + 1.8px), -50%) rotate(45deg) scale(1);
    }

    /* Hide navigation when menu is active */
    body.menu-active .nav-circle.top-left,
    body.menu-active .nav-circle.top-right {
        opacity: 0;
        visibility: hidden;
        pointer-events: none;
        transition: opacity var(--transition-duration) var(--easing-standard),
                  visibility 0s var(--transition-duration);
    }

    /* Restore visibility with delay when menu closes */
    body:not(.menu-active) .nav-circle.top-left,
    body:not(.menu-active) .nav-circle.top-right {
        transition-delay: calc(var(--transition-duration) * 0.2);
    }

    /* Responsive styles */
    @media (max-width: 768px) {
        .nav-circle.top-left,
        .nav-circle.top-right {
            top: 25px;
            width: var(--circle-size);
            height: var(--circle-size);
        }

        .nav-circle.top-left {
            left: 25px;
        }

        .nav-circle.top-right {
            right: 25px;
        }
    }
</style>
</file>

<file path="src/components/ProjectCard.astro">
---
import { slugifyStr } from "../utils/slugify";
import Tag from "./Tag.astro";

export interface Props {
  project: any; // Using 'any' for Ghost content
  variant?: "featured" | "standard";
}

const { project, variant = "standard" } = Astro.props;

// Use direct JSON properties for project data
const title = project.title;
const description = project.excerpt || project.custom_excerpt || '';

// Process tags from JSON 'tags' array
const tags = Array.isArray(project.tags) ? project.tags.map((tag: any) => {
  if (typeof tag === 'object' && tag !== null) {
    // Ghost format - tag is an object with name and slug properties
    return {
      name: tag.name,
      slug: tag.slug || slugifyStr(tag.name)
    };
  } else if (typeof tag === 'string') {
    // String format - create an object with name and slugified name
    return {
      name: tag,
      slug: slugifyStr(tag)
    };
  }
  return null;
}).filter(Boolean) : [];

const featured = project.featured || false;

// For URLs, use Ghost content structure if available, otherwise use work structure
const repoUrl = project.repo_url || project.repoUrl || '';
const liveUrl = project.live_url || project.liveUrl || '';

// Use published_at for project date
const projectDate = project.published_at;

// Use status if provided in JSON
const status = project.status || '';

function formatDate(date: Date) {
  if (!date) return "";

  try {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short'
    });
  } catch (e) {
    // If we have a string instead of a date
    if (typeof date === 'string') {
      const d = new Date(date);
      return d.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short'
      });
    }
    return "";
  }
}

const isFeatured = variant === "featured";
---

{
  isFeatured ? (
    <article class="project-card featured">
      <a href={`/work/${project.slug}`} class="project-link">
        <div class="project-meta">
          <time datetime={projectDate instanceof Date ? projectDate.toISOString() : new Date(projectDate).toISOString()}>{formatDate(projectDate)}</time>
          <div class="project-badges">
            {status && status !== "Completed" && (
              <span class="project-status">{status}</span>
            )}
            <span class="featured-badge">Featured</span>
          </div>
        </div>

        <h2 class="project-title" transition:name={slugifyStr(title)}>
          {title}
        </h2>

        <p class="project-description">{description}</p>

        {tags && tags.length > 0 && (
          <div class="project-tags">
            {tags.map((tag: {slug: string, name: string}) => (
              <Tag tag={tag.slug} tagName={tag.name} />
            ))}
          </div>
        )}

        <div class="project-actions">
          <span class="view-details">
            read more <span class="arrow">→</span>
          </span>

          <div class="external-links">
            {repoUrl && (
              <a href={repoUrl} target="_blank" rel="noopener noreferrer"
                class="external-link" onclick="event.stopPropagation()">
                Source <span class="ext-arrow">↗</span>
              </a>
            )}
            {liveUrl && (
              <a href={liveUrl} target="_blank" rel="noopener noreferrer"
                class="external-link" onclick="event.stopPropagation()">
                Demo <span class="ext-arrow">↗</span>
              </a>
            )}
          </div>
        </div>
      </a>
    </article>
  ) : (
    <article class="project-card standard">
      <a href={`/work/${project.slug}`} class="project-link">
        <div class="project-content">
          <h2 class="project-title" transition:name={slugifyStr(title)}>
            {title}
          </h2>
          <p class="project-description">{description}</p>

          <div class="project-footer">
            <div class="project-meta">
              <time datetime={projectDate instanceof Date ? projectDate.toISOString() : new Date(projectDate).toISOString()}>{formatDate(projectDate)}</time>
              {status && status !== "Completed" && (
                <span class="project-status small">{status}</span>
              )}
            </div>

            <div class="project-tags-container">
              {tags && tags.length > 0 && (
                <div class="project-tags small">
                  {tags.slice(0, 3).map((tag: {slug: string, name: string}) => (
                    <Tag tag={tag.slug} tagName={tag.name} size="sm" />
                  ))}
                  {tags.length > 3 && (
                    <span class="more-tags">+{tags.length - 3}</span>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </a>
    </article>
  )
}

<style>
  /* Project card - base styling with refined transitions */
  .project-card {
    position: relative;
    transition: transform 0.5s var(--easing-standard);
  }

  .project-card.featured {
    margin-bottom: 3.5rem;
    padding-bottom: 2.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.06);
  }

  /* Standard project card styling */
  .project-card.standard {
    position: relative;
    border-radius: 2px;
    transition: transform 0.6s var(--easing-standard), opacity 0.6s var(--easing-standard);
  }

  .project-link {
    display: block;
    text-decoration: none;
    color: inherit;
    transition: color 0.4s var(--easing-standard);
    height: 100%;
  }

  .project-card.standard .project-link {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  /* Add subtle hover effect with intentional timing */
  .project-card:hover {
    transform: translateY(-3px);
  }

  /* Add a subtle highlight line animation on hover for featured projects */
  .project-card.featured::after {
    content: '';
    position: absolute;
    bottom: 2.5rem;
    left: 0;
    width: 0;
    height: 1px;
    background-color: rgba(255, 255, 255, 0.2);
    transition: width 0.7s var(--easing-standard);
  }

  .project-card.featured:hover::after {
    width: 100%;
  }

  /* Add subtle glow effect for standard project cards */
  .project-card.standard::after {
    content: '';
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.02), transparent 70%);
    opacity: 0;
    transition: opacity 0.8s var(--easing-standard);
    pointer-events: none;
    z-index: -1;
  }

  .project-card.standard:hover::after {
    opacity: 1;
  }

  /* Project Content Styling - Typography refinements */
  .project-title {
    font-size: 1.6rem;
    font-weight: normal;
    color: rgba(255, 255, 255, 0.95);
    margin-bottom: 0.9rem;
    line-height: 1.3;
    font-family: 'Georgia Custom', Georgia, serif;
    letter-spacing: -0.01em;
  }

  .project-card.standard .project-title {
    font-size: 1.25rem;
    margin-bottom: 0.7rem;
    letter-spacing: 0;
  }

  .project-description {
    font-size: 0.95rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.75);
    margin-bottom: 1.5rem;
    font-family: 'Georgia Custom', Georgia, serif;
  }

  .project-card.standard .project-description {
    font-size: 0.85rem;
    margin-bottom: 1.2rem;
    color: rgba(255, 255, 255, 0.65);
    flex-grow: 1;
    line-height: 1.5;
  }

  /* Project metadata styling with refined spacing */
  .project-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .project-card.standard .project-content {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .project-card.standard .project-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    flex-wrap: wrap;
    gap: 0.7rem;
  }

  time {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.45);
    font-family: 'Georgia Custom', Georgia, serif;
    line-height: 1;
  }

  /* Project badges & status indicators - refined with subtle styling */
  .project-badges {
    display: flex;
    gap: 0.7rem;
    align-items: center;
  }

  .project-status {
    font-size: 0.65rem;
    color: rgba(255, 215, 0, 0.85);
    border: 1px solid rgba(255, 215, 0, 0.2);
    padding: 0 0.4rem;
    border-radius: 2px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    line-height: 1.5;
  }

  .project-status.small {
    font-size: 0.6rem;
    padding: 0 0.3rem;
  }

  .featured-badge {
    font-size: 0.65rem;
    color: rgba(255, 255, 255, 0.85);
    border: 1px solid rgba(255, 255, 255, 0.15);
    padding: 0 0.4rem;
    border-radius: 2px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    line-height: 1.5;
  }

  /* Tags styling - refined with better spacing and subtle hover effects */
  .project-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
  }

  .project-tags.small {
    margin-bottom: 0;
    gap: 0.4rem;
  }

  .tag {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.6);
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    padding: 0 0 0.15rem 0;
    letter-spacing: 0.01em;
    transition: all 0.4s var(--easing-standard);
  }

  .tag.small {
    font-size: 0.65rem;
    color: rgba(255, 255, 255, 0.5);
    border-bottom: none;
  }

  .project-card:hover .tag {
    color: rgba(255, 255, 255, 0.85);
    border-bottom-color: rgba(255, 255, 255, 0.25);
  }

  .project-card:hover .tag.small {
    color: rgba(255, 255, 255, 0.7);
  }

  .more-tags {
    font-size: 0.65rem;
    color: rgba(255, 255, 255, 0.4);
    margin-left: 0.2rem;
  }

  /* Project actions - refined with better alignment and subtle animations */
  .project-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .external-links {
    display: flex;
    gap: 1rem;
  }

  .view-details {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.5);
    display: flex;
    align-items: center;
    gap: 0.25rem;
    transition: all 0.4s var(--easing-standard);
  }

  .project-card:hover .view-details {
    color: rgba(255, 255, 255, 0.85);
  }

  .view-details .arrow {
    font-size: 0.85rem;
    transition: transform 0.4s var(--easing-standard);
  }

  .project-card:hover .view-details .arrow {
    transform: translateX(0.2rem);
  }

  .external-link {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.45);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.2rem;
    transition: all 0.3s var(--easing-standard);
  }

  .external-link:hover {
    color: rgba(255, 255, 255, 0.9);
  }

  .ext-arrow {
    font-size: 0.7rem;
    transition: transform 0.3s var(--easing-standard);
  }

  .external-link:hover .ext-arrow {
    transform: translateX(0.125rem) translateY(-0.125rem);
  }

  /* Responsive adjustments */
  @media (max-width: 48rem) { /* 768px */
    .project-title {
      font-size: 1.5rem;
    }

    .project-card.standard .project-title {
      font-size: 1.15rem;
    }

    .project-description {
      font-size: 0.9rem;
    }

    .project-card.featured {
      margin-bottom: 3rem;
      padding-bottom: 2rem;
    }

    .project-card.featured::after {
      bottom: 2rem;
    }

    .project-card.standard .project-footer {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.7rem;
    }
  }

  @media (max-width: 30rem) { /* 480px */
    .project-actions {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.7rem;
    }

    .external-links {
      width: 100%;
      justify-content: flex-start;
    }
  }
</style>
</file>

<file path="src/components/Tag.astro">
---
export interface Props {
  tag: string;
  tagName: string;
  size?: "sm" | "lg";
  class?: string;
}

const { tag, tagName, size = "sm", class: className = "" } = Astro.props;
// Ensure tag and tagName are strings; if objects, extract slug/name
const safeTag = typeof tag === 'object' && tag !== null && tag.slug ? tag.slug : tag;
const safeTagName = typeof tagName === 'object' && tagName !== null && tagName.name ? tagName.name : tagName;
---

<a
  href={`/tags/${safeTag}`}
  class:list={[
    "tag-link",
    size === "sm" ? "tag-sm" : "tag-lg",
    className
  ]}
>
  #{safeTagName}
</a>

<style>
  .tag-link {
    display: inline-block;
    padding: 0.2rem 0.6rem;
    border-radius: 1rem;
    text-decoration: none;
    border: 1px solid rgba(var(--color-accent-rgb), 0.3);
    background-color: rgba(var(--color-accent-rgb), 0.05);
    color: rgba(var(--color-accent-rgb), 0.8);
    transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
    font-family: 'Georgia Custom', Georgia, serif;
  }

  .tag-link:hover {
    background-color: rgba(var(--color-accent-rgb), 0.15);
    border-color: rgba(var(--color-accent-rgb), 0.5);
    color: rgba(var(--color-accent-rgb), 1);
  }

  .tag-sm {
    font-size: 0.7rem;
  }

  .tag-lg {
    font-size: 0.8rem;
  }
</style>
</file>

<file path="src/content/config.ts">
// This file is kept for compatibility but collections are intentionally empty
// as we're using a pure JSON-based content system from src/data/ghost/

// Empty collections object to disable Astro's content collections
export const collections = {};
</file>

<file path=".astro/astro/content.d.ts">
declare module 'astro:content' {
	interface RenderResult {
		Content: import('astro/runtime/server/index.js').AstroComponentFactory;
		headings: import('astro').MarkdownHeading[];
		remarkPluginFrontmatter: Record<string, any>;
	}
	interface Render {
		'.md': Promise<RenderResult>;
	}

	export interface RenderedContent {
		html: string;
		metadata?: {
			imagePaths: Array<string>;
			[key: string]: unknown;
		};
	}
}

declare module 'astro:content' {
	type Flatten<T> = T extends { [K: string]: infer U } ? U : never;

	export type CollectionKey = keyof AnyEntryMap;
	export type CollectionEntry<C extends CollectionKey> = Flatten<AnyEntryMap[C]>;

	export type ContentCollectionKey = keyof ContentEntryMap;
	export type DataCollectionKey = keyof DataEntryMap;

	type AllValuesOf<T> = T extends any ? T[keyof T] : never;
	type ValidContentEntrySlug<C extends keyof ContentEntryMap> = AllValuesOf<
		ContentEntryMap[C]
	>['slug'];

	/** @deprecated Use `getEntry` instead. */
	export function getEntryBySlug<
		C extends keyof ContentEntryMap,
		E extends ValidContentEntrySlug<C> | (string & {}),
	>(
		collection: C,
		// Note that this has to accept a regular string too, for SSR
		entrySlug: E,
	): E extends ValidContentEntrySlug<C>
		? Promise<CollectionEntry<C>>
		: Promise<CollectionEntry<C> | undefined>;

	/** @deprecated Use `getEntry` instead. */
	export function getDataEntryById<C extends keyof DataEntryMap, E extends keyof DataEntryMap[C]>(
		collection: C,
		entryId: E,
	): Promise<CollectionEntry<C>>;

	export function getCollection<C extends keyof AnyEntryMap, E extends CollectionEntry<C>>(
		collection: C,
		filter?: (entry: CollectionEntry<C>) => entry is E,
	): Promise<E[]>;
	export function getCollection<C extends keyof AnyEntryMap>(
		collection: C,
		filter?: (entry: CollectionEntry<C>) => unknown,
	): Promise<CollectionEntry<C>[]>;

	export function getEntry<
		C extends keyof ContentEntryMap,
		E extends ValidContentEntrySlug<C> | (string & {}),
	>(entry: {
		collection: C;
		slug: E;
	}): E extends ValidContentEntrySlug<C>
		? Promise<CollectionEntry<C>>
		: Promise<CollectionEntry<C> | undefined>;
	export function getEntry<
		C extends keyof DataEntryMap,
		E extends keyof DataEntryMap[C] | (string & {}),
	>(entry: {
		collection: C;
		id: E;
	}): E extends keyof DataEntryMap[C]
		? Promise<DataEntryMap[C][E]>
		: Promise<CollectionEntry<C> | undefined>;
	export function getEntry<
		C extends keyof ContentEntryMap,
		E extends ValidContentEntrySlug<C> | (string & {}),
	>(
		collection: C,
		slug: E,
	): E extends ValidContentEntrySlug<C>
		? Promise<CollectionEntry<C>>
		: Promise<CollectionEntry<C> | undefined>;
	export function getEntry<
		C extends keyof DataEntryMap,
		E extends keyof DataEntryMap[C] | (string & {}),
	>(
		collection: C,
		id: E,
	): E extends keyof DataEntryMap[C]
		? Promise<DataEntryMap[C][E]>
		: Promise<CollectionEntry<C> | undefined>;

	/** Resolve an array of entry references from the same collection */
	export function getEntries<C extends keyof ContentEntryMap>(
		entries: {
			collection: C;
			slug: ValidContentEntrySlug<C>;
		}[],
	): Promise<CollectionEntry<C>[]>;
	export function getEntries<C extends keyof DataEntryMap>(
		entries: {
			collection: C;
			id: keyof DataEntryMap[C];
		}[],
	): Promise<CollectionEntry<C>[]>;

	export function render<C extends keyof AnyEntryMap>(
		entry: AnyEntryMap[C][string],
	): Promise<RenderResult>;

	export function reference<C extends keyof AnyEntryMap>(
		collection: C,
	): import('astro/zod').ZodEffects<
		import('astro/zod').ZodString,
		C extends keyof ContentEntryMap
			? {
					collection: C;
					slug: ValidContentEntrySlug<C>;
				}
			: {
					collection: C;
					id: keyof DataEntryMap[C];
				}
	>;
	// Allow generic `string` to avoid excessive type errors in the config
	// if `dev` is not running to update as you edit.
	// Invalid collection names will be caught at build time.
	export function reference<C extends string>(
		collection: C,
	): import('astro/zod').ZodEffects<import('astro/zod').ZodString, never>;

	type ReturnTypeOrOriginal<T> = T extends (...args: any[]) => infer R ? R : T;
	type InferEntrySchema<C extends keyof AnyEntryMap> = import('astro/zod').infer<
		ReturnTypeOrOriginal<Required<ContentConfig['collections'][C]>['schema']>
	>;

	type ContentEntryMap = {
		
	};

	type DataEntryMap = {
		
	};

	type AnyEntryMap = ContentEntryMap & DataEntryMap;

	export type ContentConfig = typeof import("./../../src/content/config.js");
}
</file>

<file path="src/layouts/Layout.astro">
---
// Main layout template for all pages
import Navigation from '../components/Navigation.astro';
import MainMenu from '../components/MainMenu.astro';
import Seo from '../components/Seo.astro'; // Import
import '../styles/global.css';
import '../styles/blog-pages.css';

export interface Props {
    title?: string;
    description?: string;
    ogImage?: string;
    canonicalURL?: URL | string;
    publishedDate?: string;
    isHomePage?: boolean; // To control the top-left icon
    accentColor?: string; // e.g., '#3a2c23'
    bgColor?: string;     // e.g., 'rgba(250, 246, 242, 0.9)'
    backgroundImageUrl?: string; // e.g., '/images/concrete.png'
    bodyDataPage?: string; // To set data-page attribute
}

const {
    title,
    description,
    ogImage,
    canonicalURL,
    publishedDate,
    isHomePage = false, // Default to false if not provided
    accentColor = 'var(--color-accent)', // Default to CSS variable
    bgColor = 'var(--color-bg)',         // Default to CSS variable
    backgroundImageUrl = 'none',         // Default to no image
    bodyDataPage = 'default'             // Default page context
} = Astro.props;

// Construct the background style string
const bodyBgStyle = backgroundImageUrl !== 'none'
    ? `background-image: url(${backgroundImageUrl}); background-color: ${bgColor};`
    : `background-color: ${bgColor};`;

// Construct the inline style for the body
const bodyStyle = `--color-accent: ${accentColor}; --color-bg: ${bgColor}; ${bodyBgStyle}`;
---

<!DOCTYPE html>
<html lang="en">
<head>
    <Seo {title} {description} {ogImage} {canonicalURL} {publishedDate} />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style is:global>
        /* CSS Variables for consistent styling */
        :root {
            /* Core sizing */
            --circle-size: 18px;
            --circle-bottom-size: 36px;
            --circle-border-width: 1.5px;
            --circle-expand-scale: 1.6;

            /* Timing & Easing */
            --transition-duration: 0.4s;
            --bottom-button-duration: 0.6s;
            --easing-standard: cubic-bezier(0.25, 0.1, 0.25, 1);
            --menu-item-exit-duration: calc(var(--transition-duration) * 0.95);
        }

        @media (max-width: 768px) {
            :root {
                --circle-size: 16px;
                --circle-bottom-size: 32px;
            }
        }
    </style>
</head>
<body data-page={bodyDataPage} style={bodyStyle}>
    <div class="page-transition" id="page-transition"></div>

    <!-- Common logo for all pages -->
    <a href="/" class="logo">pvb</a>

    <div class="content-wrapper">
        <!-- Slot for page-specific content -->
        <slot />
    </div>

    <div class="quote-card" id="quote-card">
        <h3 class="quote-card-title"></h3>
        <p class="quote-card-subtitle"></p>
    </div>

    <!-- Include navigation on all pages -->
    <Navigation isHomePage={isHomePage} />

    <!-- Always include the bottom navigation button -->
    <div class="nav-circle bottom-center" id="menu-toggle" title="Menu" aria-label="Toggle Menu">
        <span class="nav-icon"></span>
    </div>

    <!-- Include main menu on all pages -->
    <MainMenu />

    <script is:inline>
        // Function to ensure proper scrolling behavior
        document.addEventListener('DOMContentLoaded', function() {
            // Remove the transition overlay once page loads
            const pageTransition = document.getElementById('page-transition');
            setTimeout(() => {
                if (pageTransition) {
                    pageTransition.classList.remove('active');
                }
            }, 400);

            // Make sure scrollbar positioning is always at the edge
            const bodyEl = document.body;
            const pageType = bodyEl.getAttribute('data-page');

            // Add specific adjustments based on page type
            if (pageType === 'blog' || pageType === 'blog-post' || pageType === 'about' || pageType === 'work' || pageType === 'work-post') {
                bodyEl.style.overflowY = 'auto';
                bodyEl.style.height = 'auto';
                document.documentElement.style.overflowY = 'auto';
                document.documentElement.style.height = 'auto';

                // Special handling for full-page content on blog and blog post pages
                if (pageType === 'blog' || pageType === 'blog-post' || pageType === 'work' || pageType === 'work-post') {
                    const contentWrapper = document.querySelector('.content-wrapper');
                    if (contentWrapper) {
                        contentWrapper.style.position = 'relative';
                        contentWrapper.style.height = 'auto';
                        contentWrapper.style.minHeight = '100vh';
                    }
                }

                // Restore scroll position if it was saved (for navigation back)
                if (sessionStorage.getItem(`scrollPos-${pageType}`)) {
                    const savedScrollPos = parseInt(sessionStorage.getItem(`scrollPos-${pageType}`));
                    setTimeout(() => {
                        window.scrollTo(0, savedScrollPos);
                    }, 100);
                }

                // Save scroll position when navigating away
                window.addEventListener('beforeunload', function() {
                    sessionStorage.setItem(`scrollPos-${pageType}`, window.scrollY.toString());
                });

                // Change bottom button opacity on scroll
                const bottomButton = document.querySelector('.nav-circle.bottom-center');
                if (bottomButton) {
                    window.addEventListener('scroll', function() {
                        if (window.scrollY > 100) {
                            bottomButton.style.opacity = "0.7";
                        } else {
                            bottomButton.style.opacity = "1";
                        }
                    });
                }
            }
        });

        // Simple page transition
        document.addEventListener('DOMContentLoaded', function() {
            const pageTransition = document.getElementById('page-transition');
            const links = document.querySelectorAll('a:not([target="_blank"]):not([href^="#"]):not([href^="javascript"])');

            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    if (this.hostname === window.location.hostname) {
                        e.preventDefault();
                        const href = this.getAttribute('href');

                        // Apply the transition
                        if (pageTransition) {
                            pageTransition.classList.add('active');
                        }

                        // Navigate after transition
                        setTimeout(() => {
                            window.location.href = href;
                        }, 400); // Match the CSS transition duration
                    }
                });
            });
        });
    </script>

    <!-- Fix: Use either is:inline or src, not both -->
    <script src="/scripts/main.js" defer></script>
</body>
</html>

<style>
    /* Additional styles specific to the Layout component */
    html, body {
        scroll-behavior: smooth;
        margin: 0;
        padding: 0;
        height: 100%;
        scrollbar-width: thin;
        scrollbar-color: rgba(100, 100, 100, 0.4) transparent;
    }

    /* Ensure scrollbar is always positioned correctly */
    :global(::-webkit-scrollbar) {
        width: 8px;
        height: 8px;
    }

    :global(::-webkit-scrollbar-track) {
        background: transparent;
    }

    :global(::-webkit-scrollbar-thumb) {
        background-color: rgba(100, 100, 100, 0.4);
        border-radius: 4px;
    }

    :global(::-webkit-scrollbar-thumb:hover) {
        background-color: rgba(120, 120, 120, 0.6);
    }

    body {
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        transition: background-color var(--transition-duration) var(--easing-standard);
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        background-blend-mode: normal;
        font-family: 'Georgia Custom', Georgia, serif;
    }

    /* Common logo styling for all pages */
    .logo {
        position: fixed;
        top: 30px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 1rem;
        font-weight: normal;
        z-index: 10;
        font-family: 'Georgia Custom', Georgia, serif;
        text-decoration: none;
        cursor: pointer;
        color: var(--color-accent);
        transition: color var(--transition-duration) var(--easing-standard);
    }

    .logo:hover {
        opacity: 0.85;
    }

    /* Content wrapper */
    .content-wrapper {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        transition: filter var(--transition-duration) var(--easing-standard),
                    opacity var(--transition-duration) var(--easing-standard);
        z-index: 1;
    }

    /* Different content wrapper styles for different page types */
    body[data-page="blog"] .content-wrapper,
    body[data-page="blog-post"] .content-wrapper,
    body[data-page="work"] .content-wrapper,
    body[data-page="work-post"] .content-wrapper {
        position: relative;
        justify-content: flex-start;
        align-items: stretch;
        height: auto;
        min-height: 100vh;
    }

    /* About page specific - fix for mobile image */
    body[data-page="about"] {
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        background-attachment: fixed;  /* This helps with mobile view */
    }

    /* Prevent content overflow issues */
    .content-wrapper {
        overflow-x: hidden;
    }

    /* Ensure bottom center navigation is always fixed in position */
    .nav-circle.bottom-center {
        position: fixed !important;
        bottom: 30px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 100;
        width: var(--circle-bottom-size);
        height: var(--circle-bottom-size);
        border-radius: 50%;
        border: var(--circle-border-width) solid rgba(50, 50, 50, 0.8);
        background-color: rgba(50, 50, 50, 0.6);
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: transform var(--bottom-button-duration) var(--easing-standard),
                    background-color var(--bottom-button-duration) var(--easing-standard),
                    border-color var(--bottom-button-duration) var(--easing-standard),
                    opacity 0.3s ease;
    }

    /* Icon within bottom center nav */
    .nav-circle.bottom-center .nav-icon {
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    /* Menu Toggle Hover Effect */
    body:not(.menu-active) .nav-circle.bottom-center:hover {
        transform: translateX(-50%) scale(1.5);
    }

    /* Menu Toggle Active Effect */
    body.menu-active .nav-circle.bottom-center {
        background-color: var(--color-accent);
        border-color: var(--color-accent);
    }

    /* Simple page transition layer */
    .page-transition {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: #111;
        opacity: 1;
        visibility: visible;
        z-index: 1000;
        transition: opacity var(--transition-duration) var(--easing-standard),
                    visibility 0s var(--transition-duration);
    }

    .page-transition.active {
        opacity: 1;
        visibility: visible;
        transition: opacity var(--transition-duration) var(--easing-standard), visibility 0s;
    }

    /* Not active - hide it completely */
    .page-transition:not(.active) {
        opacity: 0;
        visibility: hidden;
    }

    /* Menu effects */
    body.menu-active .content-wrapper {
        filter: blur(4px);
        opacity: 0.5;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .logo {
            top: 20px;
            font-size: 0.9rem;
        }

        .nav-circle.bottom-center {
            bottom: 25px;
        }
    }
</style>
</file>

<file path="src/pages/about.astro">
---
import Layout from '../layouts/Layout.astro';
---

<Layout
    pageTitle="About | PVB"
    isHomePage={false}
    accentColor="#3a2c23"
    bgColor="#3a2c23"
    backgroundImageUrl="/images/limestone.png"
    bodyDataPage="about"
>
    <div class="about-header">
        <div class="about-title">about</div>
    </div>
    <div class="content-container">
        <section class="about-section intro-section">
            <p>This is the main introductory text about me. Keep it concise and engaging. Briefly touch upon what drives you or the purpose of this site.</p>
            <p>This demonstrates how the components can be reused with different properties to change the appearance and behavior.</p>
        </section>

        <section class="about-section detail-section">
            <h2>My Journey</h2>
            <p>Expand on your background, key experiences, and the path that led you here. Use storytelling elements if appropriate.</p>
        </section>

        <section class="about-section detail-section">
            <h2>Skills & Expertise</h2>
            <p>List or describe your core skills, tools you master, and areas you specialize in.</p>
            <ul>
                <li>Skill/Area 1: Brief description.</li>
                <li>Skill/Area 2: Brief description.</li>
                <li>Skill/Area 3: Brief description.</li>
            </ul>
        </section>

        <section class="about-section detail-section">
            <h2>Philosophy</h2>
            <p>Discuss your approach to work, design principles, or core values that guide you.</p>
        </section>
    </div>

    <script is:inline>
      // Set up back button event
      document.addEventListener('DOMContentLoaded', function() {
        const backButton = document.querySelector('.nav-circle.top-left');
        if (backButton) {
          backButton.addEventListener('click', () => {
            window.history.back();
          });
        }
      });
    </script>
</Layout>

<style>
    /* About Title - Static (not fixed) positioned below the logo */
    .about-header {
        width: 100%;
        display: flex;
        justify-content: center;
        margin: 55px 0 30px; /* Position below the logo with more space below */
    }

    .about-title {
        font-family: 'Georgia Custom', Georgia, serif;
        font-size: 1.9rem;
        color: #3a2c23;
        letter-spacing: -0.01em;
        position: relative;
    }

    /* Add subtle underline to about title */
    .about-title::after {
        content: '';
        position: absolute;
        bottom: -8px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 1px;
        background-color: #3a2c23;
    }

    .content-container {
        max-width: 800px;
        margin: 0 auto 60px;
        padding: 0 30px;
    }

    /* Back to Home Link */
    .back-section {
        margin-bottom: 40px;
    }

    .about-content {
        padding: 20px 0;
    }

    h1 {
        font-family: 'Georgia Custom', Georgia, serif;
        margin-bottom: 2rem;
        font-size: 2.2rem;
        color: #2c1f16;
        letter-spacing: -0.02em;
    }

    h2 {
        font-family: 'Georgia Custom', Georgia, serif;
        margin: 2.5rem 0 1.2rem;
        font-size: 1.6rem;
        color: #24170f;
        letter-spacing: -0.01em;
    }

    p {
        font-family: 'Georgia Custom', Georgia, serif;
        line-height: 1.2;
        margin-bottom: 1.5rem; /* Increased spacing between paragraphs */
        font-size: 1.1rem;
        color: #2b1d15;
    }

    .future-content-section {
        margin-top: 4rem; /* Increased spacing */
        padding-top: 2.5rem; /* Increased spacing */
        border-top: 1px solid rgba(200, 200, 200, 0.15);
    }

    /* Improved mobile responsiveness */
    @media (max-width: 768px) {
        .about-title {
            font-size: 1.3rem;
        }

        .about-header {
            margin: 45px 0 15px;
        }

        .content-container {
            padding: 0 20px;
        }

        h1 {
            font-size: 1.8rem;
            margin-bottom: 1.5rem;
        }

        h2 {
            font-size: 1.4rem;
            margin: 2rem 0 1rem;
        }

        p {
            font-size: 1rem;
            margin-bottom: 1.5rem;
        }

        .future-content-section {
            margin-top: 3rem;
            padding-top: 2rem;
        }
    }

    @media (max-width: 768px) {
        .about-header {
            margin: 50px 0 30px;
        }

        .about-title {
            font-size: 1.6rem;
        }

        .content-container {
            padding: 0 20px;
            max-width: 90%;
        }

        h2 {
            font-size: 1.3rem;
        }

        .intro-section p {
            font-size: 1.0rem;
        }

        ul {
            padding-left: 20px;
        }
    }

    @media (max-width: 480px) {
        .content-container {
            padding: 0 15px;
        }
    }
</style>
</file>

<file path="src/styles/global.css">
/* Font Imports */
@font-face { font-family: 'Georgia Custom'; src: url('/fonts/georgia-ref.ttf') format('truetype'); font-weight: normal; font-style: normal; }
@font-face { font-family: 'Georgia Custom'; src: url('/fonts/georgia-bold-2.ttf') format('truetype'); font-weight: bold; font-style: normal; }
@font-face { font-family: 'Serif12'; src: url('/fonts/serif12-beta-regular.otf') format('opentype'); font-weight: normal; font-style: normal; }

/* Reset & Base */
:root {
  /* Core sizing */
  --circle-size: 18px;
  --circle-bottom-size: 36px;
  --indicator-line-width: 12px; --indicator-line-height: 2px; --indicator-circle-size: 20px; --indicator-wrapper-size: 44px;
  --circle-size-mobile: 16px;
  --circle-bottom-size-mobile: 32px;
  --circle-border-width: 1.5px;
  --circle-expand-scale: 1.6;
  --x-line-thickness: 0.8px;
  --plus-line-thickness: 0.9px;

  /* Timing & Easing */
  --transition-duration: 0.4s; /* Default speed (e.g., for unhover) */
  --bottom-button-duration: 0.6s;
  --bottom-hover-duration: 0.5s;
  --plus-grow-duration: 0.7s; /* SLOW duration for '+' hover grow */
  --plus-to-x-duration: 0.35s; /* FAST duration for + transforming into X */
  --easing-standard: cubic-bezier(0.25, 0.1, 0.25, 1);
  --easing-out-smooth: ease-out; /* Smoother easing for '+' grow */
  /* Easing for the fast + to X transition - snappy with slight overshoot */
  --easing-plus-to-x: cubic-bezier(0.34, 1.56, 0.64, 1);
  --easing-dramatic-spin: cubic-bezier(0.68, -0.55, 0.27, 1.55);
  --focus-transition-duration: 0.2s;
  --menu-item-exit-duration: calc(var(--transition-duration) * 0.95);

  /* Colors */
  --color-bg: rgba(250, 246, 242, 0.9);
  --color-bg-overlay: rgba(0, 0, 0, 0.96);
  --color-card-bg: #fdfbf9;
  --color-accent: #3a2c23;
  --color-accent-darker: #2a1f1a;
  --color-accent-inverse: #fff;
  --color-inactive: #8a8178;
  --color-text: var(--color-accent);
  --color-text-secondary: #6a5a4f;
  --color-text-inverse: #f0f0f0;
  --color-logo-menu: rgba(240, 240, 240, 0.7);
  --color-fitness-accent: #6D464F; /* New: Dark pinkish-brown for fitness page */
  --shadow-card: 0 6px 20px rgba(0, 0, 0, 0.08);
  --shadow-card-hover: 0 10px 25px rgba(0, 0, 0, 0.15);
  --shadow-card-active: 0 4px 15px rgba(0, 0, 0, 0.1);
  --shadow-card-inset: inset 0 0 10px rgba(0, 0, 0, 0.03);

  /* Navigation Bottom Active State */
  --nav-bottom-active-bg: rgba(20, 20, 20, 0.95);
  --nav-bottom-active-border: rgba(30, 30, 30, 0.95);
  --nav-bottom-active-icon: var(--color-accent-inverse);
  
  /* Button Colors - Silvery Grey Instead of White */
  --button-bg: #d0d0d0;
  --button-hover-bg: #c0c0c0;
  --button-active-bg: #b8b8b8;
  --button-text: #222222;

  /* Blog-specific Colors */
  --blog-bg-tint: rgba(10, 10, 10, 0.94);
  --blog-text: rgba(240, 240, 240, 0.9);
  --blog-text-secondary: rgba(210, 210, 210, 0.85);
  --blog-border: rgba(200, 200, 200, 0.15);
  --blog-toc-hover: rgba(255, 255, 255, 0.04);
  --blog-toc-active: rgba(255, 255, 255, 0.08);
}

/* Global Scrollbar Styling */
html {
  scrollbar-width: thin;
  scrollbar-color: rgba(100, 100, 100, 0.4) transparent;
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(100, 100, 100, 0.4);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(120, 120, 120, 0.6);
}

/* Dark theme variables (Placeholder) - Removed empty ruleset */

* { margin: 0; padding: 0; box-sizing: border-box; }
html, body { height: 100%; font-family: 'Georgia Custom', Georgia, serif; color: var(--color-text); }

/* Allow vertical scrolling for all pages */
body {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    transition: background-color var(--transition-duration) var(--easing-standard);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    overflow-y: auto;
    /* The background image will be set via inline styles on the body based on props */
}

/* Ensure smooth scrolling */
html {
  scroll-behavior: smooth;
}

.content-wrapper { position: fixed; top: 0; left: 0; width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; flex-direction: column; transition: filter var(--transition-duration) var(--easing-standard), opacity var(--transition-duration) var(--easing-standard); z-index: 1; }
body.menu-active .content-wrapper { filter: blur(4px); opacity: 0.5; transition: filter calc(var(--transition-duration) * 1.1) var(--easing-standard), opacity calc(var(--transition-duration) * 1.1) var(--easing-standard); }
body.quote-card-active .content-wrapper { filter: blur(3px); opacity: 0.7; }

/* Adjust content wrapper for blog and blogpost pages */
body[data-page="blog"] .content-wrapper,
body[data-page="blog-post"] .content-wrapper,
body[data-page="about"] .content-wrapper,
body[data-page="fitness"] .content-wrapper {
  position: relative;
  justify-content: flex-start;
  /* Hide table of contents on about page */
  .toc-container { display: none; }
  height: auto;
  min-height: 100vh;
}

/* Logo Link Styling - Centered */
.logo, .menu-logo { position: absolute; top: 30px; left: 50%; transform: translateX(-50%); font-size: 1rem; font-weight: normal; z-index: 10; font-family: 'Serif12', Georgia, serif; text-decoration: none; cursor: pointer; color: var(--color-text-secondary); transition: color var(--transition-duration) var(--easing-standard), opacity var(--transition-duration) var(--easing-standard); text-align: center; }
.logo:hover, .menu-logo:hover { color: var(--color-text); }
.menu-logo { color: var(--color-logo-menu); opacity: 0; z-index: 51; }
.menu-logo:hover { color: var(--color-text-inverse); }
 body.menu-active .menu-logo { opacity: 1; transition-delay: calc(var(--transition-duration) * 0.5); }
 body.menu-active.closing .menu-logo { opacity: 0; transition: opacity calc(var(--transition-duration)*0.5) ease-out; transition-delay: 0s; }

/* Nav Circles - Base styling */
.nav-circle { position: absolute; width: var(--circle-size); height: var(--circle-size); border-radius: 50%; cursor: pointer; display: flex; justify-content: center; align-items: center; z-index: 100; border: var(--circle-border-width) solid var(--color-accent); transition: transform var(--transition-duration) var(--easing-standard), background-color var(--transition-duration) var(--easing-standard), border-color var(--transition-duration) var(--easing-standard), width var(--transition-duration) var(--easing-standard), height var(--transition-duration) var(--easing-standard), opacity var(--transition-duration) var(--easing-standard), visibility 0s var(--transition-duration); opacity: 1; visibility: visible; transform-origin: center center; }
.nav-circle.top-left, .nav-circle.top-right { background-color: var(--color-accent); border-color: var(--color-accent); top: 30px; }
.nav-circle.top-left { left: 30px; }
.nav-circle.top-right { right: 30px; }
.nav-circle.bottom-center { background-color: transparent; width: var(--circle-bottom-size); height: var(--circle-bottom-size); bottom: 30px; left: 50%; transform: translateX(-50%); transition: transform var(--bottom-button-duration) var(--easing-standard), background-color var(--bottom-button-duration) var(--easing-standard), border-color var(--bottom-button-duration) var(--easing-standard), width var(--transition-duration) var(--easing-standard), height var(--transition-duration) var(--easing-standard); }

/* Override top-right nav circle style for blog post page (hidden) */
body[data-page="blog-post"] .nav-circle.top-right {
  display: none;
}

/* Special styling for bottom nav button in blog and tag sections when menu is active */
body[data-page="blog"].menu-active .nav-circle.bottom-center,
body[data-page="blog-post"].menu-active .nav-circle.bottom-center,
body[data-page="tag"].menu-active .nav-circle.bottom-center,
body[data-page="blog"].menu-active .nav-circle.bottom-center,
body[data-page="blog-post"].menu-active .nav-circle.bottom-center,
body[data-page="tag"].menu-active .nav-circle.bottom-center,
body[data-page="tags"].menu-active .nav-circle.bottom-center,
body[data-page="about"].menu-active .nav-circle.bottom-center {
  background-color: var(--nav-bottom-active-bg);
  border-color: var(--nav-bottom-active-border);
}

/* Homepage: Make bottom button hollow like top buttons */
body[data-page="home"] .nav-circle.bottom-center {
  background-color: transparent;
  border-color: #222222; /* Match top button color */
}

/* Fitness Page: Dark pinkish-brown navigation styling */
body[data-page="fitness"] .nav-circle.top-left,
body[data-page="fitness"] .nav-circle.top-right {
  background-color: var(--color-fitness-accent);
  border-color: var(--color-fitness-accent);
}

/* Keep icons in fitness page buttons with correct color on hover */
body[data-page="fitness"] .nav-circle.top-left:hover .nav-icon::before,
body[data-page="fitness"] .nav-circle.top-right:hover .nav-icon::before,
body[data-page="fitness"] .nav-circle.top-right:hover .nav-icon::after {
  color: var(--color-fitness-accent);
  border-color: var(--color-fitness-accent);
}

/* Fitness Page: Make bottom button hollow like home page */
body[data-page="fitness"] .nav-circle.bottom-center {
  background-color: transparent;
  border-color: var(--color-fitness-accent);
}

/* Fitness Page: Bottom button styling when menu is active */
body[data-page="fitness"].menu-active .nav-circle.bottom-center {
  background-color: var(--color-fitness-accent);
  border-color: var(--color-fitness-accent);
}

/* Blog/Tag Pages: Make top buttons white */
body[data-page="blog"] .nav-circle.top-left,
body[data-page="blog"] .nav-circle.top-right,
body[data-page="blog-post"] .nav-circle.top-left, /* Also apply to blog posts */
body[data-page="tag"] .nav-circle.top-left,
body[data-page="tag"] .nav-circle.top-right,
body[data-page="tags"] .nav-circle.top-left,
body[data-page="tags"] .nav-circle.top-right {
  background-color: var(--color-accent-inverse); /* White background */
  border-color: #222222; /* Keep dark border */
}
/* Keep icons white on hover for white buttons */
body[data-page="blog"] .nav-circle.top-left:hover .nav-icon::before,
body[data-page="blog"] .nav-circle.top-right:hover .nav-icon::before,
body[data-page="blog"] .nav-circle.top-right:hover .nav-icon::after,
body[data-page="blog-post"] .nav-circle.top-left:hover .nav-icon::before,
body[data-page="tag"] .nav-circle.top-left:hover .nav-icon::before,
body[data-page="tag"] .nav-circle.top-right:hover .nav-icon::before,
body[data-page="tag"] .nav-circle.top-right:hover .nav-icon::after,
body[data-page="tags"] .nav-circle.top-left:hover .nav-icon::before,
body[data-page="tags"] .nav-circle.top-right:hover .nav-icon::before,
body[data-page="tags"] .nav-circle.top-right:hover .nav-icon::after {
  color: var(--color-accent-inverse); /* Keep icons white on hover */
  border-color: var(--color-accent-inverse); /* White borders for link icons */
}

/* Hide TL/TR buttons when menu is active */
body.menu-active .nav-circle.top-left,
body.menu-active .nav-circle.top-right { opacity: 0; visibility: hidden; pointer-events: none; transition: opacity var(--transition-duration) var(--easing-standard), visibility 0s var(--transition-duration); }
body:not(.menu-active) .nav-circle.top-left,
body:not(.menu-active) .nav-circle.top-right { transition-delay: calc(var(--transition-duration) * 0.2); }

/* Interaction States */
.nav-circle:active { transform: scale(0.95); transition: transform 0.1s ease-out; }
.nav-circle.bottom-center:active { transform: translateX(-50%) scale(0.95); }
.nav-circle.top-left:hover, .nav-circle.top-right:hover { transform: scale(var(--circle-expand-scale)); background-color: transparent; }

/* Bottom button inactive hover - Slower scale */
body:not(.menu-active) .nav-circle.bottom-center:hover { transform: translateX(-50%) scale(1.5); transition: transform var(--bottom-hover-duration) var(--easing-standard); }
body:not(.menu-active) .nav-circle.bottom-center:hover:active { transform: translateX(-50%) scale(1.45); }

/* Quote Indicator */
.quote-indicator-wrapper { position: absolute; width: var(--indicator-wrapper-size); height: var(--indicator-wrapper-size); display: flex; justify-content: center; align-items: center; cursor: pointer; z-index: 50; }
.quote-indicator { width: var(--indicator-line-width); height: var(--indicator-line-height); background-color: var(--color-inactive); border-radius: 1px; transition: all var(--transition-duration) var(--easing-standard); }
.quote-indicator-wrapper.active .quote-indicator { width: var(--indicator-circle-size); height: var(--indicator-circle-size); background-color: transparent; border: var(--circle-border-width) solid var(--color-inactive); border-radius: 50%; }
.quote-indicator-wrapper.active:hover .quote-indicator { transform: scale(1.15); }

/* --- Icons within Nav Circles --- */
.nav-icon { position: relative; width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; color: var(--color-accent); transition: color var(--transition-duration) var(--easing-standard); line-height: 1; }
/* Top Icons */
.nav-circle.top-left .nav-icon::before { content: "?"; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%) scale(0.8); font-size: 10px; opacity: 0; transition: opacity calc(var(--transition-duration)*0.8) calc(var(--transition-duration)*0.2) var(--easing-standard), transform calc(var(--transition-duration)*0.8) calc(var(--transition-duration)*0.2) var(--easing-standard); color: var(--color-accent); }
body:not([data-page="home"]) .nav-circle.top-left .nav-icon::before { content: "←"; font-size: 12px; }
.nav-circle.top-left:hover .nav-icon::before { opacity: 1; transform: translate(-50%, -50%) scale(1); }
.nav-circle.top-right .nav-icon::before, .nav-circle.top-right .nav-icon::after { content: ''; position: absolute; top: 50%; left: 50%; width: 6px; height: 6px; border: 1.2px solid var(--color-accent); border-radius: 1.5px; opacity: 0; transition: opacity calc(var(--transition-duration)*0.8) calc(var(--transition-duration)*0.2) var(--easing-standard), transform calc(var(--transition-duration)*0.8) calc(var(--transition-duration)*0.2) var(--easing-standard); transform-origin: center center; }
.nav-circle.top-right .nav-icon::before { transform: translate(calc(-50% - 1.8px), -50%) rotate(45deg) scale(0.8); }
.nav-circle.top-right .nav-icon::after { transform: translate(calc(-50% + 1.8px), -50%) rotate(45deg) scale(0.8); }
.nav-circle.top-right:hover .nav-icon::before { opacity: 1; transform: translate(calc(-50% - 1.8px), -50%) rotate(45deg) scale(1); }
.nav-circle.top-right:hover .nav-icon::after { opacity: 1; transform: translate(calc(-50% + 1.8px), -50%) rotate(45deg) scale(1); }

/* --- Unified Bottom center + / X transformation --- */
/* Base state for the lines (TINY, invisible, NO rotation) */
.nav-circle.bottom-center .nav-icon::before,
.nav-circle.bottom-center .nav-icon::after {
    content: '';
    position: absolute;
    background-color: var(--color-accent);
    top: 50%;
    left: 50%;
    width: 40%;
    height: var(--plus-line-thickness);
    transform-origin: center;
    opacity: 0;
    /* Start tiny and centered, initial rotation based on line */
    transform: translate(-50%, -50%) scale(0.05) rotate(var(--plus-initial-rotation, 0deg));
    /* Default transition (handles UNHOVER shrink and X->+ reset after close) */
    transition: transform var(--transition-duration) var(--easing-standard),
                opacity calc(var(--transition-duration) * 0.7) var(--easing-standard),
                background-color var(--transition-duration) var(--easing-standard),
                height var(--transition-duration) var(--easing-standard);
}
/* Set initial rotations for '+' lines */
.nav-circle.bottom-center .nav-icon::before { --plus-initial-rotation: 90deg; } /* Vertical line */
.nav-circle.bottom-center .nav-icon::after { --plus-initial-rotation: 0deg; }   /* Horizontal line */


/* '+' grow-in on bottom hover (when inactive) - SLOW, STRAIGHT, remains SMALL */
body:not(.menu-active) .nav-circle.bottom-center:hover .nav-icon::before,
body:not(.menu-active) .nav-circle.bottom-center:hover .nav-icon::after {
    opacity: 0.9;
    /* Grow slowly to a SMALL scale, maintaining initial rotation */
    transform: translate(-50%, -50%) scale(0.6) rotate(var(--plus-initial-rotation, 0deg));
    /* SLOWER transition ONLY for hover grow */
    transition: opacity var(--plus-grow-duration) var(--easing-out-smooth),
                transform var(--plus-grow-duration) var(--easing-out-smooth);
                /* Color/Height don't change on hover, use default transition if needed */
}

/* Fitness page specific button icon coloring */
body[data-page="fitness"] .nav-circle.bottom-center .nav-icon::before,
body[data-page="fitness"] .nav-circle.bottom-center .nav-icon::after {
    background-color: var(--color-fitness-accent);
}

/* State when menu is ACTIVE (+ transforming into X) - FAST, SNAPPY */
body.menu-active .nav-circle.bottom-center .nav-icon::before,
body.menu-active .nav-circle.bottom-center .nav-icon::after {
    /* White X for visibility on dark backgrounds */
    background-color: var(--nav-bottom-active-icon);
    opacity: 1;
    height: var(--x-line-thickness); /* X thickness */
    /* Target state: full size, rotated X */
    transform: translate(-50%, -50%) rotate(var(--x-rotation, 0deg)) scale(1);
    /* FAST transition specifically for the + to X formation */
    transition: transform var(--plus-to-x-duration) var(--easing-plus-to-x),
                opacity var(--plus-to-x-duration) ease-in-out, /* Faster fade in */
                background-color var(--plus-to-x-duration) var(--easing-standard),
                height var(--plus-to-x-duration) var(--easing-standard);
}
/* Define target X rotations */
body.menu-active .nav-circle.bottom-center .nav-icon::before {
  --x-rotation: 45deg;
}
body.menu-active .nav-circle.bottom-center .nav-icon::after {
  --x-rotation: -45deg;
}


/* Hover effect for Active X (Larger X) - Uses default transition */
body.menu-active .nav-circle.bottom-center:hover { transform: translateX(-50%) scale(1.25); }
body.menu-active .nav-circle.bottom-center:hover .nav-icon::before { transform: translate(-50%, -50%) rotate(45deg) scale(1.6); }
body.menu-active .nav-circle.bottom-center:hover .nav-icon::after { transform: translate(-50%, -50%) rotate(-45deg) scale(1.6); }
body.menu-active .nav-circle.bottom-center:active { transform: translateX(-50%) scale(1.2); }

/* Closing X animation (Dramatic Spin Out + Fall) */
 body.menu-active.closing .nav-circle.bottom-center .nav-icon::before,
 body.menu-active.closing .nav-circle.bottom-center .nav-icon::after {
     opacity: 0;
     /* Define target transform for closing */
     transform: translate(-50%, calc(-50% + 15px)) scale(0.4) rotate(var(--x-close-rotation)); /* Fall down */
     /* Use dramatic spin easing and exit duration */
     transition: transform var(--menu-item-exit-duration) var(--easing-dramatic-spin),
                 opacity calc(var(--menu-item-exit-duration) * 0.7) ease-out;
     /* Background/Height transition back using default (or specify if needed) */
 }
 /* Define closing rotations */
 body.menu-active.closing .nav-circle.bottom-center .nav-icon::before { --x-close-rotation: 225deg; }
 body.menu-active.closing .nav-circle.bottom-center .nav-icon::after { --x-close-rotation: 135deg; }
/* --- End Unified + / X Animation --- */


/* Quote Card */
.quote-card { position: fixed; left: 50%; transform: translate(-50%, 10px) scale(0.95); background-color: var(--color-card-bg); box-shadow: var(--shadow-card-active), var(--shadow-card-inset); border-radius: 4px; padding: 15px 20px; min-width: 260px; max-width: 300px; opacity: 0; visibility: hidden; z-index: 40; cursor: pointer; transition: opacity calc(var(--transition-duration) * 0.9) var(--easing-standard), visibility 0s calc(var(--transition-duration) * 0.9), transform calc(var(--transition-duration) * 0.9) var(--easing-standard), box-shadow var(--transition-duration) var(--easing-standard), top var(--transition-duration) var(--easing-standard); transform-origin: 50% 0%; }
.quote-card.active { opacity: 1; visibility: visible; transform: translate(-50%, 0) scale(1); transition: opacity var(--transition-duration) var(--easing-standard), visibility 0s, transform var(--transition-duration) var(--easing-standard), box-shadow var(--transition-duration) var(--easing-standard), top var(--transition-duration) var(--easing-standard); }
.quote-card.active:hover { box-shadow: var(--shadow-card-hover), var(--shadow-card-inset); transform: translate(-50%, -3px) scale(1.02); }
.quote-card-title { font-size: 0.9rem; font-weight: bold; margin-bottom: 6px; color: var(--color-text); font-family: 'Georgia Custom', Georgia, serif; }
.quote-card-subtitle { font-size: 0.75rem; color: var(--color-text-secondary); font-family: 'Georgia Custom', Georgia, serif; }

/* Quote Container - Centered Text */
.quote-container { text-align: center; max-width: 550px; padding: 20px; position: relative; }
.quote-container .quote-indicator-wrapper { position: absolute; bottom: 100%; left: 50%; transform: translateX(-50%); margin-bottom: 25px; }
.quote-text { font-size: 1.2rem; line-height: 1.65; margin-bottom: 1em; color: var(--color-text); font-family: 'Georgia Custom', Georgia, serif; }
.quote-attribution { font-size: 0.9rem; font-style: italic; color: var(--color-text-secondary); font-family: 'Georgia Custom', Georgia, serif; }

/* Menu - Centered Content */
.main-menu { position: fixed; top: 0; left: 0; width: 100%; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; opacity: 0; visibility: hidden; z-index: 50; background-color: var(--color-bg-overlay); backdrop-filter: blur(4px); transition: opacity var(--transition-duration) var(--easing-standard), visibility 0s var(--transition-duration), backdrop-filter var(--transition-duration) var(--easing-standard); }
.menu-wrapper { position: relative; width: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; flex-grow: 1; }

/* Base Menu Item Styles */
.main-menu a { color: var(--color-text-inverse); text-decoration: none; opacity: 0; filter: blur(0px); transition: opacity var(--transition-duration) var(--easing-standard), transform var(--transition-duration) var(--easing-standard), filter var(--focus-transition-duration) ease-out; font-family: 'Georgia Custom', Georgia, serif; text-align: center; }

/* Specific Menu Item Sizes & Positioning */
.main-menu > .menu-wrapper > a:not(.side-menu-item):not(.see-more) { font-size: 1.5rem; margin: 15px 0; transform: translateY(20px); }
.side-menu-item { position: absolute; font-size: 0.8rem; top: 50%; transform: translateX(var(--slide-offset, 0)) translateY(calc(-50% + 20px)); }
.side-menu-item.left { right: calc(50% + 240px); --slide-offset: -50px; }
.side-menu-item.right { left: calc(50% + 240px); --slide-offset: 50px; }
.see-more { font-size: 0.8rem; margin-top: 45px; margin-bottom: 35px; display: flex; flex-direction: column; align-items: center; transform: translateY(25px) scale(0.95); }
.see-more .arrow { font-size: 1rem; margin-top: 3px; display: block; }

/* Active state transforms (Slide into place) */
body.menu-active .main-menu > .menu-wrapper > a:not(.side-menu-item):not(.see-more) { transform: translateY(0); opacity: 0.9; }
body.menu-active .side-menu-item { transform: translateX(0) translateY(-50%); opacity: 0.9; }
body.menu-active .see-more { transform: translateY(0) scale(1); opacity: 0.9; }

/* Focus effect */
.menu-wrapper:has(> a:hover) > *:not(a:hover):not(.menu-logo) { filter: blur(1px); opacity: 0.7; }
.menu-wrapper > a:hover { opacity: 1 !important; filter: none !important; }

/* Menu Active State */
body.menu-active { color: var(--color-text-inverse); }
body.menu-active .main-menu { opacity: 1; visibility: visible; transition: opacity var(--transition-duration) var(--easing-standard), visibility 0s, backdrop-filter var(--transition-duration) var(--easing-standard); }
body.menu-active .nav-circle.bottom-center { background-color: var(--nav-bottom-active-bg); border-color: var(--nav-bottom-active-border); }

/* Menu closing animation */
body.menu-active.closing .main-menu a { opacity: 0; filter: blur(1px); transition: opacity var(--menu-item-exit-duration) ease-out, transform var(--menu-item-exit-duration) ease-out, filter var(--menu-item-exit-duration) ease-out; }
 body.menu-active.closing .main-menu > .menu-wrapper > a:not(.side-menu-item):not(.see-more) { transform: translateY(25px) scale(0.95); }
 body.menu-active.closing .side-menu-item { transform: translateX(var(--slide-offset, 0)) translateY(calc(-50% + 25px)) scale(0.95); }
 body.menu-active.closing .see-more { transform: translateY(30px) scale(0.9); }

/* Page transition layer */
.page-transition { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: #111; opacity: 0; visibility: hidden; z-index: 1000; transition: opacity calc(var(--transition-duration) * 1) var(--easing-standard), visibility 0s calc(var(--transition-duration) * 1); }
.page-transition.active { opacity: 1; visibility: visible; transition: opacity calc(var(--transition-duration) * 1) var(--easing-standard), visibility 0s; }

/* Staggered animations */
body.menu-active .menu-wrapper > a:nth-of-type(1) { transition-delay: 0.06s; }
body.menu-active .menu-wrapper > a:nth-of-type(2) { transition-delay: 0.12s; }
body.menu-active .menu-wrapper > a:nth-of-type(3) { transition-delay: 0.18s; }
body.menu-active .menu-wrapper > a:nth-of-type(4) { transition-delay: 0.24s; }
body.menu-active .side-menu-item { transition-delay: 0.30s; } /* Both side items share delay */
body.menu-active .see-more { transition-delay: 0.36s; }

/* Reverse staggered animations */
body.menu-active.closing .see-more { transition-delay: 0s; }
body.menu-active.closing .side-menu-item { transition-delay: 0.04s; }
body.menu-active.closing .menu-wrapper > a:nth-of-type(4) { transition-delay: 0.08s; }
body.menu-active.closing .menu-wrapper > a:nth-of-type(3) { transition-delay: 0.12s; }
body.menu-active.closing .menu-wrapper > a:nth-of-type(2) { transition-delay: 0.16s; }
body.menu-active.closing .menu-wrapper > a:nth-of-type(1) { transition-delay: 0.20s; }

/* Responsive */
@media (max-width: 768px) {
   :root { --circle-size: var(--circle-size-mobile); --circle-bottom-size: var(--circle-bottom-size-mobile); --indicator-line-width: 10px; --indicator-circle-size: 18px; }
   .logo, .menu-logo { top: 20px; font-size: 0.9rem;}
   .nav-circle.top-left { top: 25px; left: 25px; }
   .nav-circle.top-right { top: 25px; right: 25px; }
   .nav-circle.bottom-center { bottom: 30px; }
   .quote-container { max-width: 90%; padding: 15px;} .quote-container .quote-indicator-wrapper { margin-bottom: 20px; }
   .quote-text { font-size: 1.1rem; } .quote-attribution { font-size: 0.85rem; }
   .quote-card { width: calc(100% - 40px); max-width: 280px; padding: 12px 18px; } .quote-card-title { font-size: 0.85rem; } .quote-card-subtitle { font-size: 0.7rem; }

   /* Responsive Menu */
   .main-menu > .menu-wrapper > a:not(.side-menu-item):not(.see-more) { font-size: 1.4rem; margin: 12px 0; }
   .side-menu-item, .see-more { font-size: 0.75rem; }
   .side-menu-item.left { right: calc(50% + 130px); --slide-offset: -35px; }
   .side-menu-item.right { left: calc(50% + 130px); --slide-offset: 35px; }
   .see-more { margin-top: 35px; margin-bottom: 30px;}
   .see-more .arrow { font-size: 0.9rem; margin-top: 2px;}
}
</file>

<file path="src/utils/unifiedContent.js">
/**
 * Unified Content Loader (JSON-only, Ghost export)
 *
 * Reads all content from src/data/ghost/posts.json
 * Provides filtering, pagination, timeline, and tag utilities
 */

import fs from 'fs';
import path from 'path';

const POSTS_PATH = path.resolve(process.cwd(), 'src/data/ghost/posts.json');

// Read and parse posts.json once at module load
let posts = [];
try {
  console.log('Reading posts.json...');
  const json = fs.readFileSync(POSTS_PATH, 'utf8');
  console.log('Raw posts.json content length:', json.length);
  posts = JSON.parse(json);
  console.log('Parsed posts.json - Total posts:', posts.length);
} catch (err) {
  console.error('Error loading posts.json:', err);
  posts = [];
}

// Normalize published_at and html for consistency
posts = posts.map(post => ({
  ...post,
  published_at: post.published_at || post.pubDatetime,
  html: post.html || post.mob_html || post.lex_html || ''
}));

/**
 * Get all blog posts from both Markdown and Ghost
 * @returns {Promise<Array>} Combined array of blog posts
 */
export function getAllPosts() {
  // Return all posts, sorted by published_at (newest first)
  return posts.slice().sort((a, b) => new Date(b.published_at) - new Date(a.published_at));
}

/**
 * Find a post by slug from either content source
 * @param {string} slug - Post slug to find
 * @returns {Promise<Object|null>} The post object or null if not found
 */
export function getPostBySlug(slug) {
  return posts.find(post => post.slug === slug);
}

/**
 * Get all unique tags from both Markdown and Ghost posts
 * @returns {Promise<Array>} Array of unique tag names
 */
export function getAllUniqueTags() {
  console.log('Getting all unique tags...');

  // Extract tag names, handling both string tags and object tags with name property
  const allTags = posts.flatMap(post => {
    if (!Array.isArray(post.tags)) return [];

    return post.tags.map(tag => {
      // Handle Ghost format where tags are objects with a name property
      if (typeof tag === 'object' && tag !== null && tag.name) {
        console.log(`Found tag object with name: ${tag.name}`);
        return tag.name;
      }
      // Handle string tags
      if (typeof tag === 'string') {
        return tag;
      }
      // Skip invalid tags
      console.log(`Skipping invalid tag:`, tag);
      return null;
    }).filter(Boolean); // Remove null/undefined values
  });

  // Convert to lowercase and get unique values
  const uniqueTags = [...new Set(allTags.map(tag => tag.toLowerCase()))];
  console.log(`Found ${uniqueTags.length} unique tags:`, uniqueTags);
  return uniqueTags;
}

/**
 * Get all posts with a specific tag
 * @param {string} tagSlug - Tag slug to filter by
 * @returns {Promise<Array>} Array of posts with the specified tag
 */
export function getPostsByTag(tagSlug) {
  console.log(`DEBUG: getPostsByTag called for slug: "${tagSlug}". Processing ${posts?.length || 0} total posts.`);
  const normalizedTagSlug = tagSlug.toLowerCase();

  const filtered = posts.filter(post => {
    if (!Array.isArray(post.tags)) {
      return false;
    }

    // Normalize all post tag slugs
    const postTagSlugs = post.tags.map(t =>
      typeof t === 'object' && t !== null && t.slug ? t.slug.toLowerCase() :
      typeof t === 'object' && t !== null && t.name ? t.name.toLowerCase() :
      typeof t === 'string' ? t.toLowerCase() : ''
    ).filter(Boolean);

    const hasTag = postTagSlugs.includes(normalizedTagSlug);
    console.log(`DEBUG (${tagSlug}): Checking post "${post.title}". Has slugs: [${postTagSlugs.join(', ')}]. Match found: ${hasTag}`);
    return hasTag;
  });

  console.log(`DEBUG: getPostsByTag for slug: "${tagSlug}" returning ${filtered.length} posts.`);
  return filtered;
}

/**
 * Determine content type based on first tag
 * @param {Object} post - Post object
 * @returns {string} Content type ('blog', 'archive', or 'work')
 */
export function getContentType(post) {
  console.log(`Determining content type for post: "${post.title}"`);

  if (post.type && typeof post.type === 'string') {
    const type = post.type.toLowerCase();
    console.log(`  Post has explicit type: ${type}`);
    if (["archive", "work", "blog"].includes(type)) {
      console.log(`  Using explicit type: ${type}`);
      return type;
    }
  }

  if (Array.isArray(post.tags)) {
    // Extract tag names/slugs, handling both string tags and object tags
    const tagValues = post.tags.map(t => {
      if (typeof t === 'object' && t !== null) {
        // Prefer slug if available, otherwise use name
        return (t.slug || t.name || '').toLowerCase();
      }
      return typeof t === 'string' ? t.toLowerCase() : '';
    }).filter(Boolean); // Remove empty strings

    console.log(`  Post has tag values: ${JSON.stringify(tagValues)}`);

    if (tagValues.includes("archive")) {
      console.log(`  Using tag-based type: archive`);
      return "archive";
    }
    if (tagValues.includes("work") || tagValues.includes("project")) {
      console.log(`  Using tag-based type: work`);
      return "work";
    }
    if (tagValues.includes("blog")) {
      console.log(`  Using tag-based type: blog`);
      return "blog";
    }
  }

  console.log(`  No type or relevant tags found, defaulting to: blog`);
  return "blog";
}

/**
 * Get posts by content type
 * @param {string} type - Content type ('blog', 'archive', or 'work')
 * @returns {Promise<Array>} Filtered posts
 */
export function getPostsByType(type) {
  const allPosts = getAllPosts();
  console.log(`getPostsByType called for type: ${type}, processing ${allPosts.length} total posts`);

  // Debug the structure of the first post to see what we're working with
  if (allPosts.length > 0) {
    console.log('First post structure:', JSON.stringify({
      title: allPosts[0].title,
      slug: allPosts[0].slug,
      type: allPosts[0].type,
      tags: allPosts[0].tags,
      featured: allPosts[0].featured
    }, null, 2));
  }

  const filteredPosts = allPosts.filter(post => {
    const contentType = getContentType(post);
    console.log(`Post "${post.title}" has content type: ${contentType}`);
    return contentType === type;
  });

  console.log(`getPostsByType found ${filteredPosts.length} posts for type: ${type}`);
  return filteredPosts;
}

/**
 * Paginate a list of posts
 * @param {Array} posts - Array of posts to paginate
 * @param {number} currentPage - Current page number (1-based)
 * @param {number} postsPerPage - Number of posts per page
 * @returns {Object} Paginated posts and pagination info
 */
export function paginatePosts(posts, page = 1, pageSize = 5) {
  const totalPosts = posts.length;
  const totalPages = Math.max(1, Math.ceil(totalPosts / pageSize));
  const currentPage = Math.max(1, Math.min(page, totalPages));
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  return {
    posts: posts.slice(startIndex, endIndex),
    pagination: {
      currentPage,
      totalPages,
      totalPosts,
      pageSize,
      hasNextPage: currentPage < totalPages,
      hasPrevPage: currentPage > 1,
      nextPage: currentPage < totalPages ? currentPage + 1 : null,
      prevPage: currentPage > 1 ? currentPage - 1 : null
    }
  };
}

/**
 * Group posts into a timeline by year and month (newest first)
 * @param {Array} posts
 * @returns {Object} { [year]: { [month]: [posts] } }
 */
export function getTimeline(posts) {
  // Sort posts by published_at (newest first)
  const sorted = posts.slice().sort((a, b) => new Date(b.published_at) - new Date(a.published_at));
  const timeline = {};
  for (const post of sorted) {
    const date = new Date(post.published_at);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    if (!timeline[year]) timeline[year] = {};
    if (!timeline[year][month]) timeline[year][month] = [];
    timeline[year][month].push(post);
  }
  return timeline;
}

// Utility to slugify strings for tags and URLs
export function slugifyStr(str) {
  return String(str)
    .trim()
    .toLowerCase()
    .replace(/\s+/g, '-')       // Replace spaces with -
    .replace(/[^a-z0-9-\-]/g, '') // Remove invalid chars
    .replace(/-+/g, '-')          // Collapse multiple -
    .replace(/^-+|-+$/g, '');     // Trim - from ends
}
</file>

<file path="package.json">
{
  "name": "pvb-astro",
  "type": "module",
  "version": "0.0.1",
  "scripts": {
    "dev": "astro dev",
    "start": "astro dev",
    "build": "astro build && pagefind --site dist || echo 'Build completed with warnings'",
    "preview": "astro preview",
    "astro": "astro"
  },
  "dependencies": {
    "@astrojs/image": "^0.18.0",
    "@astrojs/react": "^4.2.4",
    "@astrojs/rss": "^4.0.5",
    "@types/react": "^19.1.2",
    "@types/react-dom": "^19.1.2",
    "astro": "^4.5.5",
    "dayjs": "^1.11.10",
    "gray-matter": "^4.0.3",
    "react": "^19.1.0",
    "react-dom": "^19.1.0"
  },
  "devDependencies": {
    "@astrojs/sitemap": "^3.3.0",
    "@pagefind/default-ui": "^1.0.4",
    "pagefind": "^1.0.4"
  }
}
</file>

<file path="src/pages/blog/[slug].astro">
---
import { getAllPosts, getPostBySlug } from "../../utils/unifiedContent.js";
import BlogPost from "../../layouts/BlogPost.astro";
import Layout from "../../layouts/Layout.astro";

export async function getStaticPaths() {
  // Use the new JSON utility to get all posts
  const allPosts = await getAllPosts();
  if (allPosts && allPosts.length > 0) {
    return allPosts.map(post => ({
      params: { slug: post.slug },
    }));
  }
  // Fallback
  return [
    {
      params: { slug: "hello-world" },
    }
  ];
}

const { slug } = Astro.params;
let post = null;
let errorState = false;

try {
  post = await getPostBySlug(slug);
  if (!post) errorState = true;
} catch (e) {
  errorState = true;
}

---

{errorState ? (
  <Layout
      title="Blog Post Not Found"
      description="Sorry, the blog post you're looking for is not available."
      isHomePage={false}
      accentColor="#f0f0f0"
      bgColor="rgba(20, 20, 20, 0.9)"
      backgroundImageUrl="/images/blackgranite.png"
      bodyDataPage="blog-post"
    >
    <div class="error-container">
      <h1>Blog Post Not Found</h1>
      <p>Sorry, the blog post you're looking for is not available.</p>
      <a href="/blog" class="return-link">Return to Blog</a>
    </div>

    <style>
      .error-container {
        max-width: 650px;
        margin: 130px auto 100px;
        padding: 0 20px;
        text-align: center;
      }

      h1 {
        font-size: 2rem;
        margin-bottom: 1rem;
        font-family: 'Georgia Custom', Georgia, serif;
        color: rgba(240, 240, 240, 0.9);
      }

      p {
        font-size: 1.1rem;
        margin-bottom: 2rem;
        font-family: 'Georgia Custom', Georgia, serif;
        color: rgba(240, 240, 240, 0.8);
      }

      .return-link {
        display: inline-block;
        padding: 0.5rem 1rem;
        background-color: #e0e0e0;
        color: #222222;
        text-decoration: none;
        border-radius: 4px;
        font-family: 'Georgia Custom', Georgia, serif;
        transition: background-color 0.3s ease;
      }

      .return-link:hover {
        background-color: #cccccc;
      }
    </style>
  </Layout>
) : (
  <BlogPost post={post} />
)}
</file>

<file path="src/pages/blog/timeline.astro">
---
import { getPostsByType } from "../../utils/unifiedContent";
import Layout from "../../layouts/Layout.astro";

// Fetch posts at render time
let postsByYear: Record<string, any[]> = {};
let sortedYears: string[] = [];
let error = false;

try {
  // Get all archive posts (posts with 'archive' as first tag)
  const archivePosts = await getPostsByType('archive');

  // Group posts by year
  postsByYear = archivePosts.reduce((acc: Record<string, any[]>, post) => {
    const pubDate = post.data?.pubDatetime || post.published_at;
    const year = new Date(pubDate).getFullYear().toString();
    if (!acc[year]) {
      acc[year] = [];
    }
    acc[year].push(post);
    return acc;
  }, {});

  // Sort years in descending order (newest first)
  sortedYears = Object.keys(postsByYear).sort((a, b) => parseInt(b) - parseInt(a));

  // Sort posts within each year by date (newest first)
  for (const year of sortedYears) {
    postsByYear[year].sort((a, b) => {
      const dateA = new Date(a.data?.pubDatetime || a.published_at).getTime();
      const dateB = new Date(b.data?.pubDatetime || b.published_at).getTime();
      return dateB - dateA;
    });
  }
} catch (e) {
  console.error("Error fetching posts:", e);
  error = true;
}
---

{error ? (
  <Layout
    pageTitle="Archive Error | Blog | PVB"
    isHomePage={false}
    accentColor="#f0f0f0"
    bgColor="rgba(10, 10, 10, 0.94)"
    backgroundImageUrl="/images/blackgranite.png"
    bodyDataPage="blog"
  >
    <div class="error-container">
      <h1>Error Loading Archive</h1>
      <p>Sorry, there was an error loading the blog archive.</p>
      <a href="/blog" class="back-to-blog">&larr; back to blog</a>
    </div>

    <style>
      .error-container {
        max-width: 600px;
        margin: 100px auto;
        text-align: center;
        padding: 0 20px;
      }

      h1 {
        font-size: 1.8rem;
        color: rgba(240, 240, 240, 0.9);
        margin-bottom: 20px;
        font-family: 'Georgia Custom', Georgia, serif;
      }

      p {
        font-size: 1.1rem;
        color: rgba(220, 220, 220, 0.8);
        margin-bottom: 30px;
        font-family: 'Georgia Custom', Georgia, serif;
      }

      .back-to-blog {
        font-family: 'Georgia Custom', Georgia, serif;
        font-size: 0.95rem;
        color: rgba(200, 200, 200, 0.8);
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-block;
        padding: 0.5rem 1rem;
        border: 1px solid rgba(200, 200, 200, 0.3);
        border-radius: 3px;
      }

      .back-to-blog:hover {
        color: rgba(240, 240, 240, 1);
        border-color: rgba(240, 240, 240, 0.5);
        background-color: rgba(255, 255, 255, 0.05);
        transform: translateX(-3px);
      }
    </style>
  </Layout>
) : (
  <Layout
    pageTitle="Archive | PVB"
    isHomePage={false}
    accentColor="#f0f0f0"
    bgColor="rgba(10, 10, 10, 0.94)"
    backgroundImageUrl="/images/blackgranite.png"
    bodyDataPage="blog"
  >
    <!-- Blog title - Static (not fixed) -->
    <div class="blog-header">
      <div class="blog-title">archive</div>
    </div>

    <!-- Full Page Content Container -->
    <div class="timeline-container">
      <!-- Back to Blog -->
      <div class="back-section">
        <a href="/blog" class="back-to-blog">
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M19 12H5"></path>
            <path d="M12 19l-7-7 7-7"></path>
          </svg>
          <span>back to blog</span>
        </a>
      </div>

      <!-- Timeline Content -->
      <div class="timeline-content">
        {sortedYears.map(year => (
          <div class="year-section">
            <h2 class="year-heading">{year}</h2>
            <div class="year-posts">
              {postsByYear[year].map(post => (
                <div class="timeline-post">
                  <div class="post-date">
                    {new Date(post.data?.pubDatetime || post.published_at).toLocaleDateString('en-US', {month: 'short', day: 'numeric'})}
                  </div>
                  <h3 class="post-title">
                    <a href={`/blog/${post.slug}`}>{post.data?.title || post.title}</a>
                  </h3>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>

    <style>
      /* Global Scrollbar Styling */
      :global(html) {
        scrollbar-width: thin;
        scrollbar-color: rgba(0, 0, 0, 0.4) transparent;
      }

      :global(::-webkit-scrollbar) {
        width: 8px;
        height: 8px;
      }

      :global(::-webkit-scrollbar-track) {
        background: transparent;
      }

      :global(::-webkit-scrollbar-thumb) {
        background-color: rgba(100, 100, 100, 0.4);
        border-radius: 4px;
      }

      :global(::-webkit-scrollbar-thumb:hover) {
        background-color: rgba(120, 120, 120, 0.6);
      }

      /* Blog Header - Static (not fixed) */
      .blog-header {
        width: 100%;
        display: flex;
        justify-content: center;
        margin: 55px 0 30px;
      }

      .blog-title {
        font-family: 'Georgia Custom', Georgia, serif;
        font-size: 1.5rem;
        color: rgba(240, 240, 240, 0.9);
        letter-spacing: -0.01em;
        position: relative;
      }

      /* Add subtle underline to blog title */
      .blog-title::after {
        content: '';
        position: absolute;
        bottom: -8px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 1px;
        background-color: rgba(240, 240, 240, 0.4);
      }

      /* Timeline Container */
      .timeline-container {
        max-width: 800px;
        margin: 0 auto 60px;
        padding: 0 30px;
      }

      /* Back to Blog Link */
      .back-section {
        margin-bottom: 40px;
      }

      .back-to-blog {
        font-family: 'Georgia Custom', Georgia, serif;
        font-size: 0.95rem;
        color: rgba(200, 200, 200, 0.8);
        text-decoration: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 6px;
        width: fit-content;
        padding: 0.5rem 1rem;
        border: 1px solid rgba(200, 200, 200, 0.3);
        border-radius: 3px;
      }

      .back-to-blog:hover {
        color: rgba(240, 240, 240, 1);
        border-color: rgba(240, 240, 240, 0.5);
        background-color: rgba(255, 255, 255, 0.05);
        transform: translateX(-3px);
      }

      /* Year Sections */
      .year-section {
        margin-bottom: 50px;
      }

      .year-heading {
        font-family: 'Georgia Custom', Georgia, serif;
        font-size: 1.8rem;
        color: rgba(240, 240, 240, 0.95);
        margin-bottom: 20px;
        font-weight: normal;
        position: relative;
        padding-bottom: 10px;
      }

      .year-heading::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 60px;
        height: 1px;
        background-color: rgba(200, 200, 200, 0.3);
      }

      /* Post Items */
      .timeline-post {
        margin-bottom: 25px;
        display: flex;
        flex-direction: column;
      }

      .post-date {
        font-family: 'Georgia Custom', Georgia, serif;
        font-size: 0.9rem;
        color: rgba(180, 180, 180, 0.75);
        margin-bottom: 5px;
      }

      .post-title {
        font-family: 'Georgia Custom', Georgia, serif;
        font-size: 1.2rem;
        font-weight: normal;
        margin: 0;
      }

      .post-title a {
        color: rgba(220, 220, 220, 0.9);
        text-decoration: none;
        transition: color 0.3s ease;
      }

      .post-title a:hover {
        color: rgba(255, 255, 255, 1);
        text-decoration: underline;
        text-underline-offset: 3px;
        text-decoration-color: rgba(200, 200, 200, 0.4);
      }

      /* Responsive Styles */
      @media (max-width: 768px) {
        .blog-title {
          font-size: 1.3rem;
        }

        .timeline-container {
          padding: 0 20px;
        }

        .year-heading {
          font-size: 1.6rem;
        }

        .post-title {
          font-size: 1.1rem;
        }
      }
    </style>
  </Layout>
)}
</file>

<file path="src/pages/fitness.astro">
---
import Layout from "../layouts/Layout.astro";

// Define transformation images array 
const transformationImages = [
  { year: 2017, src: "/images/fit2017.jpg", alt: "Fitness transformation 2017" },
  { year: 2018, src: "/images/fit2018.jpg", alt: "Fitness transformation 2018" },
  { year: 2019, src: "/images/fit2019.jpg", alt: "Fitness transformation 2019" },
  { year: 2020, src: "/images/fit2020.jpeg", alt: "Fitness transformation 2020" },
  { year: 2021, src: "/images/fit2021.jpeg", alt: "Fitness transformation 2021" },
  { year: 2022, src: "/images/fit2022.2.jpeg", alt: "Fitness transformation 2022" },
  { year: 2023, src: "/images/fit2023.1.jpeg", alt: "Fitness transformation 2023" },
  { year: 2024, src: "/images/fit2024.1.jpeg", alt: "Fitness transformation 2024" },
  { year: 2025, src: "/images/fit2025.png", alt: "Fitness transformation 2025" }
];

// Performance data
const performanceData = {
  personalRecords: [
    { exercise: "Bench Press", weight: "120kg", note: "3-rep max" },
    { exercise: "Bulgarian Split Squat", weight: "60kg", note: "each leg" },
    { exercise: "Barbell Row", weight: "115kg", note: "5-rep max" }
  ],
  statistics: [
    { label: "Years Training", value: "7+" },
    { label: "Total Sessions", value: "4,500+" },
    { label: "Weekly Frequency", value: "4.8" },
    { label: "Total Hours", value: "9,000+" }
  ]
};

// Philosophy sections
const philosophySections = [
  {
    title: "Consistent Progression",
    content: "Physical development occurs not through sporadic intensity but through sustained, incremental progress. My approach centers on data-driven progression: each workout builds upon the previous, with meticulously tracked performance guiding adjustments to stimulate continued adaptation."
  },
  {
    title: "Empirical Methodology",
    content: "Every repetition, every set, every session exists as a data point in a seven-year experiment. This comprehensive record reveals patterns in recovery, adaptation, and performance that inform my training protocol—transforming fitness from subjective effort into an objective practice."
  },
  {
    title: "Recovery Architecture",
    content: "Training creates the stimulus; recovery enables the transformation. I've systematically optimized sleep quality, nutritional timing, and stress management to create the physiological conditions where adaptation can occur without accumulating systemic fatigue or reaching plateaus."
  }
];

// Workout routines
const workoutRoutines = [
  { 
    name: "Chest & Shoulders", 
    href: "https://hevy.com/routine/oD9TKyY25F7" 
  },
  { 
    name: "Back", 
    href: "https://hevy.com/routine/PSvJCDxdTAS" 
  },
  { 
    name: "Arms", 
    href: "https://hevy.com/routine/FBu1xtqJsaW" 
  },
  { 
    name: "Legs", 
    href: "https://hevy.com/routine/X9WG47EEhWT" 
  }
];
---

<Layout
  pageTitle="Physical Practice | PVB"
  isHomePage={false}
  accentColor="#1a1a1a"
  bgColor="rgba(235, 230, 225, 0.85)"
  backgroundImageUrl="/images/emperadormarble.png"
  bodyDataPage="fitness"
>
  <!-- Google Fonts for Cinzel and Crimson Pro -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@600;700&family=Crimson+Pro:wght@400;500&display=swap" rel="stylesheet">

  <div class="fitness-header">
    <h1 class="fitness-title">PHYSICAL PRACTICE</h1>
  </div>

  <div class="content-container">
    <!-- Transformation Showcase - Immediate Visual Impact -->
    <section class="transformation-section" data-animate="fade-in">
      <div class="section-header">
        <div class="section-line"></div>
        <h2 class="section-title">TRANSFORMATION</h2>
        <div class="section-line"></div>
      </div>
      
      <div class="transformation-gallery">
        <div class="current-image-container">
          <div class="image-controls">
            <button class="control-button prev-button" id="prev-year">
              <span class="control-arrow">←</span>
            </button>
            <div class="year-display" id="current-year">2017</div>
            <button class="control-button next-button" id="next-year">
              <span class="control-arrow">→</span>
            </button>
          </div>
          
          <div class="current-image">
            <!-- Images will transition here via JS -->
            {transformationImages.map((image, index) => (
              <img
                src={image.src}
                alt={image.alt}
                class={`transformation-img ${index === 0 ? 'active' : ''}`}
                data-year={image.year}
                data-index={index}
              />
            ))}
          </div>
        </div>
        
        <div class="year-timeline">
          {transformationImages.map((image, index) => (
            <button 
              class={`year-marker ${index === 0 ? "active" : ""}`} 
              data-index={index}
              data-year={image.year}

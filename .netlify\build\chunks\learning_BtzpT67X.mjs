import { a as createComponent, m as maybeRenderHead, u as unescapeHTML, r as renderTemplate } from './astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import 'clsx';

const html = "";

				const frontmatter = {"text":"The purpose of knowledge is action, not more knowledge.","author":"Pruthvi Bhat","linkedPage":"/blog/knowledge-and-action","cardTitle":"Applied Learning","cardSubtitle":"Insights into meaningful action","featured":true,"tags":["learning","action"]};
				const file = "C:/Users/<USER>/Desktop/pvb-astro/src/content/quotes/learning.md";
				const url = undefined;
				function rawContent() {
					return "";
				}
				function compiledContent() {
					return html;
				}
				function getHeadings() {
					return [];
				}

				const Content = createComponent((result, _props, slots) => {
					const { layout, ...content } = frontmatter;
					content.file = file;
					content.url = url;

					return renderTemplate`${maybeRenderHead()}${unescapeHTML(html)}`;
				});

export { Content, compiledContent, Content as default, file, frontmatter, getHeadings, rawContent, url };

/**
 * Ghost Content Integration
 * 
 * Treats local JSON files as if they were direct Ghost API responses
 */

import fs from 'fs';
import path from 'path';

// Path to Ghost JSON files
const CONTENT_DIR = path.join(process.cwd(), 'src/content');

/**
 * Load all posts from the local Ghost JSON
 * @returns {Array} Posts array
 */
export function getPosts() {
  try {
    const filePath = path.join(CONTENT_DIR, 'posts.json');
    if (!fs.existsSync(filePath)) {
      console.warn('posts.json not found');
      return [];
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    const data = JSON.parse(content);
    return data.posts || [];
  } catch (error) {
    console.error('Error loading posts:', error);
    return [];
  }
}

/**
 * Get a single post by slug
 * @param {string} slug Post slug
 * @returns {Object|null} Post object or null if not found
 */
export function getPostBySlug(slug) {
  const posts = getPosts();
  return posts.find(post => post.slug === slug) || null;
}

/**
 * Get posts with a specific tag
 * @param {string} tagName Tag name
 * @returns {Array} Filtered posts
 */
export function getPostsByTag(tagName) {
  const posts = getPosts();
  
  // Convert tag name to lowercase for case-insensitive comparison
  const normalizedTagName = tagName.toLowerCase();
  
  return posts.filter(post => 
    (post.tags || []).some(tag => tag.name.toLowerCase() === normalizedTagName)
  );
}

/**
 * Get all tags from posts
 * @returns {Array} Array of unique tags
 */
export function getAllTags() {
  try {
    const filePath = path.join(CONTENT_DIR, 'tags.json');
    if (!fs.existsSync(filePath)) {
      console.warn('tags.json not found');
      return [];
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    const data = JSON.parse(content);
    return data.tags || [];
  } catch (error) {
    console.error('Error loading tags:', error);
    return [];
  }
}

/**
 * Get posts by content type based on first tag
 * @param {string} type Content type ('blog', 'archive', 'work')
 * @returns {Array} Filtered posts
 */
export function getPostsByType(type) {
  const posts = getPosts();
  
  return posts.filter(post => {
    // If no tags, default to 'blog'
    if (!post.tags || post.tags.length === 0) {
      return type === 'blog';
    }
    
    // Check first tag (case insensitive)
    const firstTagName = post.tags[0].name.toLowerCase();
    
    if (type === 'archive' && firstTagName === 'archive') {
      return true;
    }
    
    if (type === 'work' && (firstTagName === 'work' || firstTagName === 'project')) {
      return true;
    }
    
    // Default to blog for anything else
    return type === 'blog' && firstTagName !== 'archive' && 
           firstTagName !== 'work' && firstTagName !== 'project';
  });
}

/**
 * Get featured posts
 * @param {string} type Optional content type filter
 * @returns {Array} Featured posts
 */
export function getFeaturedPosts(type) {
  const posts = type ? getPostsByType(type) : getPosts();
  return posts.filter(post => post.featured);
}

/**
 * Get related posts based on tags
 * @param {Object} post Current post
 * @param {number} limit Maximum number of posts to return
 * @returns {Array} Related posts
 */
export function getRelatedPosts(post, limit = 3) {
  if (!post || !post.tags || post.tags.length === 0) {
    return [];
  }
  
  const allPosts = getPosts();
  const otherPosts = allPosts.filter(p => p.id !== post.id);
  
  // Compute tag similarity score
  const scoredPosts = otherPosts.map(otherPost => {
    // Get tag names for easy comparison
    const postTags = post.tags.map(t => t.name.toLowerCase());
    const otherTags = (otherPost.tags || []).map(t => t.name.toLowerCase());
    
    // Count matching tags
    let score = 0;
    for (const tag of otherTags) {
      if (postTags.includes(tag)) {
        score++;
      }
    }
    
    return { post: otherPost, score };
  });
  
  // Sort by score (highest first) and date (newest first if same score)
  return scoredPosts
    .filter(item => item.score > 0)
    .sort((a, b) => {
      // Sort by score first
      if (b.score !== a.score) {
        return b.score - a.score;
      }
      
      // If same score, sort by date
      return new Date(b.post.published_at) - new Date(a.post.published_at);
    })
    .slice(0, limit)
    .map(item => item.post);
}

/**
 * Group posts by year for timeline view
 * @returns {Object} Posts grouped by year
 */
export function getPostsByYear(type = 'archive') {
  const posts = getPostsByType(type);
  const postsByYear = {};
  
  // Group posts by year
  for (const post of posts) {
    const year = new Date(post.published_at).getFullYear().toString();
    
    if (!postsByYear[year]) {
      postsByYear[year] = [];
    }
    
    postsByYear[year].push(post);
  }
  
  // Sort posts within each year (newest first)
  for (const year in postsByYear) {
    postsByYear[year].sort((a, b) => 
      new Date(b.published_at) - new Date(a.published_at)
    );
  }
  
  return postsByYear;
}

/**
 * Paginate posts
 * @param {Array} posts Posts to paginate
 * @param {number} page Page number (1-based)
 * @param {number} perPage Posts per page
 * @returns {Object} Pagination result with posts and metadata
 */
export function paginatePosts(posts, page = 1, perPage = 5) {
  const totalPosts = posts.length;
  const totalPages = Math.ceil(totalPosts / perPage);
  
  // Ensure valid page number
  page = Math.max(1, Math.min(page, totalPages || 1));
  
  const startIndex = (page - 1) * perPage;
  const endIndex = Math.min(startIndex + perPage, totalPosts);
  
  return {
    posts: posts.slice(startIndex, endIndex),
    pagination: {
      page,
      perPage,
      totalPages,
      totalPosts,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1,
      nextPage: page < totalPages ? page + 1 : null,
      prevPage: page > 1 ? page - 1 : null
    }
  };
}

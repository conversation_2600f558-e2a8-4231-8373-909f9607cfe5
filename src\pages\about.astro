---
import Layout from '../layouts/Layout.astro';
import { Image } from 'astro:assets';
// import profileImage from '../assets/profile.jpg'; // Uncomment when you add the image
---

<Layout
    pageTitle="About | PVB"
    isHomePage={false}
    accentColor="#3a2c23" 
    bgColor="#3a2c23" 
    backgroundImageUrl="/images/limestone.png" 
    bodyDataPage="about"
>
    <main class="about-main">
        <header class="about-header">
            <div class="profile-section">
                <div class="profile-image-container">
                    <!-- Uncomment when you add your profile image -->
                    <!-- <Image 
                        src={profileImage} 
                        alt="Profile picture" 
                        class="profile-image"
                        width={120}
                        height={120}
                        quality="high"
                    /> -->
                    <div class="profile-placeholder">
                        <span class="profile-initials">PVB</span>
                    </div>
                </div>
                <div class="profile-text">
                    <h1 class="about-title">About</h1>
                    <p class="about-tagline">Writer, researcher, and curious mind exploring the intersections of technology, philosophy, and human experience.</p>
                </div>
            </div>
        </header>

        <div class="content-sections">
            <section class="about-section intro-section">
                <h2 class="section-title">Introduction</h2>
                <div class="section-content">
                    <p class="lead-paragraph">
                        Welcome to my corner of the internet. This space serves as a repository for thoughts, research, and explorations into the ideas that shape our world.
                    </p>
                    <p>
                        I believe in the power of clear thinking, careful observation, and the written word to illuminate complex ideas. Whether diving into technical subjects or pondering philosophical questions, my approach centers on curiosity and precision.
                    </p>
                </div>
            </section>

            <section class="about-section philosophy-section">
                <h2 class="section-title">Philosophy</h2>
                <div class="section-content">
                    <p>
                        My work is guided by a few core principles: intellectual honesty, the importance of primary sources, and the belief that complex ideas can be made accessible without losing their depth.
                    </p>
                    <p>
                        I'm particularly interested in how technology shapes human behavior, the evolution of ideas across disciplines, and the art of clear communication in an increasingly complex world.
                    </p>
                </div>
            </section>

            <section class="about-section current-section">
                <h2 class="section-title">Currently Focused On</h2>
                <div class="section-content">
                    <ul class="focus-list">
                        <li>Exploring the intersection of AI and human creativity</li>
                        <li>Researching historical patterns in technological adoption</li>
                        <li>Writing about the philosophy of knowledge work</li>
                        <li>Building tools for better thinking and writing</li>
                    </ul>
                </div>
            </section>

            <section class="about-section connect-section">
                <h2 class="section-title">Connect</h2>
                <div class="section-content">
                    <p>
                        I'm always interested in thoughtful conversation and collaboration. Feel free to reach out if you'd like to discuss ideas, share feedback, or explore potential projects.
                    </p>
                    <div class="contact-links">
                        <a href="mailto:<EMAIL>" class="contact-link">
                            <span class="link-label">Email</span>
                            <span class="link-address"><EMAIL></span>
                        </a>
                        <a href="https://twitter.com/example" class="contact-link" target="_blank" rel="noopener noreferrer">
                            <span class="link-label">Twitter</span>
                            <span class="link-address">@example</span>
                        </a>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <script is:inline>
      document.addEventListener('DOMContentLoaded', function() {
        const backButton = document.querySelector('.nav-circle.top-left');
        if (backButton) {
          backButton.addEventListener('click', () => {
            window.history.back();
          });
        }
      });
    </script>
</Layout>

<style>
    .about-main {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem 2rem 4rem;
        animation: fade-in 0.6s ease-out;
    }

    .about-header {
        margin-bottom: 4rem;
    }

    .profile-section {
        display: flex;
        align-items: flex-start;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .profile-image-container {
        flex-shrink: 0;
    }

    .profile-image {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .profile-placeholder {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--theme-accent, #3a2c23), #2c2018);
        display: flex;
        align-items: center;
        justify-content: center;
        border: 3px solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .profile-initials {
        font-size: 2rem;
        font-weight: 700;
        color: white;
        letter-spacing: -0.05em;
    }

    .profile-text {
        flex: 1;
        min-width: 0;
    }

    .about-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0 0 1rem 0;
        color: var(--theme-text, #2c2c2c);
        letter-spacing: -0.025em;
    }

    .about-tagline {
        font-size: 1.25rem;
        color: var(--theme-text-light, #666);
        margin: 0;
        line-height: 1.5;
        font-style: italic;
    }

    .content-sections {
        display: flex;
        flex-direction: column;
        gap: 3rem;
    }

    .about-section {
        opacity: 0;
        animation: fade-in-up 0.6s ease-out forwards;
    }

    .about-section:nth-child(1) { animation-delay: 0.1s; }
    .about-section:nth-child(2) { animation-delay: 0.2s; }
    .about-section:nth-child(3) { animation-delay: 0.3s; }
    .about-section:nth-child(4) { animation-delay: 0.4s; }

    .section-title {
        font-size: 1.5rem;
        font-weight: 600;
        margin: 0 0 1.5rem 0;
        color: var(--theme-text, #2c2c2c);
        letter-spacing: -0.02em;
        position: relative;
        padding-bottom: 0.75rem;
    }

    .section-title::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 40px;
        height: 2px;
        background: var(--theme-accent, #3a2c23);
        border-radius: 1px;
    }

    .section-content {
        color: var(--theme-text, #2c2c2c);
        line-height: 1.7;
    }

    .section-content p {
        margin: 0 0 1.5rem 0;
        font-size: 1.125rem;
    }

    .section-content p:last-child {
        margin-bottom: 0;
    }

    .lead-paragraph {
        font-size: 1.25rem !important;
        font-weight: 500;
        color: var(--theme-text, #2c2c2c);
    }

    .focus-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .focus-list li {
        padding: 0.75rem 0;
        position: relative;
        padding-left: 1.5rem;
        font-size: 1.125rem;
    }

    .focus-list li::before {
        content: '→';
        position: absolute;
        left: 0;
        color: var(--theme-accent, #3a2c23);
        font-weight: 600;
    }

    .contact-links {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-top: 2rem;
    }

    .contact-link {
        display: flex;
        align-items: center;
        gap: 1rem;
        text-decoration: none;
        padding: 1rem 1.25rem;
        border: 1px solid rgba(20, 20, 20, 0.1);
        border-radius: 0.5rem;
        transition: all 0.2s ease-in-out;
        background: rgba(255, 255, 255, 0.5);
    }

    .contact-link:hover {
        border-color: var(--theme-accent, #3a2c23);
        background: rgba(255, 255, 255, 0.8);
        transform: translateY(-1px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    .link-label {
        font-weight: 600;
        color: var(--theme-text, #2c2c2c);
        min-width: 60px;
    }

    .link-address {
        color: var(--theme-text-light, #666);
    }

    @keyframes fade-in {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes fade-in-up {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @media (max-width: 768px) {
        .about-main {
            padding: 1.5rem 1.5rem 3rem;
        }

        .about-header {
            margin-bottom: 3rem;
        }

        .profile-section {
            flex-direction: column;
            align-items: center;
            text-align: center;
            gap: 1.5rem;
        }

        .profile-image,
        .profile-placeholder {
            width: 100px;
            height: 100px;
        }

        .profile-initials {
            font-size: 1.75rem;
        }

        .about-title {
            font-size: 2rem;
        }

        .about-tagline {
            font-size: 1.125rem;
        }

        .content-sections {
            gap: 2.5rem;
        }

        .section-content p {
            font-size: 1rem;
        }

        .lead-paragraph {
            font-size: 1.125rem !important;
        }

        .focus-list li {
            font-size: 1rem;
        }

        .contact-links {
            gap: 0.75rem;
        }

        .contact-link {
            padding: 0.875rem 1rem;
        }
    }
</style>

[build]
  command = "npm run build"
  publish = "dist"
  ignore_warnings = true

[build.environment]
  NODE_VERSION = "18"

# Asset optimization
[build.processing]
  skip_processing = false
[build.processing.css]
  bundle = true
  minify = true
[build.processing.js]
  bundle = true
  minify = true
[build.processing.html]
  pretty_urls = true
[build.processing.images]
  compress = true

# API routes should be handled properly
[[redirects]]
  from = "/api/*"
  to = "/api/:splat"
  status = 200

# Custom 404 page rather than SPA redirect
[[redirects]]
  from = "/*"
  to = "/404.html"
  status = 404

# Headers to ensure proper caching and security
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# Cache control for static assets
[[headers]]
  for = "/_astro/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/images/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/fonts/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

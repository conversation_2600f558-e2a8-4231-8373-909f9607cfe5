/**
 * Convert markdown content in src/content into JSON for Astro loader
 * Run with: node convert-md-to-json.js
 */
import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';
import { marked } from 'marked';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Directories
const projectRoot = path.resolve(__dirname);
const contentDir = path.join(projectRoot, 'src', 'content');
const ghostDataDir = path.join(projectRoot, 'src', 'data', 'ghost');

// Ensure ghost data dir exists
if (!fs.existsSync(ghostDataDir)) {
  fs.mkdirSync(ghostDataDir, { recursive: true });
}

// Convert markdown posts (blog and work)
const mdDirs = [
  { subdir: 'blog', type: 'blog' },
  { subdir: 'work', type: 'work' }
];
let posts = [];
mdDirs.forEach(({ subdir, type }) => {
  const dirPath = path.join(contentDir, subdir);
  if (fs.existsSync(dirPath)) {
    fs.readdirSync(dirPath)
      .filter(file => file.endsWith('.md'))
      .forEach(file => {
        const filepath = path.join(dirPath, file);
        const slug = path.basename(file, '.md');
        const rawContent = fs.readFileSync(filepath, 'utf8');
        const { data, content } = matter(rawContent);
        const html = marked(content);
        posts.push({ slug, type, ...data, content, html });
      });
  }
});
// Write posts.json
fs.writeFileSync(
  path.join(ghostDataDir, 'posts.json'),
  JSON.stringify(posts, null, 2)
);
console.log(`Converted ${posts.length} markdown posts to JSON at src/data/ghost/posts.json`);

// Convert quotes
const quotesDir = path.join(contentDir, 'quotes');
let quotes = [];
if (fs.existsSync(quotesDir)) {
  fs.readdirSync(quotesDir)
    .filter(file => file.endsWith('.md'))
    .forEach(file => {
      const filepath = path.join(quotesDir, file);
      const raw = fs.readFileSync(filepath, 'utf8');
      const { data } = matter(raw);
      quotes.push(data);
    });
}
// Write quotes.json
fs.writeFileSync(
  path.join(ghostDataDir, 'quotes.json'),
  JSON.stringify(quotes, null, 2)
);
console.log(`Converted ${quotes.length} quotes to JSON at src/data/ghost/quotes.json`);

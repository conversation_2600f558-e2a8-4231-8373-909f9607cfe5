import { c as createAstro, a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../../chunks/astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import { g as getCollection } from '../../chunks/_astro_content_CcaQj6Wl.mjs';
import { $ as $$BlogPost } from '../../chunks/BlogPost_DYNjvq7J.mjs';
import { $ as $$Layout } from '../../chunks/Layout_rXbp99fE.mjs';
export { renderers } from '../../renderers.mjs';

const $$Astro = createAstro("https://pvb.com");
async function getStaticPaths() {
  try {
    const posts = await getCollection("blog", ({ data }) => !data.draft);
    if (posts && posts.length > 0) {
      return posts.map((post) => ({
        params: { slug: post.slug },
        props: { post }
      }));
    }
    console.warn("No blog posts found, using fallback");
    return [
      {
        params: { slug: "hello-world" },
        props: { error: "No blog posts found" }
      }
    ];
  } catch (error) {
    console.error("Error getting blog posts for static paths:", error);
    return [
      {
        params: { slug: "hello-world" },
        props: { error: "Failed to load blog posts" }
      }
    ];
  }
}
const $$slug = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$slug;
  const { post, error } = Astro2.props;
  let Content;
  let errorState = !!error;
  if (!errorState && post) {
    try {
      const renderResult = await post.render();
      Content = renderResult.Content;
    } catch (e) {
      console.error("Error rendering post:", e);
      errorState = true;
    }
  }
  return renderTemplate`${errorState ? renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "pageTitle": "Blog Post Not Found", "isHomePage": false, "accentColor": "#f0f0f0", "bgColor": "rgba(20, 20, 20, 0.9)", "backgroundImageUrl": "/images/blackgranite.png", "bodyDataPage": "blog-post" }, { "default": async ($$result2) => renderTemplate`${maybeRenderHead()}<div class="error-container"><h1>Blog Post Not Found</h1><p>Sorry, the blog post you're looking for is not available.</p><a href="/blog" class="return-link">Return to Blog</a></div><style>
      .error-container {
        max-width: 650px;
        margin: 130px auto 100px;
        padding: 0 20px;
        text-align: center;
      }

      h1 {
        font-size: 2rem;
        margin-bottom: 1rem;
        font-family: 'Georgia Custom', Georgia, serif;
        color: rgba(240, 240, 240, 0.9);
      }

      p {
        font-size: 1.1rem;
        margin-bottom: 2rem;
        font-family: 'Georgia Custom', Georgia, serif;
        color: rgba(240, 240, 240, 0.8);
      }

      .return-link {
        display: inline-block;
        padding: 0.5rem 1rem;
        background-color: #e0e0e0;
        color: #222222;
        text-decoration: none;
        border-radius: 4px;
        font-family: 'Georgia Custom', Georgia, serif;
        transition: background-color 0.3s ease;
      }

      .return-link:hover {
        background-color: #cccccc;
      }
    </style>` })}` : renderTemplate`${renderComponent($$result, "BlogPost", $$BlogPost, { "post": post }, { "default": async ($$result2) => renderTemplate`${renderComponent($$result2, "Content", Content, {})}` })}`}`;
}, "C:/Users/<USER>/Desktop/pvb-astro/src/pages/blog/[slug].astro", void 0);

const $$file = "C:/Users/<USER>/Desktop/pvb-astro/src/pages/blog/[slug].astro";
const $$url = "/blog/[slug]";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$slug,
  file: $$file,
  getStaticPaths,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

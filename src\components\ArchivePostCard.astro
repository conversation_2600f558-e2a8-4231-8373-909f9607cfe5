---
// export interface Props {
//   post: Post;
// }

const { post } = Astro.props;
const postDate = new Date(post.published_at).toLocaleDateString('en-US', {
    month: 'short',
    day: '2-digit'
});
---

<article class="archive-post-card">
  <a href={`/blog/${post.slug}/`} class="archive-post-link">
    <time class="archive-date">{postDate}</time>
    <h3 class="archive-title">{post.title}</h3>
  </a>
</article>

<style>
  .archive-post-card {
    margin: 0;
    padding: 0;
  }
  
  .archive-post-link {
    display: flex;
    align-items: baseline;
    gap: 1.5rem;
    padding: 0.875rem 1.25rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease-in-out;
    text-decoration: none;
    color: var(--theme-text, #2c2c2c);
    border: 1px solid transparent;
    position: relative;
  }
  
  .archive-post-link:hover {
    background-color: rgba(20, 20, 20, 0.03);
    border-color: rgba(20, 20, 20, 0.08);
    transform: translateX(4px);
  }
  
  .archive-post-link:hover .archive-title {
    color: var(--theme-accent, #3a2c23);
  }
  
  .archive-date {
    font-family: var(--font-mono, 'SF Mono', 'Monaco', 'Inconsolata', monospace);
    font-size: 0.875rem;
    color: var(--theme-text-light, #666);
    font-weight: 500;
    flex-shrink: 0;
    width: 60px;
    letter-spacing: -0.025em;
  }
  
  .archive-title {
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.4;
    margin: 0;
    transition: color 0.2s ease-in-out;
    letter-spacing: -0.015em;
  }
  
  @media (max-width: 640px) {
    .archive-post-link {
      gap: 1rem;
      padding: 0.75rem 1rem;
    }
    
    .archive-date {
      width: 50px;
      font-size: 0.8rem;
    }
    
    .archive-title {
      font-size: 0.9rem;
    }
  }
</style>
import { a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../chunks/astro/server_Dba0FyIl.mjs';
import 'kleur/colors';
import { $ as $$Layout } from '../chunks/Layout_rXbp99fE.mjs';
/* empty css                                  */
export { renderers } from '../renderers.mjs';

const $$Search = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "pageTitle": "Search | PVB", "isHomePage": false, "accentColor": "#2a2a2a", "bgColor": "rgba(245, 245, 245, 0.9)", "backgroundImageUrl": "/images/whitemarble.png", "bodyDataPage": "search", "data-astro-cid-ipsxrsrh": true }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="quote-container" style="max-width: 800px;" data-astro-cid-ipsxrsrh> <h1 class="text-3xl font-bold mb-4 text-accent" data-astro-cid-ipsxrsrh>Search</h1> <p class="mb-8 text-muted" data-astro-cid-ipsxrsrh>Find posts by title, content, or tags</p> <div class="search-container" data-astro-cid-ipsxrsrh> <div id="search-box" class="mb-8" data-astro-cid-ipsxrsrh> <!-- This div will be replaced with the search UI --> <div class="search-placeholder" data-astro-cid-ipsxrsrh> <p class="text-muted" data-astro-cid-ipsxrsrh>Search functionality will be available after building the site.</p> <p class="search-note" data-astro-cid-ipsxrsrh>For local development, you need to build the site first with <code data-astro-cid-ipsxrsrh>npm run build</code></p> </div> </div> <div id="search-results" data-astro-cid-ipsxrsrh> <!-- Results will appear here --> </div> </div> <div class="mt-10" data-astro-cid-ipsxrsrh> <a href="/blog" class="back-link" data-astro-cid-ipsxrsrh> <span class="back-arrow" data-astro-cid-ipsxrsrh>←</span> <span data-astro-cid-ipsxrsrh>Back to blog</span> </a> </div> </div> ` })}  `;
}, "C:/Users/<USER>/Desktop/pvb-astro/src/pages/search.astro", void 0);

const $$file = "C:/Users/<USER>/Desktop/pvb-astro/src/pages/search.astro";
const $$url = "/search";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Search,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

{"$ref": "#/definitions/quotes", "definitions": {"quotes": {"type": "object", "properties": {"text": {"type": "string"}, "author": {"type": "string", "default": "PVB"}, "linkedPage": {"type": "string"}, "cardTitle": {"type": "string"}, "cardSubtitle": {"type": "string"}, "featured": {"type": "boolean", "default": false}, "tags": {"type": "array", "items": {"type": "string"}, "default": []}, "$schema": {"type": "string"}}, "required": ["text", "cardTitle", "cardSubtitle"], "additionalProperties": false}}, "$schema": "http://json-schema.org/draft-07/schema#"}
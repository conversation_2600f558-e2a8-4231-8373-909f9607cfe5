// import { slugifyStr } from "../utils/slugify";
import Tag from "./Tag.astro";

// export interface Props { project: any; variant?: "featured" | "standard"; }
const { project, variant = "standard" } = Astro.props;
const title = project.title;
const description = project.excerpt || project.custom_excerpt || '';
const tags = project.tags?.map(tag => typeof tag === 'string' ? { slug: tag, name: tag } : tag) || [];
const projectDate = project.published_at;
const status = project.status || '';
const repoUrl = project.repo_url || project.repoUrl || '';
const liveUrl = project.live_url || project.liveUrl || '';

function formatDate(dateString) {
  if (!dateString) return '';
  return new Date(dateString).toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'short' 
  });
}

const formattedDate = formatDate(projectDate);
const isFeatured = variant === "featured";
const internalTagSlugs = ['blog','work','archive'];
const filteredTags = tags.filter(tag => !internalTagSlugs.includes(tag.slug.toLowerCase()));
---

<article class={`project-card ${isFeatured ? 'featured' : 'standard'}`}>
  <a href={`/work/${project.slug}/`} class="project-link">
    <div class="project-content">
      <div class="project-header">
        {formattedDate && (
          <time class="project-date">{formattedDate}</time>
        )}
        {status && (
          <span class={`project-status status-${status.toLowerCase()}`}>
            {status}
          </span>
        )}
      </div>
      
      <h2 class={`project-title ${isFeatured ? 'featured-title' : ''}`}>
        {title}
      </h2>
      
      {description && (
        <p class="project-description">{description}</p>
      )}
      
      {filteredTags.length > 0 && (
        <div class="project-tags">
          {filteredTags.map((tag) => (
            <Tag tag={tag.slug} tagName={tag.name} />
          ))}
        </div>
      )}
      
      {(repoUrl || liveUrl) && (
        <div class="project-actions">
          {repoUrl && (
            <span class="project-action-link">
              <span class="action-label">Repository</span>
              <span class="action-icon">↗</span>
            </span>
          )}
          {liveUrl && (
            <span class="project-action-link">
              <span class="action-label">Live Demo</span>
              <span class="action-icon">↗</span>
            </span>
          )}
        </div>
      )}
    </div>
  </a>
</article>

<style>
  .project-card {
    opacity: 0;
    animation: fade-in-card 0.5s ease-out forwards;
    margin: 1.5rem 0;
    background-color: rgba(20, 20, 20, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 0.75rem;
    padding: 1.5rem;
    list-style: none;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease;
    position: relative;
  }

  .project-card.featured {
    background-color: rgba(20, 20, 20, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: 2rem;
    margin: 2rem 0;
  }

  .project-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .project-card.featured:hover {
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
  }

  .project-link {
    text-decoration: none;
    color: inherit;
    display: block;
    height: 100%;
  }

  .project-content {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .project-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
  }

  .project-date {
    font-size: 0.875rem;
    color: var(--theme-text-light, rgba(255, 255, 255, 0.7));
    font-weight: 500;
    font-family: var(--font-mono, 'SF Mono', 'Monaco', monospace);
    letter-spacing: -0.025em;
  }

  .project-status {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .status-active,
  .status-live {
    background-color: rgba(34, 197, 94, 0.2);
    color: rgb(34, 197, 94);
    border: 1px solid rgba(34, 197, 94, 0.3);
  }

  .status-development,
  .status-wip {
    background-color: rgba(234, 179, 8, 0.2);
    color: rgb(234, 179, 8);
    border: 1px solid rgba(234, 179, 8, 0.3);
  }

  .status-archived,
  .status-inactive {
    background-color: rgba(156, 163, 175, 0.2);
    color: rgb(156, 163, 175);
    border: 1px solid rgba(156, 163, 175, 0.3);
  }

  .project-title {
    font-size: 1.25rem;
    font-weight: 700;
    margin: 0 0 1rem 0;
    color: var(--theme-text, rgba(255, 255, 255, 0.95));
    line-height: 1.3;
    letter-spacing: -0.02em;
  }

  .featured-title {
    font-size: 1.5rem;
    margin-bottom: 1.25rem;
  }

  .project-description {
    color: var(--theme-text-light, rgba(255, 255, 255, 0.8));
    line-height: 1.6;
    margin: 0 0 1.5rem 0;
    flex-grow: 1;
    font-size: 1rem;
  }

  .project-card.featured .project-description {
    font-size: 1.125rem;
    margin-bottom: 2rem;
  }

  .project-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
  }

  .project-actions {
    display: flex;
    gap: 1rem;
    margin-top: auto;
    flex-wrap: wrap;
  }

  .project-action-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--theme-accent, rgba(255, 255, 255, 0.9));
    padding: 0.5rem 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.375rem;
    transition: all 0.2s ease-in-out;
    text-decoration: none;
  }

  .project-action-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
  }

  .action-label {
    letter-spacing: -0.01em;
  }

  .action-icon {
    font-size: 0.75rem;
    opacity: 0.8;
  }

  @keyframes fade-in-card {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @media (max-width: 768px) {
    .project-card {
      margin: 1rem 0;
      padding: 1.25rem;
    }

    .project-card.featured {
      padding: 1.5rem;
      margin: 1.5rem 0;
    }

    .project-title {
      font-size: 1.125rem;
    }

    .featured-title {
      font-size: 1.25rem;
    }

    .project-description {
      font-size: 0.9rem;
    }

    .project-card.featured .project-description {
      font-size: 1rem;
    }

    .project-actions {
      gap: 0.75rem;
    }

    .project-action-link {
      font-size: 0.8rem;
      padding: 0.4rem 0.8rem;
    }
  }
</style>
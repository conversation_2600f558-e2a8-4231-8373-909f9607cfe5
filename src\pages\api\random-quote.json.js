import fs from 'fs';
import path from 'path';

// Path to quotes JSON file
const QUOTES_PATH = path.resolve(process.cwd(), 'src/data/ghost/quotes.json');

// Fallback quotes in case the JSON file fails to load
const fallbackQuotes = [
  {
    text: "Many mistake stability for safety, but only the dead remain still.",
    author: "Pruthvi Bhat",
    linkedPage: "/blog/stability-vs-safety",
    cardTitle: "Core Philosophy",
    cardSubtitle: "Exploring the need for change"
  },
  {
    text: "The purpose of knowledge is action, not more knowledge.",
    author: "Pruthvi Bhat",
    linkedPage: "/blog/knowledge-and-action",
    cardTitle: "Applied Learning",
    cardSubtitle: "Insights into meaningful action"
  },
  {
    text: "Silence is not empty, it's full of answers.",
    author: "Unknown",
    linkedPage: "/blog/power-of-silence",
    cardTitle: "Mindfulness",
    cardSubtitle: "Finding clarity in quiet"
  }
];

export const prerender = false;

export async function GET({ request }) {
  try {
    // Read quotes from the JSON file
    const quotesData = fs.readFileSync(QUOTES_PATH, 'utf8');
    const allQuotes = JSON.parse(quotesData);

    // If we have quotes, select a random one
    if (allQuotes && allQuotes.length > 0) {
      const randomIndex = Math.floor(Math.random() * allQuotes.length);
      const randomQuote = allQuotes[randomIndex];

      return new Response(
        JSON.stringify({
          text: randomQuote.text,
          author: randomQuote.author,
          linkedPage: randomQuote.linkedPage || null,
          cardTitle: randomQuote.cardTitle,
          cardSubtitle: randomQuote.cardSubtitle
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
    }

    // If no quotes found, use fallback
    console.warn('No quotes found in JSON file, using fallback');
    const randomIndex = Math.floor(Math.random() * fallbackQuotes.length);
    return new Response(
      JSON.stringify(fallbackQuotes[randomIndex]),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  } catch (error) {
    console.error('Error reading quotes from JSON file:', error);

    // Return a fallback quote
    const randomIndex = Math.floor(Math.random() * fallbackQuotes.length);
    return new Response(
      JSON.stringify(fallbackQuotes[randomIndex]),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}

import type { CollectionEntry } from "astro:content";
import { slugifyStr } from "./slugify";

// Change to default export
export default function getUniqueTags(posts: CollectionEntry<"blog">[]) {
  let tags: string[] = [];
  
  posts.forEach(post => {
    if (post.data.tags) {
      tags = [...tags, ...post.data.tags];
    }
  });
  
  const uniqueTags = [...new Set(tags)];
  
  return uniqueTags.map(tag => ({
    tag: slugifyStr(tag),
    tagName: tag,
  }));
}
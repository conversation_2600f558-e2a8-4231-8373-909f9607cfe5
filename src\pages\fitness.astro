---
import Layout from "../layouts/Layout.astro";

// Define transformation images array 
const transformationImages = [
  { year: 2017, src: "/images/fit2017.jpg", alt: "Fitness transformation 2017" },
  { year: 2018, src: "/images/fit2018.jpg", alt: "Fitness transformation 2018" },
  { year: 2019, src: "/images/fit2019.jpg", alt: "Fitness transformation 2019" },
  { year: 2020, src: "/images/fit2020.jpeg", alt: "Fitness transformation 2020" },
  { year: 2021, src: "/images/fit2021.jpeg", alt: "Fitness transformation 2021" },
  { year: 2022, src: "/images/fit2022.2.jpeg", alt: "Fitness transformation 2022" },
  { year: 2023, src: "/images/fit2023.1.jpeg", alt: "Fitness transformation 2023" },
  { year: 2024, src: "/images/fit2024.1.jpeg", alt: "Fitness transformation 2024" },
  { year: 2025, src: "/images/fit2025.png", alt: "Fitness transformation 2025" }
];

// Performance data
const performanceData = {
  personalRecords: [
    { exercise: "Bench Press", weight: "120kg", note: "3-rep max" },
    { exercise: "Bulgarian Split Squat", weight: "60kg", note: "each leg" },
    { exercise: "Barbell Row", weight: "115kg", note: "5-rep max" }
  ],
  statistics: [
    { label: "Years Training", value: "7+" },
    { label: "Total Sessions", value: "4,500+" },
    { label: "Weekly Frequency", value: "4.8" },
    { label: "Total Hours", value: "9,000+" }
  ]
};

// Philosophy sections
const philosophySections = [
  {
    title: "Consistent Progression",
    content: "Physical development occurs not through sporadic intensity but through sustained, incremental progress. My approach centers on data-driven progression: each workout builds upon the previous, with meticulously tracked performance guiding adjustments to stimulate continued adaptation."
  },
  {
    title: "Empirical Methodology",
    content: "Every repetition, every set, every session exists as a data point in a seven-year experiment. This comprehensive record reveals patterns in recovery, adaptation, and performance that inform my training protocol—transforming fitness from subjective effort into an objective practice."
  },
  {
    title: "Recovery Architecture",
    content: "Training creates the stimulus; recovery enables the transformation. I've systematically optimized sleep quality, nutritional timing, and stress management to create the physiological conditions where adaptation can occur without accumulating systemic fatigue or reaching plateaus."
  }
];

// Workout routines
const workoutRoutines = [
  { 
    name: "Chest & Shoulders", 
    href: "https://hevy.com/routine/oD9TKyY25F7" 
  },
  { 
    name: "Back", 
    href: "https://hevy.com/routine/PSvJCDxdTAS" 
  },
  { 
    name: "Arms", 
    href: "https://hevy.com/routine/FBu1xtqJsaW" 
  },
  { 
    name: "Legs", 
    href: "https://hevy.com/routine/X9WG47EEhWT" 
  }
];
---

<Layout
  pageTitle="Physical Practice | PVB"
  isHomePage={false}
  accentColor="#1a1a1a"
  bgColor="rgba(235, 230, 225, 0.85)"
  backgroundImageUrl="/images/emperadormarble.png"
  bodyDataPage="fitness"
>
  <!-- Google Fonts for Cinzel and Crimson Pro -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@600;700&family=Crimson+Pro:wght@400;500&display=swap" rel="stylesheet">

  <div class="fitness-header">
    <h1 class="fitness-title">PHYSICAL PRACTICE</h1>
  </div>

  <div class="content-container">
    <!-- Transformation Showcase - Immediate Visual Impact -->
    <section class="transformation-section" data-animate="fade-in">
      <div class="section-header">
        <div class="section-line"></div>
        <h2 class="section-title">TRANSFORMATION</h2>
        <div class="section-line"></div>
      </div>
      
      <div class="transformation-gallery">
        <div class="current-image-container">
          <div class="image-controls">
            <button class="control-button prev-button" id="prev-year">
              <span class="control-arrow">←</span>
            </button>
            <div class="year-display" id="current-year">2017</div>
            <button class="control-button next-button" id="next-year">
              <span class="control-arrow">→</span>
            </button>
          </div>
          
          <div class="current-image">
            <!-- Images will transition here via JS -->
            {transformationImages.map((image, index) => (
              <img
                src={image.src}
                alt={image.alt}
                class={`transformation-img ${index === 0 ? 'active' : ''}`}
                data-year={image.year}
                data-index={index}
              />
            ))}
          </div>
        </div>
        
        <div class="year-timeline">
          {transformationImages.map((image, index) => (
            <button 
              class={`year-marker ${index === 0 ? "active" : ""}`} 
              data-index={index}
              data-year={image.year}
            >
              <span class="year-dot"></span>
              <span class="year-label">{image.year}</span>
            </button>
          ))}
        </div>
      </div>
    </section>
    
    <hr class="section-divider">
    
    <!-- Stats Component - Key Metrics -->
    <section class="stats-section" data-animate="fade-in">
      <div class="section-header">
        <div class="section-line"></div>
        <h2 class="section-title">PERFORMANCE</h2>
        <div class="section-line"></div>
      </div>
      
      <div class="stats-container">
        <div class="stats-column records-column">
          <h3 class="stats-heading">PERSONAL RECORDS</h3>
          <div class="records-list">
            {performanceData.personalRecords.map(record => (
              <div class="record-item">
                <div class="record-exercise">{record.exercise}</div>
                <div class="record-weight">{record.weight}</div>
                <div class="record-note">{record.note}</div>
              </div>
            ))}
          </div>
        </div>
        
        <div class="stats-column metrics-column">
          <h3 class="stats-heading">TRAINING VOLUME</h3>
          <div class="metrics-grid">
            {performanceData.statistics.map(stat => (
              <div class="metric-item">
                <div class="metric-value">{stat.value}</div>
                <div class="metric-label">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
    
    <hr class="section-divider">

    <!-- Philosophy Section -->
    <section class="philosophy-section" data-animate="fade-in">
      <div class="section-header">
        <div class="section-line"></div>
        <h2 class="section-title">PHILOSOPHY</h2>
        <div class="section-line"></div>
      </div>

      <p class="intro-text">
        Since 2017, I've approached physical training as an empirical practice—a deliberate application 
        of progressive overload principles documented through extensive data collection. The transformation 
        above represents not sporadic effort but systematic application of principles.
      </p>
      
      <div class="philosophy-content">
        {philosophySections.map(section => (
          <div class="philosophy-item">
            <h3 class="philosophy-title">{section.title}</h3>
            <p class="philosophy-text">{section.content}</p>
          </div>
        ))}
      </div>
    </section>
    
    <hr class="section-divider">

    <!-- Training System Section -->
    <section class="routines-section" data-animate="fade-in">
      <div class="section-header">
        <div class="section-line"></div>
        <h2 class="section-title">METHODOLOGY</h2>
        <div class="section-line"></div>
      </div>
      
      <div class="routines-content">
        <p class="routines-intro">
          My current training protocol follows a four-day rotation targeting specific movement patterns 
          and muscle groups. Each protocol is meticulously documented and progressively adapted based 
          on performance data.
        </p>
        
        <div class="routines-grid">
          {workoutRoutines.map(routine => (
            <a href={routine.href} class="routine-link" target="_blank" rel="noopener noreferrer">
              <div class="routine-name">{routine.name}</div>
              <div class="routine-arrow">→</div>
            </a>
          ))}
        </div>
        
        <div class="hevy-link-container">
          <a href="https://hevy.com/user/approxfit" class="hevy-link" target="_blank" rel="noopener noreferrer">
            <span class="hevy-link-text">VIEW COMPLETE TRAINING ARCHIVE</span>
            <span class="hevy-link-arrow">→</span>
          </a>
        </div>
      </div>
    </section>
  </div>
</Layout>

<script>
  // Parallax effect for marble background
  document.addEventListener('DOMContentLoaded', function() {
    window.addEventListener('scroll', function() {
      const scrollPosition = window.scrollY;
      document.body.style.backgroundPosition = `center ${scrollPosition * 0.02}px`;
    });
    
    // Intersection Observer for fade-in animations
    const animatedSections = document.querySelectorAll('[data-animate="fade-in"]');
    
    // Create observer
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('visible');
          observer.unobserve(entry.target); // Stop observing once it's visible
        }
      });
    }, {
      root: null,
      rootMargin: '0px',
      threshold: 0.15
    });
    
    // Observe all sections with fade-in animation
    animatedSections.forEach(section => {
      observer.observe(section);
    });
    
    // Gallery transformation functionality
    const transformationImages = document.querySelectorAll('.transformation-img');
    const yearMarkers = document.querySelectorAll('.year-marker');
    const prevYearBtn = document.getElementById('prev-year');
    const nextYearBtn = document.getElementById('next-year');
    const currentYearDisplay = document.getElementById('current-year');
    
    if (!transformationImages.length || !yearMarkers.length) return;
    
    let currentIndex = 0;
    const totalImages = transformationImages.length;
    
    // Function to update the active image
    function updateActiveImage(newIndex) {
      // Check bounds
      if (newIndex < 0) newIndex = 0;
      if (newIndex >= totalImages) newIndex = totalImages - 1;
      
      // Update active index
      currentIndex = newIndex;
      
      // Update image visibility
      transformationImages.forEach((img, index) => {
        if (index === currentIndex) {
          img.classList.add('active');
          img.style.opacity = 1;
        } else {
          img.classList.remove('active');
          img.style.opacity = 0;
        }
      });
      
      // Update year markers
      yearMarkers.forEach((marker, index) => {
        marker.classList.toggle('active', index === currentIndex);
      });
      
      // Update current year display
      if (currentYearDisplay) {
        currentYearDisplay.textContent = transformationImages[currentIndex].dataset.year;
      }
    }
    
    // Add click event to year markers
    yearMarkers.forEach((marker, index) => {
      marker.addEventListener('click', () => {
        updateActiveImage(index);
      });
    });
    
    // Add click events to prev/next buttons
    if (prevYearBtn) {
      prevYearBtn.addEventListener('click', () => {
        updateActiveImage(currentIndex - 1);
      });
    }
    
    if (nextYearBtn) {
      nextYearBtn.addEventListener('click', () => {
        updateActiveImage(currentIndex + 1);
      });
    }
    
    // Touch swipe functionality for mobile
    let touchStartX = 0;
    let touchEndX = 0;
    
    const currentImageContainer = document.querySelector('.current-image');
    if (currentImageContainer) {
      currentImageContainer.addEventListener('touchstart', (e) => {
        touchStartX = e.changedTouches[0].screenX;
      });
      
      currentImageContainer.addEventListener('touchend', (e) => {
        touchEndX = e.changedTouches[0].screenX;
        handleSwipe();
      });
    }
    
    function handleSwipe() {
      const swipeThreshold = 50; // Minimum distance to register as swipe
      
      if (touchEndX < touchStartX - swipeThreshold) {
        // Swipe left - next image
        updateActiveImage(currentIndex + 1);
      } else if (touchEndX > touchStartX + swipeThreshold) {
        // Swipe right - previous image
        updateActiveImage(currentIndex - 1);
      }
    }
    
    // Auto-rotate images every 5 seconds if no interaction
    let autoRotateTimer;
    
    function startAutoRotate() {
      autoRotateTimer = setInterval(() => {
        let nextIndex = (currentIndex + 1) % totalImages;
        updateActiveImage(nextIndex);
      }, 5000);
    }
    
    function stopAutoRotate() {
      clearInterval(autoRotateTimer);
    }
    
    // Start auto-rotation
    startAutoRotate();
    
    // Pause on hover/interaction
    const galleryContainer = document.querySelector('.transformation-gallery');
    if (galleryContainer) {
      galleryContainer.addEventListener('mouseenter', stopAutoRotate);
      galleryContainer.addEventListener('touchstart', stopAutoRotate);
      galleryContainer.addEventListener('mouseleave', startAutoRotate);
      galleryContainer.addEventListener('touchend', startAutoRotate);
    }
  });
</script>

<style>
  /* === Typography & Font Setup === */
  @import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@600;700&family=Crimson+Pro:wght@400;500&display=swap');
  
  /* Page Fundamentals */
  :global(body[data-page="fitness"]) {
    overflow-y: auto;
    overflow-x: hidden;
    height: auto;
    min-height: 100vh;
    background-attachment: fixed; /* For parallax effect */
    background-size: cover; /* Ensure background covers the whole viewport */
    color: #1a1a1a;
  }
  
  /* All headings engraved effect */
  h1, h2, h3, h4 {
    font-family: 'Cinzel', serif;
    font-weight: 700;
    letter-spacing: 0.05em;
    text-shadow: 0 1px 1px rgba(0,0,0,0.25);
    color: #1a1a1a;
    margin-bottom: 2.5rem;
  }
  
  /* Body text */
  p, .metric-label, .year-label, .record-note {
    font-family: 'Crimson Pro', serif;
    font-weight: 400;
    color: rgba(0,0,0,0.85);
    line-height: 1.4;
    letter-spacing: -0.01em;
  }
  
  /* Section dividers */
  .section-divider {
    border: none;
    height: 1px;
    background: rgba(0,0,0,0.15);
    margin: 3rem 0;
  }
  
  /* Fade-in animation for sections */
  .transformation-section,
  .stats-section,
  .philosophy-section,
  .routines-section {
    opacity: 0;
    transform: translateY(15px);
    transition: opacity 1s ease, transform 1s ease;
    animation: fadeIn 1s forwards;
  }
  
  @keyframes fadeIn {
    0% {
      opacity: 0;
      transform: translateY(15px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* When section becomes visible */
  .transformation-section.visible,
  .stats-section.visible,
  .philosophy-section.visible,
  .routines-section.visible {
    opacity: 1;
    transform: translateY(0);
  }
  
  /* === Header Section === */
  .fitness-header {
    width: 100%;
    text-align: center;
    margin: 55px 0 40px;
    position: relative;
  }

  .fitness-title {
    font-size: 2.2rem;
    display: inline-block;
    margin: 0;
    text-transform: uppercase;
  }
  
  /* Subtle underline with engraved appearance */
  .fitness-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 2px;
    background-color: rgba(0, 0, 0, 0.4);
  }

  /* === Content Container === */
  .content-container {
    max-width: 800px;
    margin: 0 auto 80px;
    padding: 0 25px;
  }
  
  /* === Section Headers === */
  .section-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin: 50px 0 35px;
  }

  .section-line {
    flex-grow: 1;
    height: 1px;
    background-color: rgba(0, 0, 0, 0.2);
    transition: background-color 0.4s cubic-bezier(0.25, 0.1, 0.25, 1);
  }
  
  .section-title {
    font-size: 1.1rem;
    padding: 0 0.8rem;
    margin: 0;
    text-transform: uppercase;
  }
  
  /* === Transformation Gallery === */
  .transformation-gallery {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 auto;
    max-width: 500px;
  }
  
  .current-image-container {
    position: relative;
    width: 100%;
    margin-bottom: 20px;
  }
  
  .image-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding: 0 5px;
  }
  
  .control-button {
    background: rgba(255, 255, 255, 0.5);
    border: 1px solid rgba(0, 0, 0, 0.15);
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0px 2px 4px rgba(0,0,0,0.15);
  }
  
  .control-button:hover {
    background-color: rgba(255, 255, 255, 0.7);
    border-color: rgba(0, 0, 0, 0.25);
  }
  
  .control-arrow {
    color: rgba(0, 0, 0, 0.7);
    font-size: 0.9rem;
  }
  
  .year-display {
    font-family: 'Cinzel', serif;
    font-size: 1.2rem;
    font-weight: 600;
    color: #1a1a1a;
    letter-spacing: 0.05em;
  }
  
  .current-image {
    position: relative;
    width: 100%;
    height: 480px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    overflow: hidden;
  }
  
  .transformation-img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    opacity: 0;
    transition: opacity 0.8s ease;
  }
  
  .transformation-img.active {
    opacity: 1;
  }
  
  .year-timeline {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-top: 20px;
    position: relative;
  }
  
  /* Timeline connecting line */
  .year-timeline::before {
    content: '';
    position: absolute;
    top: 10px;
    left: 0;
    right: 0;
    height: 1px;
    background-color: rgba(0, 0, 0, 0.15);
    z-index: 0;
  }
  
  .year-marker {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    position: relative;
    z-index: 1;
    transition: transform 0.3s ease;
  }
  
  .year-marker:hover {
    transform: translateY(-2px);
  }
  
  .year-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.3);
    margin-bottom: 7px;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.0);
  }
  
  .year-marker.active .year-dot {
    background-color: #000000;
    width: 12px;
    height: 12px;
    border: 1px solid rgba(0, 0, 0, 0.1);
  }
  
  .year-marker:hover .year-dot {
    background-color: rgba(0, 0, 0, 0.6);
  }
  
  .year-label {
    font-family: 'Cinzel', serif;
    font-size: 0.8rem;
    color: rgba(0, 0, 0, 0.6);
    transition: color 0.3s ease;
    letter-spacing: 0.03em;
  }
  
  .year-marker.active .year-label {
    font-weight: 600;
    color: #000000;
  }
  
  .year-marker:hover .year-label {
    color: rgba(0, 0, 0, 0.85);
  }
  
  /* === Stats Component === */
  .stats-container {
    display: flex;
    flex-direction: column;
    gap: 40px;
  }
  
  .stats-column {
    width: 100%;
  }
  
  .stats-heading {
    font-size: 1.2rem;
    margin: 0 0 25px;
    text-align: center;
    letter-spacing: 0.05em;
    text-transform: uppercase;
  }
  
  /* Personal Records */
  .records-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }
  
  .record-item {
    display: flex;
    align-items: baseline;
    justify-content: space-between;
    padding: 0.6rem 1rem;
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.5);
    box-shadow: 0px 2px 4px rgba(0,0,0,0.15);
  }
  
  .record-exercise {
    font-family: 'Cinzel', serif;
    font-size: 1rem;
    font-weight: 600;
    color: #1a1a1a;
    letter-spacing: 0.03em;
  }
  
  .record-weight {
    font-family: 'Cinzel', serif;
    font-size: 1.05rem;
    font-weight: 700;
    color: #1a1a1a;
    text-align: right;
    letter-spacing: 0.03em;
  }
  
  .record-note {
    font-size: 0.8rem;
    color: rgba(0, 0, 0, 0.6);
    margin-top: 3px;
  }
  
  /* Training Metrics */
  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
  
  .metric-item {
    text-align: center;
    padding: 0.6rem 1rem;
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.5);
    box-shadow: 0px 2px 4px rgba(0,0,0,0.15);
    transition: transform 0.3s ease;
  }
  
  .metric-item:hover {
    transform: translateY(-3px);
  }
  
  .metric-value {
    font-family: 'Cinzel', serif;
    font-size: 1.3rem;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 7px;
    letter-spacing: 0.03em;
  }
  
  .metric-label {
    font-size: 0.9rem;
    color: rgba(0, 0, 0, 0.7);
  }
  
  /* === Philosophy Section === */
  .intro-text {
    font-size: 1.15rem;
    line-height: 1.4;
    color: rgba(0, 0, 0, 0.85);
    margin-bottom: 40px;
  }
  
  .philosophy-content {
    display: flex;
    flex-direction: column;
    gap: 35px;
  }
  
  .philosophy-item {
    padding: 0.6rem 1rem;
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.5);
    box-shadow: 0px 2px 4px rgba(0,0,0,0.15);
  }
  
  .philosophy-title {
    font-size: 1.2rem;
    margin: 0 0 15px;
    color: #1a1a1a;
    letter-spacing: 0.05em;
  }
  
  .philosophy-text {
    font-size: 1rem;
    line-height: 1.4;
    color: rgba(0, 0, 0, 0.8);
    margin: 0;
  }
  
  /* === Routines Section === */
  .routines-intro {
    font-size: 1.05rem;
    line-height: 1.4;
    color: rgba(0, 0, 0, 0.8);
    margin-bottom: 35px;
  }
  
  .routines-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-bottom: 40px;
  }
  
  .routine-link {
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-decoration: none;
    padding: 0.6rem 1rem;
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.5);
    box-shadow: 0px 2px 4px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
  }
  
  .routine-link:hover {
    background-color: rgba(255, 255, 255, 0.7);
    transform: translateY(-3px);
    box-shadow: 0px 4px 8px rgba(0,0,0,0.2);
  }
  
  .routine-name {
    font-family: 'Cinzel', serif;
    font-size: 1rem;
    font-weight: 600;
    color: #1a1a1a;
    letter-spacing: 0.03em;
  }
  
  .routine-arrow {
    font-size: 1rem;
    color: rgba(0, 0, 0, 0.5);
    transition: transform 0.3s ease;
  }
  
  .routine-link:hover .routine-arrow {
    transform: translateX(3px);
    color: #1a1a1a;
  }
  
  /* === Hevy Link === */
  .hevy-link-container {
    text-align: center;
    margin-top: 30px;
  }
  
  .hevy-link {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
    padding: 0.6rem 1.5rem;
    background-color: rgba(0, 0, 0, 0.85);
    color: #ffffff;
    font-family: 'Cinzel', serif;
    font-weight: 600;
    letter-spacing: 0.05em;
    border-radius: 15px;
    transition: all 0.3s ease;
    box-shadow: 0px 2px 4px rgba(0,0,0,0.15);
  }
  
  .hevy-link:hover {
    background-color: #000000;
    transform: translateY(-3px);
    box-shadow: 0px 4px 12px rgba(0,0,0,0.25);
  }
  
  .hevy-link-text {
    font-size: 0.9rem;
  }
  
  .hevy-link-arrow {
    font-size: 0.9rem;
    transition: transform 0.3s ease;
  }
  
  .hevy-link:hover .hevy-link-arrow {
    transform: translateX(3px);
  }
  
  /* === Mobile-first responsive styles === */
  @media (max-width: 480px) {
    .fitness-header {
      margin: 45px 0 30px;
    }
    
    .fitness-title {
      font-size: 1.6rem;
    }
    
    .section-title {
      font-size: 0.9rem;
    }
    
    .current-image {
      height: 400px;
    }
    
    .year-timeline {
      padding: 0 10px;
    }
    
    .year-dot {
      width: 6px;
      height: 6px;
    }
    
    .year-marker.active .year-dot {
      width: 10px;
      height: 10px;
    }
    
    .year-label {
      font-size: 0.7rem;
    }
    
    .metrics-grid {
      grid-template-columns: 1fr;
      gap: 15px;
    }
    
    .routines-grid {
      grid-template-columns: 1fr;
    }
  }
  
  /* === Tablet responsive styles === */
  @media (min-width: 481px) and (max-width: 768px) {
    .content-container {
      max-width: 90%;
    }
  }
  
  /* === Larger screens === */
  @media (min-width: 769px) {
    .stats-container {
      flex-direction: row;
      gap: 40px;
    }
    
    .stats-column {
      width: calc(50% - 20px);
    }
  }
</style>
